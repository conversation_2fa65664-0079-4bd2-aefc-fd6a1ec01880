{"name": "xact-data", "version": "1.0.0", "description": "The ultimate operating system for TikTok Shop creators", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "pnpm run --parallel dev", "build": "pnpm build:packages && pnpm run --filter \"./apps/*\" build", "build:backend": "pnpm build:packages && pnpm --filter api build && pnpm --filter worker build", "lint": "pnpm run --recursive lint", "format": "prettier --write .", "type-check": "pnpm run --recursive type-check", "clean": "pnpm run --recursive clean", "setup": "./scripts/setup.sh", "build:packages": "pnpm --filter @xact-data/database build && pnpm --filter @xact-data/ai-wrapper build && pnpm --filter @xact-data/shared build", "test": "echo 'Tests not yet implemented'", "db:generate": "pnpm --filter @xact-data/database db:generate", "db:push": "pnpm --filter @xact-data/database db:push", "db:seed": "pnpm --filter @xact-data/database db:seed", "db:studio": "pnpm --filter @xact-data/database db:studio"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.1.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.6.0", "dependencies": {"axios": "^1.12.2", "dotenv": "^16.3.1"}}