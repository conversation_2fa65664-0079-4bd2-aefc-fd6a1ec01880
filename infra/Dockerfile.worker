# Multi-stage build for Worker
FROM node:18-alpine AS base

# Install pnpm
RUN corepack enable

FROM base AS dependencies

WORKDIR /app
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY packages/shared/package.json ./packages/shared/
COPY packages/database/package.json ./packages/database/
COPY packages/ai-wrapper/package.json ./packages/ai-wrapper/
COPY apps/worker/package.json ./apps/worker/

RUN pnpm install --frozen-lockfile

FROM base AS build

WORKDIR /app
COPY . .
COPY --from=dependencies /app/node_modules ./node_modules

# Set Supabase DATABASE_URL for Prisma generation
ENV DATABASE_URL="*******************************************************************************/postgres?sslmode=require"

# Generate Prisma client first
RUN pnpm --filter @xact-data/database db:generate

# Build Worker and all dependencies
RUN pnpm --filter worker build

FROM base AS runtime

WORKDIR /app

# Copy built applications
COPY --from=build /app/apps/worker/dist ./apps/worker/dist
COPY --from=build /app/packages/shared/dist ./packages/shared/dist
COPY --from=build /app/packages/database/dist ./packages/database/dist
COPY --from=build /app/packages/ai-wrapper/dist ./packages/ai-wrapper/dist

# Copy package files
COPY --from=build /app/package.json ./
COPY --from=build /app/pnpm-workspace.yaml ./
COPY --from=build /app/apps/worker/package.json ./apps/worker/
COPY --from=build /app/packages/shared/package.json ./packages/shared/
COPY --from=build /app/packages/database/package.json ./packages/database/
COPY --from=build /app/packages/ai-wrapper/package.json ./packages/ai-wrapper/

# Copy Prisma files
COPY --from=build /app/packages/database/prisma ./packages/database/prisma
COPY --from=build /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=build /app/node_modules/@prisma ./node_modules/@prisma

# Install production dependencies
RUN pnpm install --prod --frozen-lockfile

EXPOSE 8080

CMD ["node", "apps/worker/dist/index.js"]