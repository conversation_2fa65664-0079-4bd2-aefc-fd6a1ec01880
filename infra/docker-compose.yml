version: '3.8'

services:
  # Redis (for future caching/sessions)
  redis:
    image: redis:7-alpine
    container_name: xact-data-redis
    ports:
      - "6379:6379"
    networks:
      - xact-data-network

  # API Server
  api:
    build:
      context: ../
      dockerfile: infra/Dockerfile.api
    container_name: xact-data-api
    environment:
      NODE_ENV: production
      PORT: 8080
      DATABASE_URL: *******************************************************************************/postgres?sslmode=require
      NEXT_PUBLIC_SUPABASE_URL: https://yufcxcwnwmlcykqpugkl.supabase.co
      NEXT_PUBLIC_SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl1ZmN4Y3dud21sY3lrcXB1Z2tsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgzNTI0MjMsImV4cCI6MjA3MzkyODQyM30.JvQ9PgX1I5LABhJN0BJBO0W_GVZb4Nn3vSopKmAUGFM
      GEMINI_API_KEY: AIzaSyAij22a341Hlw_DMlF5MRI-caOIni065xo
    ports:
      - "8080:8080"
    depends_on:
      - redis
    networks:
      - xact-data-network
    restart: unless-stopped

  # Worker
  worker:
    build:
      context: ../
      dockerfile: infra/Dockerfile.worker
    container_name: xact-data-worker
    environment:
      NODE_ENV: production
      PORT: 8081
      DATABASE_URL: *******************************************************************************/postgres?sslmode=require
      NEXT_PUBLIC_SUPABASE_URL: https://yufcxcwnwmlcykqpugkl.supabase.co
      NEXT_PUBLIC_SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl1ZmN4Y3dud21sY3lrcXB1Z2tsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTgzNTI0MjMsImV4cCI6MjA3MzkyODQyM30.JvQ9PgX1I5LABhJN0BJBO0W_GVZb4Nn3vSopKmAUGFM
      GEMINI_API_KEY: AIzaSyAij22a341Hlw_DMlF5MRI-caOIni065xo
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - xact-data-network
    restart: unless-stopped

  # Web Frontend
  web:
    build:
      context: ../
      dockerfile: infra/Dockerfile.web
    container_name: xact-data-web
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: https://api-production-7bd1.up.railway.app
      NEXTAUTH_URL: https://web-production-611c4.up.railway.app/
      NEXTAUTH_SECRET: ikB1vqfrh2iFl7WZkSpn2iFfluA02aiXy_C_o2TsIi8
      WHOP_CLIENT_ID: app_DA2C9XoK7mRye9
      WHOP_CLIENT_SECRET: xWhxsuZSsC4OsI8PBURSJORXMAVd2n3jC_3xbDgZU6E
    ports:
      - "3000:3000"
    depends_on:
      - api
    networks:
      - xact-data-network
    restart: unless-stopped

# No volumes needed - using external Supabase database

networks:
  xact-data-network:
    driver: bridge
