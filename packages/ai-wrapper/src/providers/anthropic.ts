import Anthropic from '@anthropic-ai/sdk';
import { ChatMessage, AIResponse } from '../types';

export class AnthropicProvider {
  private client: Anthropic;
  private model: string;
  private maxTokens: number;
  private temperature?: number;

  constructor(
    apiKey: string,
    model = 'claude-3-sonnet-20240229',
    maxTokens = 4000,
    temperature?: number
  ) {
    this.client = new Anthropic({ apiKey });
    this.model = model;
    this.maxTokens = maxTokens;
    this.temperature = temperature;
  }

  async chat(messages: ChatMessage[]): Promise<AIResponse> {
    // Anthropic expects system messages to be separate
    const systemMessage = messages.find(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    const response = await this.client.messages.create({
      model: this.model,
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      })),
    });

    const content = response.content[0];
    if (content.type !== 'text') {
      throw new Error('Unexpected response type from Anthropic');
    }

    return {
      content: content.text,
      usage: {
        promptTokens: response.usage.input_tokens,
        completionTokens: response.usage.output_tokens,
        totalTokens: response.usage.input_tokens + response.usage.output_tokens,
      },
    };
  }

  async generateInsight(prompt: string): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a strategic TikTok Shop growth consultant helping creators dominate their competition. Write insights in a conversational, human tone as if you\'re personally coaching them. Focus on actionable strategies to outperform competitors and maximize revenue. Avoid robotic language, technical jargon, or generic advice. Be specific, direct, and motivational.',
      },
      {
        role: 'user',
        content: prompt,
      },
    ];

    const response = await this.chat(messages);
    return response.content;
  }

  async generateCompetitorAnalysis(
    creatorData: string,
    competitorData: string
  ): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a competitive intelligence expert helping creators crush their competition. Your job is to identify exactly how the user can outperform their competitor and steal market share. Write in a confident, strategic tone like a business mentor. Focus on what the USER needs to do to win, not how to improve the competitor. Be specific about tactics to gain competitive advantage.',
      },
      {
        role: 'user',
        content: `Help me dominate this competitor. Here's my data vs theirs:

MY PROFILE:
${creatorData}

COMPETITOR I WANT TO BEAT:
${competitorData}

Show me exactly how to:
1. Exploit their weaknesses to gain followers
2. Steal their audience with better content
3. Outrank them on products they're promoting
4. Capture market share they're missing
5. Build on my strengths to surpass them

Give me a battle plan to win.`,
      },
    ];

    const response = await this.chat(messages);
    return response.content;
  }
}