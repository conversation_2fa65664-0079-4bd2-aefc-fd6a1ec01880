import OpenAI from 'openai';
import { ChatMessage, AIResponse } from '../types';

export class OpenA<PERSON>rovider {
  private client: OpenAI;
  private model: string;
  private maxTokens?: number;
  private temperature?: number;

  constructor(
    apiKey: string,
    model = 'gpt-4',
    maxTokens?: number,
    temperature?: number
  ) {
    this.client = new OpenAI({ apiKey });
    this.model = model;
    this.maxTokens = maxTokens;
    this.temperature = temperature;
  }

  async chat(messages: ChatMessage[]): Promise<AIResponse> {
    const response = await this.client.chat.completions.create({
      model: this.model,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })),
      max_tokens: this.maxTokens,
      temperature: this.temperature,
    });

    const choice = response.choices[0];
    if (!choice.message.content) {
      throw new Error('No content in OpenAI response');
    }

    return {
      content: choice.message.content,
      usage: response.usage ? {
        promptTokens: response.usage.prompt_tokens,
        completionTokens: response.usage.completion_tokens,
        totalTokens: response.usage.total_tokens,
      } : undefined,
    };
  }

  async generateInsight(prompt: string): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a strategic TikTok Shop growth consultant helping creators dominate their competition. Write insights in a conversational, human tone as if you\'re personally coaching them. Focus on actionable strategies to outperform competitors and maximize revenue. Avoid robotic language, technical jargon, or generic advice. Be specific, direct, and motivational.',
      },
      {
        role: 'user',
        content: prompt,
      },
    ];

    const response = await this.chat(messages);
    return response.content;
  }

  async generateCompetitorAnalysis(
    creatorData: string,
    competitorData: string
  ): Promise<string> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'You are a competitive intelligence expert helping creators crush their competition. Your job is to identify exactly how the user can outperform their competitor and steal market share. Write in a confident, strategic tone like a business mentor. Focus on what the USER needs to do to win, not how to improve the competitor. Be specific about tactics to gain competitive advantage.',
      },
      {
        role: 'user',
        content: `Help me dominate this competitor. Here's my data vs theirs:

MY PROFILE:
${creatorData}

COMPETITOR I WANT TO BEAT:
${competitorData}

Show me exactly how to:
1. Exploit their weaknesses to gain followers
2. Steal their audience with better content
3. Outrank them on products they're promoting
4. Capture market share they're missing
5. Build on my strengths to surpass them

Give me a battle plan to win.`,
      },
    ];

    const response = await this.chat(messages);
    return response.content;
  }
}