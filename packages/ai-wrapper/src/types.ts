import { z } from 'zod';

export const AIProviderSchema = z.enum(['openai', 'anthropic', 'gemini']);
export type AIProvider = z.infer<typeof AIProviderSchema>;

export const ChatMessageSchema = z.object({
  role: z.enum(['system', 'user', 'assistant']),
  content: z.string(),
});

export type ChatMessage = z.infer<typeof ChatMessageSchema>;

export const AIConfigSchema = z.object({
  provider: AIProviderSchema,
  apiKey: z.string(),
  model: z.string().optional(),
  maxTokens: z.number().optional(),
  temperature: z.number().min(0).max(2).optional(),
});

export type AIConfig = z.infer<typeof AIConfigSchema>;

export const AIResponseSchema = z.object({
  content: z.string(),
  usage: z.object({
    promptTokens: z.number(),
    completionTokens: z.number(),
    totalTokens: z.number(),
  }).optional(),
});

export type AIResponse = z.infer<typeof AIResponseSchema>;