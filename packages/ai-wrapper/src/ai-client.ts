import { OpenAIProvider } from './providers/openai';
import { AnthropicProvider } from './providers/anthropic';
import { GeminiProvider } from './providers/gemini';
import { AIConfig, ChatMessage, AIResponse, AIProvider } from './types';

export class AIClient {
  private provider: OpenAIProvider | AnthropicProvider | GeminiProvider;
  private config: AIConfig;

  constructor(config: AIConfig) {
    this.config = config;
    
    switch (config.provider) {
      case 'openai':
        this.provider = new OpenAIProvider(
          config.apiKey,
          config.model,
          config.maxTokens,
          config.temperature
        );
        break;
      case 'anthropic':
        this.provider = new AnthropicProvider(
          config.apiKey,
          config.model,
          config.maxTokens,
          config.temperature
        );
        break;
      case 'gemini':
        this.provider = new GeminiProvider(
          config.apiKey,
          config.model,
          config.maxTokens,
          config.temperature
        );
        break;
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  async chat(messages: ChatMessage[]): Promise<AIResponse> {
    return this.provider.chat(messages);
  }

  async generateInsight(prompt: string): Promise<string> {
    return this.provider.generateInsight(prompt);
  }

  async generateCompetitorAnalysis(
    creatorData: string,
    competitorData: string
  ): Promise<string> {
    return this.provider.generateCompetitorAnalysis(creatorData, competitorData);
  }

  async generateProductRecommendations(
    userProfile: string,
    productData: string
  ): Promise<string> {
    const prompt = `Based on this user's profile and available products, recommend the top products that would maximize their revenue:

User Profile:
${userProfile}

Available Products:
${productData}

Please provide:
1. Top 5 product recommendations with reasoning
2. Expected revenue potential for each
3. Content strategy suggestions for each product
4. Risk factors to consider`;

    return this.generateInsight(prompt);
  }

  async generateGrowthCoachInsights(
    performanceData: string,
    goals: string
  ): Promise<string> {
    const prompt = `Act as a growth coach for a TikTok Shop creator. Based on their performance data and goals, provide actionable insights:

Performance Data:
${performanceData}

Goals:
${goals}

Please provide:
1. Current performance analysis
2. Gap analysis between current and target performance
3. Specific action items for the next 7 days
4. Weekly and monthly growth strategies
5. Key metrics to track`;

    return this.generateInsight(prompt);
  }

  getProvider(): AIProvider {
    return this.config.provider;
  }
}