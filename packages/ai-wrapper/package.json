{"name": "@xact-data/ai-wrapper", "version": "1.0.0", "description": "Provider-agnostic AI wrapper for OpenAI and Anthropic", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"openai": "^4.20.0", "@anthropic-ai/sdk": "^0.24.0", "@google/generative-ai": "^0.7.1", "zod": "^3.22.0"}, "devDependencies": {"typescript": "^5.1.0", "@types/node": "^20.0.0"}}