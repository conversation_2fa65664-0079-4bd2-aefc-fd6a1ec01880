{"name": "@xact-data/database", "version": "1.0.0", "description": "Database layer with Prisma for PostgreSQL", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "prisma generate && tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/seed.ts", "postinstall": "prisma generate"}, "dependencies": {"@prisma/client": "^6.16.2"}, "devDependencies": {"@types/node": "^20.0.0", "prisma": "^6.16.2", "tsx": "^4.0.0", "typescript": "^5.1.0"}}