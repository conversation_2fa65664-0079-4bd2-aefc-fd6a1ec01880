import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seed() {
  console.log('🌱 Seeding database...');

  // Create sample products
  const products = [
    {
      id: 'prod_1',
      title: 'Wireless Bluetooth Earbuds',
      category: 'Electronics',
      commissionRate: 15.5,
      soldIn24h: 1250,
      creatorsCarrying: 45,
      estimatedGMV: 75000,
      trendScore: 85.5,
      price: 59.99,
      imageUrl: 'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_1',
    },
    {
      id: 'prod_2',
      title: 'LED Strip Lights',
      category: 'Home & Garden',
      commissionRate: 12.0,
      soldIn24h: 890,
      creatorsCarrying: 32,
      estimatedGMV: 45000,
      trendScore: 72.3,
      price: 24.99,
      imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_2',
    },
    {
      id: 'prod_3',
      title: 'Skincare Set',
      category: 'Beauty',
      commissionRate: 20.0,
      soldIn24h: 2100,
      creatorsCarrying: 78,
      estimatedGMV: 125000,
      trendScore: 92.1,
      price: 89.99,
      imageUrl: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_3',
    },
    {
      id: 'prod_4',
      title: 'Fitness Resistance Bands',
      category: 'Sports & Outdoors',
      commissionRate: 18.0,
      soldIn24h: 650,
      creatorsCarrying: 28,
      estimatedGMV: 32000,
      trendScore: 68.7,
      price: 29.99,
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_4',
    },
    {
      id: 'prod_5',
      title: 'Smart Watch',
      category: 'Electronics',
      commissionRate: 22.0,
      soldIn24h: 1800,
      creatorsCarrying: 92,
      estimatedGMV: 180000,
      trendScore: 94.2,
      price: 199.99,
      imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_5',
    },
    {
      id: 'prod_6',
      title: 'Portable Phone Charger',
      category: 'Electronics',
      commissionRate: 14.5,
      soldIn24h: 920,
      creatorsCarrying: 41,
      estimatedGMV: 55000,
      trendScore: 76.8,
      price: 39.99,
      imageUrl: 'https://images.unsplash.com/photo-1609592094540-8b1f8e6e9d16?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_6',
    },
    {
      id: 'prod_7',
      title: 'Trendy Sunglasses',
      category: 'Fashion',
      commissionRate: 25.0,
      soldIn24h: 1450,
      creatorsCarrying: 67,
      estimatedGMV: 87000,
      trendScore: 81.4,
      price: 49.99,
      imageUrl: 'https://images.unsplash.com/photo-1511499767150-a48a237f0083?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_7',
    },
    {
      id: 'prod_8',
      title: 'Coffee Maker',
      category: 'Home & Garden',
      commissionRate: 16.0,
      soldIn24h: 380,
      creatorsCarrying: 19,
      estimatedGMV: 28500,
      trendScore: 45.2,
      price: 149.99,
      imageUrl: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_8',
    },
    {
      id: 'prod_9',
      title: 'Yoga Mat',
      category: 'Sports & Outdoors',
      commissionRate: 19.0,
      soldIn24h: 720,
      creatorsCarrying: 35,
      estimatedGMV: 36000,
      trendScore: 63.9,
      price: 34.99,
      imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_9',
    },
    {
      id: 'prod_10',
      title: 'Hair Styling Tool',
      category: 'Beauty',
      commissionRate: 23.0,
      soldIn24h: 1650,
      creatorsCarrying: 58,
      estimatedGMV: 99000,
      trendScore: 88.6,
      price: 79.99,
      imageUrl: 'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=200',
      affiliateLink: 'https://tiktokshop.com/affiliate/prod_10',
    },
  ];

  for (const product of products) {
    await prisma.product.upsert({
      where: { id: product.id },
      update: {},
      create: product,
    });
  }

  // Create sample creators
  const creators = [
    {
      id: 'creator_1',
      username: 'fashionista_jane',
      followerCount: 250000,
      averageViews: 45000,
      engagementRate: 8.5,
      totalGMV: 125000,
      profileImageUrl: 'https://example.com/jane.jpg',
    },
    {
      id: 'creator_2',
      username: 'tech_reviewer_mike',
      followerCount: 180000,
      averageViews: 32000,
      engagementRate: 6.8,
      totalGMV: 95000,
      profileImageUrl: 'https://example.com/mike.jpg',
    },
  ];

  for (const creator of creators) {
    await prisma.creator.upsert({
      where: { id: creator.id },
      update: {},
      create: creator,
    });
  }

  // Create sample creator-product relationships
  await prisma.creatorProduct.upsert({
    where: {
      creatorId_productId: {
        creatorId: 'creator_1',
        productId: 'prod_3',
      },
    },
    update: {},
    create: {
      creatorId: 'creator_1',
      productId: 'prod_3',
      gmv: 45000,
      sales: 500,
    },
  });

  await prisma.creatorProduct.upsert({
    where: {
      creatorId_productId: {
        creatorId: 'creator_2',
        productId: 'prod_1',
      },
    },
    update: {},
    create: {
      creatorId: 'creator_2',
      productId: 'prod_1',
      gmv: 35000,
      sales: 585,
    },
  });

  console.log('✅ Database seeded successfully!');
}

seed()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });