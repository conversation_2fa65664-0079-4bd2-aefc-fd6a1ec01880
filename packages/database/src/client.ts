import { PrismaClient } from '@prisma/client';

declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

export const prisma = globalThis.__prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? 
    ['query', 'error', 'warn'] : 
    ['error'],
  errorFormat: 'pretty',
});

// Enhanced connection test with better error handling
export async function testDatabaseConnection() {
  try {
    console.log('🔍 Attempting to connect to database...');
    
    // Test connection with timeout
    const connectPromise = prisma.$connect();
    const timeoutPromise = new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Connection timeout after 10 seconds')), 10000)
    );
    
    await Promise.race([connectPromise, timeoutPromise]);
    
    // Test basic query
    await prisma.$queryRaw`SELECT 1`;
    
    console.log('✅ Database connected successfully');
    return true;
  } catch (error: any) {
    console.error('❌ Database connection failed:');
    console.error('Error message:', error.message);
    
    // Enhanced error diagnostics
    if (error.code === 'P1001') {
      console.error('');
      console.error('🔍 Connection Error Details:');
      console.error('- Error Code: P1001 (Cannot reach database server)');
      console.error('- This indicates the database server is not accessible');
      console.error('');
      console.error('🚨 Possible Issues:');
      console.error('1. Database server is not running or is unreachable');
      console.error('2. Network connectivity issues');
      console.error('3. Incorrect host/port in DATABASE_URL');
      console.error('4. SSL/TLS configuration required');
      console.error('5. Firewall blocking the connection');
      console.error('');
      console.error('💡 Solutions to try:');
      console.error('1. Verify DATABASE_URL format: postgresql://user:pass@host:port/db');
      console.error('2. Try with SSL mode: add ?sslmode=require to DATABASE_URL');
      console.error('3. Check if using pooled connection (port 6543) is needed');
      console.error('4. Verify database service is active in your provider dashboard');
    } else if (error.message.includes('timeout')) {
      console.error('');
      console.error('⏱️ Connection Timeout:');
      console.error('- The database server is not responding within 10 seconds');
      console.error('- This usually indicates the server is unreachable or overloaded');
    }
    
    return false;
  }
}

// Graceful shutdown
export async function disconnectDatabase() {
  await prisma.$disconnect();
}

if (process.env.NODE_ENV !== 'production') {
  globalThis.__prisma = prisma;
}