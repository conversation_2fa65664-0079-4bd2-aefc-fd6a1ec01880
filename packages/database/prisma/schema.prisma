// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
  schemas   = ["public"]
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  name         String?
  image        String?
  whopId       String?  @unique @map("whop_id")
  tiktokShopId String?  @map("tiktok_shop_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")


  // TikTok Shop OAuth tokens
  tiktokShopAccessToken     String?   @map("tiktok_shop_access_token")
  tiktokShopRefreshToken    String?   @map("tiktok_shop_refresh_token")
  tiktokShopTokenExpiresAt  DateTime? @map("tiktok_shop_token_expires_at")
  tiktokShopRefreshExpiresAt DateTime? @map("tiktok_shop_refresh_expires_at")
  tiktokShopName            String?   @map("tiktok_shop_name")
  tiktokShopRegion          String?   @map("tiktok_shop_region")
  tiktokShopConnectedAt     DateTime? @map("tiktok_shop_connected_at")


  // Relations
  alerts     Alert[]
  analytics  Analytics[]
  alertFires AlertFire[]
  competitorTracking CompetitorTracking[]
  affiliateGoals AffiliateGoal[]
  achievements Achievement[]
  competitiveBenchmarks CompetitiveBenchmark[]
  aiInsights AIInsight[]
  productPerformance ProductPerformance[]
  shopPerformance ShopPerformance[]

  @@map("users")
  @@schema("public")
}

model Product {
  id               String   @id
  title            String
  category         String
  commissionRate   Float    @map("commission_rate")
  soldIn24h        Int      @map("sold_in_24h")
  creatorsCarrying Int      @map("creators_carrying")
  estimatedGMV     Float    @map("estimated_gmv")
  trendScore       Float    @map("trend_score")
  affiliateLink    String?  @map("affiliate_link")
  imageUrl         String?  @map("image_url")
  price            Float
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  
  // TikTok API specific fields
  tiktokProductId  String?  @map("tiktok_product_id")
  rank             Int?
  category1        String?  @map("category_1")
  category2        String?  @map("category_2")
  category3        String?  @map("category_3")
  countryCode      String?  @map("country_code")
  soldCount        Int?     @map("sold_count")
  totalSales       String?  @map("total_sales")
  weekSoldCount    Int?     @map("week_sold_count")
  weekSales        String?  @map("week_sales")
  productRating    Float?   @map("product_rating")
  sellerId         String?  @map("seller_id")
  sellerName       String?  @map("seller_name")
  shopId           String?  @map("shop_id")
  shopName         String?  @map("shop_name")
  stock            Int?
  relatedVideos    Int?     @map("related_videos")
  relatedAuthors   Int?     @map("related_authors")
  freeShipping     Boolean? @map("free_shipping")
  lastTimeStamp    BigInt?  @map("last_time_stamp")

  // Relations
  alerts     Alert[]
  alertFires AlertFire[]
  creatorProducts CreatorProduct[]
  productPerformance ProductPerformance[]

  @@map("products")
  @@schema("public")
}

model Alert {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  productId String   @map("product_id")
  threshold Float
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  product    Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  alertFires AlertFire[]

  @@map("alerts")
  @@schema("public")
}

model AlertFire {
  id         String   @id @default(cuid())
  alertId    String   @map("alert_id")
  productId  String   @map("product_id")
  userId     String   @map("user_id")
  trendScore Float    @map("trend_score")
  firedAt    DateTime @default(now()) @map("fired_at")
  notified   Boolean  @default(false)

  // Relations
  alert   Alert   @relation(fields: [alertId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("alert_fires")
  @@schema("public")
}

model Creator {
  id               String   @id
  username         String   @unique
  displayName      String?  @map("display_name")
  bio              String?
  followerCount    Int      @map("follower_count")
  followingCount   Int      @default(0) @map("following_count")
  likesCount       Int      @default(0) @map("likes_count")
  videoCount       Int      @default(0) @map("video_count")
  averageViews     Int      @map("average_views")
  engagementRate   Float    @map("engagement_rate")
  totalGMV         Float    @map("total_gmv")
  profileImageUrl  String?  @map("profile_image_url")
  isVerified       Boolean  @default(false) @map("is_verified")
  tiktokUserId     String?  @map("tiktok_user_id")
  secUid           String?  @map("sec_uid")
  region           String?
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  creatorProducts CreatorProduct[]
  competitorTracking CompetitorTracking[]
  videos CreatorVideo[]
  competitiveBenchmarks CompetitiveBenchmark[]

  @@map("creators")
  @@schema("public")
}

model CreatorProduct {
  id        String   @id @default(cuid())
  creatorId String   @map("creator_id")
  productId String   @map("product_id")
  gmv       Float
  sales     Int
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  creator Creator @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([creatorId, productId])
  @@map("creator_products")
  @@schema("public")
}

model Analytics {
  id             String   @id @default(cuid())
  userId         String   @map("user_id")
  date           DateTime
  gmv            Float
  commissions    Float
  orders         Int
  aov            Float
  conversionRate Float    @map("conversion_rate")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@map("analytics")
  @@schema("public")
}

// Competitor Tracking Models
model CompetitorTracking {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  creatorId   String   @map("creator_id")
  nickname    String?  // Custom nickname for this competitor
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  creator Creator @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  alerts  CompetitorAlert[]
  analyses CompetitorAnalysis[]

  @@unique([userId, creatorId])
  @@map("competitor_tracking")
  @@schema("public")
}

model CompetitorAlert {
  id                  String              @id @default(cuid())
  competitorTrackingId String             @map("competitor_tracking_id")
  alertType           CompetitorAlertType @map("alert_type")
  title               String
  description         String
  metadata            Json? // Additional data specific to alert type
  isRead              Boolean             @default(false) @map("is_read")
  createdAt           DateTime            @default(now()) @map("created_at")

  // Relations
  competitorTracking CompetitorTracking @relation(fields: [competitorTrackingId], references: [id], onDelete: Cascade)

  @@map("competitor_alerts")
  @@schema("public")
}

model CompetitorAnalysis {
  id                  String               @id @default(cuid())
  competitorTrackingId String              @map("competitor_tracking_id")
  analysisType        CompetitorAnalysisType @map("analysis_type")
  title               String
  content             String // AI-generated analysis content
  strengths           Json? // Array of strengths
  weaknesses          Json? // Array of weaknesses
  opportunities       Json? // Array of opportunities
  actionItems         Json? // Array of recommended actions
  score               Float? // Overall performance score (0-100)
  createdAt           DateTime             @default(now()) @map("created_at")

  // Relations
  competitorTracking CompetitorTracking @relation(fields: [competitorTrackingId], references: [id], onDelete: Cascade)

  @@map("competitor_analyses")
  @@schema("public")
}

model CreatorVideo {
  id              String   @id
  creatorId       String   @map("creator_id")
  title           String?
  description     String?
  viewCount       Int      @map("view_count")
  likeCount       Int      @map("like_count")
  shareCount      Int      @map("share_count")
  commentCount    Int      @map("comment_count")
  playTime        Int?     @map("play_time") // Duration in seconds
  publishedAt     DateTime @map("published_at")
  thumbnailUrl    String?  @map("thumbnail_url")
  videoUrl        String?  @map("video_url")
  hashtags        Json? // Array of hashtags
  mentionedProducts Json? // Array of product IDs mentioned
  engagementRate  Float?   @map("engagement_rate")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  creator Creator @relation(fields: [creatorId], references: [id], onDelete: Cascade)

  @@map("creator_videos")
  @@schema("public")
}

// Enums for competitor tracking
enum CompetitorAlertType {
  NEW_VIDEO
  VIRAL_POST
  LIVESTREAM
  PRODUCT_LAUNCH
  FOLLOWER_MILESTONE
  ENGAGEMENT_SPIKE
  SALES_MILESTONE

  @@schema("public")
}

enum CompetitorAnalysisType {
  STRENGTHS_WEAKNESSES
  CONTENT_ANALYSIS
  PERFORMANCE_COMPARISON
  GAP_OPPORTUNITIES
  GROWTH_STRATEGY

  @@schema("public")
}

// Affiliate Dashboard Models
model AffiliateGoal {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  goalType    GoalType    @map("goal_type")
  title       String
  description String?
  targetValue Float       @map("target_value") // Target GMV, commissions, etc.
  currentValue Float      @default(0) @map("current_value")
  targetDate  DateTime?   @map("target_date")
  isCompleted Boolean     @default(false) @map("is_completed")
  completedAt DateTime?   @map("completed_at")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("affiliate_goals")
  @@schema("public")
}

model Achievement {
  id          String          @id @default(cuid())
  userId      String          @map("user_id")
  type        AchievementType
  title       String
  description String
  badgeIcon   String          @map("badge_icon") // Icon name or URL
  badgeColor  String          @map("badge_color") // Hex color
  unlockedAt  DateTime        @default(now()) @map("unlocked_at")
  value       Float? // Associated value (e.g., $1000 for first 1K month)

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("achievements")
  @@schema("public")
}

model CompetitiveBenchmark {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  competitorId    String   @map("competitor_id")
  metric          String   // 'gmv', 'commissions', 'conversion_rate', etc.
  userValue       Float    @map("user_value")
  competitorValue Float    @map("competitor_value")
  period          String   // 'daily', 'weekly', 'monthly'
  date            DateTime
  createdAt       DateTime @default(now()) @map("created_at")

  // Relations
  user       User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  competitor Creator @relation(fields: [competitorId], references: [id], onDelete: Cascade)

  @@unique([userId, competitorId, metric, period, date])
  @@map("competitive_benchmarks")
  @@schema("public")
}

model AIInsight {
  id          String      @id @default(cuid())
  userId      String      @map("user_id")
  insightType InsightType @map("insight_type")
  title       String
  content     String      // AI-generated insight content
  priority    String      @default("medium") // 'low', 'medium', 'high'
  actionItems Json?       @map("action_items") // Array of recommended actions
  metadata    Json? // Additional data (trends, comparisons, etc.)
  isRead      Boolean     @default(false) @map("is_read")
  validUntil  DateTime?   @map("valid_until") // When insight becomes stale
  createdAt   DateTime    @default(now()) @map("created_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("ai_insights")
  @@schema("public")
}

model ProductPerformance {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  productId    String   @map("product_id")
  date         DateTime
  gmv          Float    @default(0)
  commissions  Float    @default(0)
  orders       Int      @default(0)
  clicks       Int      @default(0)
  conversionRate Float  @default(0) @map("conversion_rate")

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([userId, productId, date])
  @@map("product_performance")
  @@schema("public")
}

// Enums for affiliate dashboard
enum GoalType {
  MONTHLY_GMV
  MONTHLY_COMMISSIONS
  CONVERSION_RATE
  PRODUCT_SALES
  CUSTOM

  @@schema("public")
}

enum AchievementType {
  FIRST_SALE
  MONTHLY_MILESTONE // $1K, $5K, $10K, $50K months
  STREAK // Consecutive days/weeks of sales
  PRODUCT_MASTER // High performance with specific product
  GROWTH_SPURT // Significant growth period
  CONVERSION_KING // High conversion rate achievement

  @@schema("public")
}

enum InsightType {
  PERFORMANCE_SUMMARY
  GROWTH_OPPORTUNITY
  PRODUCT_RECOMMENDATION
  COMPETITIVE_GAP
  ACTION_PLAN
  TREND_ALERT

  @@schema("public")
}

// TikTok Shop Performance Models
model ShopPerformance {
  id                      String   @id @default(cuid())
  userId                  String   @map("user_id")
  date                    DateTime
  granularity             String   // 'DAILY', 'WEEKLY', 'MONTHLY', 'ALL'
  
  // Core metrics
  gmv                     Float    @default(0)
  currency                String   @default("USD")
  orders                  Int      @default(0)
  skuOrders               Int      @default(0) @map("sku_orders")
  unitsSold               Int      @default(0) @map("units_sold")
  buyers                  Int      @default(0)
  avgOrderValue           Float    @default(0) @map("avg_order_value")
  
  // Traffic metrics
  productImpressions      Int      @default(0) @map("product_impressions")
  productPageViews        Int      @default(0) @map("product_page_views")
  avgProductPageVisitors  Int      @default(0) @map("avg_product_page_visitors")
  
  // Revenue breakdown
  gmvLive                 Float    @default(0) @map("gmv_live")
  gmvVideo                Float    @default(0) @map("gmv_video")
  gmvShop                 Float    @default(0) @map("gmv_shop")
  
  // Buyer breakdown
  buyersLive              Int      @default(0) @map("buyers_live")
  buyersVideo             Int      @default(0) @map("buyers_video")
  buyersShop              Int      @default(0) @map("buyers_shop")
  
  // Impression breakdown
  impressionsLive         Int      @default(0) @map("impressions_live")
  impressionsVideo        Int      @default(0) @map("impressions_video")
  impressionsShop         Int      @default(0) @map("impressions_shop")
  
  // Page view breakdown
  pageViewsLive           Int      @default(0) @map("page_views_live")
  pageViewsVideo          Int      @default(0) @map("page_views_video")
  pageViewsShop           Int      @default(0) @map("page_views_shop")
  
  // Returns and refunds
  refunds                 Float    @default(0)
  cancellationsAndReturns Int      @default(0) @map("cancellations_and_returns")
  
  // Calculated metrics
  conversionRate          Float    @default(0) @map("conversion_rate")
  estimatedCommissions    Float    @default(0) @map("estimated_commissions")
  
  // Metadata
  isComparison            Boolean  @default(false) @map("is_comparison") // If this is comparison period data
  tiktokRequestId         String?  @map("tiktok_request_id")
  rawData                 Json?    @map("raw_data") // Store original API response
  
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date, granularity, isComparison])
  @@map("shop_performance")
  @@schema("public")
}