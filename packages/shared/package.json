{"name": "@xact-data/shared", "version": "1.0.0", "description": "Shared utilities and types for Xact Data", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"@xact-data/ai-wrapper": "workspace:*", "@xact-data/database": "workspace:*", "node-fetch": "^3.3.2", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/node-fetch": "^2.6.13", "typescript": "^5.1.0"}}