import crypto from 'crypto';
import { TikTokShopOAuthConfig, TikTokShopOAuthTokenResponse, TikTokShopAuthorizationResponse } from './tiktok-api-types';

export class TikTokShopOAuthService {
  private config: TikTokShopOAuthConfig;

  constructor(config: TikTokShopOAuthConfig) {
    this.config = config;
  }

  /**
   * Generate the authorization URL for TikTok Shop Creator OAuth flow (affiliate integration)
   */
  generateAuthorizationUrl(state: string): string {
    // For creator authorization, state parameter is required and must be provided manually
    if (!state) {
      throw new Error('State parameter is required for creator authorization');
    }

    const params = new URLSearchParams({
      app_key: this.config.appKey,
      state: state,
    });

    // Use the creator authorization endpoint as specified in the documentation
    return `https://shop.tiktok.com/alliance/creator/auth?${params.toString()}`;
  }

  /**
   * Generate signature for TikTok Shop API request
   */
  private generateSignature(params: Record<string, string>): string {
    // Create the string to sign by sorting parameters
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}${params[key]}`)
      .join('');

    // Create the full string with app_secret at both ends
    const stringToSign = `${this.config.appSecret}${sortedParams}${this.config.appSecret}`;
    
    // Generate HMAC-SHA256 signature
    return crypto.createHmac('sha256', this.config.appSecret)
      .update(stringToSign)
      .digest('hex');
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForTokens(authorizationCode: string): Promise<TikTokShopOAuthTokenResponse> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Build parameters
    const params: Record<string, string> = {
      app_key: this.config.appKey,
      app_secret: this.config.appSecret,
      timestamp: timestamp.toString(),
      auth_code: authorizationCode,
      grant_type: 'authorized_code',
    };

    // Generate signature
    const signature = this.generateSignature(params);
    params.sign = signature;

    try {
      // TikTok Shop API expects GET request with query parameters
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        queryParams.append(key, params[key]);
      });

      const url = `https://auth.tiktok-shops.com/api/v2/token/get?${queryParams.toString()}`;

      const response = await (global as any).fetch(url, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`TikTok Shop OAuth token exchange failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokShopOAuthTokenResponse = await response.json();

      if (data.code !== 0) {
        throw new Error(`TikTok Shop OAuth error: ${data.message} (code: ${data.code})`);
      }

      return data;
    } catch (error) {
      console.error('Error exchanging authorization code for tokens:', error);
      throw error;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string): Promise<TikTokShopOAuthTokenResponse> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Build parameters
    const params: Record<string, string> = {
      app_key: this.config.appKey,
      app_secret: this.config.appSecret,
      timestamp: timestamp.toString(),
      refresh_token: refreshToken,
      grant_type: 'refresh_token',
    };

    // Generate signature
    const signature = this.generateSignature(params);
    params.sign = signature;

    try {
      // TikTok Shop API expects GET request with query parameters for token refresh
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        queryParams.append(key, params[key]);
      });

      const url = `https://auth.tiktok-shops.com/api/v2/token/refresh?${queryParams.toString()}`;

      const response = await (global as any).fetch(url, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`TikTok Shop OAuth token refresh failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokShopOAuthTokenResponse = await response.json();

      if (data.code !== 0) {
        throw new Error(`TikTok Shop OAuth refresh error: ${data.message} (code: ${data.code})`);
      }

      return data;
    } catch (error) {
      console.error('Error refreshing access token:', error);
      throw error;
    }
  }

  /**
   * Get authorized shop information
   */
  async getAuthorizedShops(accessToken: string): Promise<TikTokShopAuthorizationResponse> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Build parameters
    const params: Record<string, string> = {
      app_key: this.config.appKey,
      app_secret: this.config.appSecret,
      timestamp: timestamp.toString(),
    };

    // Generate signature
    const signature = this.generateSignature(params);
    params.sign = signature;

    // Build URL with query parameters
    const url = new URL('/authorization/202309/shops', this.config.apiBaseUrl);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    try {
      const response = await (global as any).fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-tts-access-token': accessToken,
        },
      });

      if (!response.ok) {
        throw new Error(`TikTok Shop authorization info request failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokShopAuthorizationResponse = await response.json();

      if (data.code !== 0) {
        throw new Error(`TikTok Shop authorization info error: ${data.message} (code: ${data.code})`);
      }

      return data;
    } catch (error) {
      console.error('Error getting authorized shops:', error);
      throw error;
    }
  }

  /**
   * Check if access token is expired
   */
  static isTokenExpired(expiryDate: Date): boolean {
    return new Date() >= expiryDate;
  }

  /**
   * Calculate token expiry date from expires_in seconds
   */
  static calculateTokenExpiry(expiresIn: number): Date {
    return new Date(Date.now() + expiresIn * 1000);
  }
}

/**
 * Create TikTok Shop OAuth service instance with environment configuration
 */
export function createTikTokShopOAuthService(): TikTokShopOAuthService {
  const config: TikTokShopOAuthConfig = {
    appKey: process.env.TIKTOK_SHOP_APP_KEY || '6hi6rl0brml5g',
    appSecret: process.env.TIKTOK_SHOP_APP_SECRET || '85aca6a18764530340e7f57cd24316d3bf5816c9',
    redirectUri: process.env.TIKTOK_SHOP_REDIRECT_URI || 'https://web-production-611c4.up.railway.app/auth/tiktok-shop/callback',
    authBaseUrl: process.env.TIKTOK_SHOP_AUTH_BASE_URL || 'https://services.tiktokshop.com',
    apiBaseUrl: process.env.TIKTOK_SHOP_API_BASE_URL || 'https://open-api.tiktokglobalshop.com',
  };

  return new TikTokShopOAuthService(config);
}