import { TikTokUserInfoResponse } from './tiktok-api-types';
import { TikTokApiService } from './tiktok-api-service';

/**
 * Transform TikTok User API response to Creator database model
 */
export function transformTikTokUserToCreator(userInfo: TikTokUserInfoResponse) {
  const { user, stats } = userInfo.userInfo;
  
  // Calculate estimated average views (we don't have this directly, so estimate)
  const estimatedAverageViews = stats.videoCount > 0 
    ? Math.round(stats.heartCount / stats.videoCount / 10) // Rough estimate based on likes per video
    : 0;
  
  // Calculate engagement rate
  const engagementRate = TikTokApiService.calculateEngagementRate(
    stats.followerCount,
    estimatedAverageViews,
    stats.diggCount
  );

  return {
    id: user.secUid, // Use secUid as primary key
    username: user.uniqueId,
    displayName: user.nickname,
    bio: user.signature,
    followerCount: stats.followerCount,
    followingCount: stats.followingCount,
    likesCount: stats.heartCount,
    videoCount: stats.videoCount,
    averageViews: estimatedAverageViews,
    engagementRate: Math.round(engagementRate * 100) / 100, // Round to 2 decimal places
    totalGMV: 0, // This would need to be calculated separately
    profileImageUrl: user.avatarLarger,
    isVerified: user.verified,
    tiktokUserId: user.id,
    secUid: user.secUid,
    region: null, // Not available in this API
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Extract key metrics from TikTok user for comparison
 */
export function extractCreatorMetrics(userInfo: TikTokUserInfoResponse) {
  const { user, stats } = userInfo.userInfo;
  
  return {
    username: user.uniqueId,
    displayName: user.nickname,
    followerCount: stats.followerCount,
    followingCount: stats.followingCount,
    likesCount: stats.heartCount,
    videoCount: stats.videoCount,
    isVerified: user.verified,
    averageViewsEstimate: stats.videoCount > 0 
      ? Math.round(stats.heartCount / stats.videoCount / 10)
      : 0,
    likesPerFollower: stats.followerCount > 0 
      ? Math.round((stats.heartCount / stats.followerCount) * 100) / 100
      : 0,
    videosPerFollower: stats.followerCount > 0
      ? Math.round((stats.videoCount / stats.followerCount) * 10000) / 10000
      : 0,
  };
}

/**
 * Generate creator comparison insights
 */
export function compareCreators(creator1: any, creator2: any) {
  const followerDiff = creator1.followerCount - creator2.followerCount;
  const followerDiffPercent = creator2.followerCount > 0 
    ? Math.round(((followerDiff / creator2.followerCount) * 100) * 100) / 100
    : 0;

  const likeDiff = creator1.likesCount - creator2.likesCount;
  const likeDiffPercent = creator2.likesCount > 0
    ? Math.round(((likeDiff / creator2.likesCount) * 100) * 100) / 100
    : 0;

  const videoDiff = creator1.videoCount - creator2.videoCount;
  const videoDiffPercent = creator2.videoCount > 0
    ? Math.round(((videoDiff / creator2.videoCount) * 100) * 100) / 100
    : 0;

  return {
    followerComparison: {
      difference: followerDiff,
      percentageDifference: followerDiffPercent,
      winner: followerDiff > 0 ? creator1.username : creator2.username,
    },
    likesComparison: {
      difference: likeDiff,
      percentageDifference: likeDiffPercent,
      winner: likeDiff > 0 ? creator1.username : creator2.username,
    },
    contentComparison: {
      difference: videoDiff,
      percentageDifference: videoDiffPercent,
      winner: videoDiff > 0 ? creator1.username : creator2.username,
    },
    overallWinner: determineOverallWinner(creator1, creator2),
  };
}

/**
 * Determine overall winner based on multiple factors
 */
function determineOverallWinner(creator1: any, creator2: any) {
  let creator1Score = 0;
  let creator2Score = 0;

  // Follower count (30% weight)
  if (creator1.followerCount > creator2.followerCount) creator1Score += 30;
  else creator2Score += 30;

  // Engagement rate (25% weight)
  if (creator1.engagementRate > creator2.engagementRate) creator1Score += 25;
  else creator2Score += 25;

  // Total likes (20% weight)
  if (creator1.likesCount > creator2.likesCount) creator1Score += 20;
  else creator2Score += 20;

  // Content volume (15% weight)
  if (creator1.videoCount > creator2.videoCount) creator1Score += 15;
  else creator2Score += 15;

  // Verification status (10% weight)
  if (creator1.isVerified && !creator2.isVerified) creator1Score += 10;
  else if (creator2.isVerified && !creator1.isVerified) creator2Score += 10;
  else {
    creator1Score += 5;
    creator2Score += 5;
  }

  return {
    winner: creator1Score > creator2Score ? creator1.username : creator2.username,
    score: {
      [creator1.username]: creator1Score,
      [creator2.username]: creator2Score,
    },
    margin: Math.abs(creator1Score - creator2Score),
  };
}