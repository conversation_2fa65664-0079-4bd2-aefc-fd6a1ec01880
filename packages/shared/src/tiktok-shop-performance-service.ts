import crypto from 'crypto';
import { 
  TikTokShopPerformanceConfig, 
  TikTokShopPerformanceQuery, 
  TikTokShopPerformanceResponse 
} from './tiktok-api-types';

export class TikTokShopPerformanceService {
  private config: TikTokShopPerformanceConfig;

  constructor(config: TikTokShopPerformanceConfig) {
    this.config = config;
  }

  /**
   * Generate signature for TikTok Shop API request
   */
  private generateSignature(params: Record<string, string>, timestamp: number): string {
    // Create the string to sign by sorting parameters
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}${params[key]}`)
      .join('');

    // Create the full string with app_secret at both ends
    const stringToSign = `${this.config.appSecret}${sortedParams}${this.config.appSecret}`;
    
    // Generate HMAC-SHA256 signature
    return crypto.createHmac('sha256', this.config.appSecret)
      .update(stringToSign)
      .digest('hex');
  }

  /**
   * Fetch shop performance data from TikTok Shop API
   */
  async getShopPerformance(query: TikTokShopPerformanceQuery): Promise<TikTokShopPerformanceResponse> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Build query parameters
    const params: Record<string, string> = {
      app_key: this.config.appKey,
      timestamp: timestamp.toString(),
      shop_cipher: this.config.shopCipher,
      granularity: query.granularity,
      currency: query.currency,
      start_date_ge: query.start_date_ge,
      end_date_lt: query.end_date_lt,
    };

    if (query.with_comparison !== undefined) {
      params.with_comparison = query.with_comparison.toString();
    }

    // Generate signature
    const signature = this.generateSignature(params, timestamp);
    params.sign = signature;

    // Build URL with query parameters
    const url = new URL('/analytics/202405/shop/performance', this.config.baseUrl);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    try {
      const response = await (global as any).fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-tts-access-token': this.config.accessToken,
        },
      });

      if (!response.ok) {
        throw new Error(`TikTok Shop Performance API request failed: ${response.status} ${response.statusText}`);
      }

      const data: TikTokShopPerformanceResponse = await response.json();

      if (data.code !== 0) {
        throw new Error(`TikTok Shop Performance API error: ${data.message} (code: ${data.code})`);
      }

      return data;
    } catch (error) {
      console.error('Error fetching TikTok Shop performance:', error);
      throw error;
    }
  }

  /**
   * Get performance data for the last 30 days
   */
  async getLast30DaysPerformance(): Promise<TikTokShopPerformanceResponse> {
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 30);

    return this.getShopPerformance({
      granularity: 'DAILY',
      currency: 'USD',
      start_date_ge: startDate.toISOString().split('T')[0],
      end_date_lt: endDate.toISOString().split('T')[0],
      with_comparison: true,
    });
  }

  /**
   * Get monthly performance summary
   */
  async getMonthlyPerformance(year: number, month: number): Promise<TikTokShopPerformanceResponse> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    return this.getShopPerformance({
      granularity: 'MONTHLY',
      currency: 'USD',
      start_date_ge: startDate.toISOString().split('T')[0],
      end_date_lt: endDate.toISOString().split('T')[0],
      with_comparison: true,
    });
  }

  /**
   * Get weekly performance data
   */
  async getWeeklyPerformance(startDate: Date, endDate: Date): Promise<TikTokShopPerformanceResponse> {
    return this.getShopPerformance({
      granularity: 'WEEKLY',
      currency: 'USD',
      start_date_ge: startDate.toISOString().split('T')[0],
      end_date_lt: endDate.toISOString().split('T')[0],
      with_comparison: true,
    });
  }

  /**
   * Parse GMV amount from string format
   */
  static parseGMVAmount(gmvStr: string): number {
    return parseFloat(gmvStr.replace(/[,$]/g, ''));
  }

  /**
   * Calculate commission estimate based on GMV and average commission rate
   */
  static estimateCommissions(gmv: number, commissionRate: number = 0.1): number {
    return gmv * commissionRate;
  }

  /**
   * Calculate conversion rate from performance data
   */
  static calculateConversionRate(orders: number, productPageViews: number): number {
    if (productPageViews === 0) return 0;
    return (orders / productPageViews) * 100;
  }

  /**
   * Calculate average order value from performance data
   */
  static calculateAOV(gmv: number, orders: number): number {
    if (orders === 0) return 0;
    return gmv / orders;
  }
}

/**
 * Create TikTok Shop Performance service instance with user-specific configuration
 */
export function createTikTokShopPerformanceService(
  accessToken?: string, 
  shopCipher?: string
): TikTokShopPerformanceService {
  const config: TikTokShopPerformanceConfig = {
    appKey: process.env.TIKTOK_SHOP_APP_KEY || '6hi6rl0brml5g',
    appSecret: process.env.TIKTOK_SHOP_APP_SECRET || '85aca6a18764530340e7f57cd24316d3bf5816c9',
    accessToken: accessToken || process.env.TIKTOK_SHOP_ACCESS_TOKEN || '',
    shopCipher: shopCipher || process.env.TIKTOK_SHOP_CIPHER || '',
    baseUrl: process.env.TIKTOK_SHOP_API_BASE_URL || 'https://open-api.tiktokglobalshop.com',
  };

  return new TikTokShopPerformanceService(config);
}