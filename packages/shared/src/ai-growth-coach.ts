import { AIClient } from '@xact-data/ai-wrapper';
import { prisma } from '@xact-data/database';
import type { 
  Analytics, 
  ProductPerformance, 
  CompetitiveBenchmark, 
  Creator,
  Product,
  InsightType 
} from './types';

interface UserPerformanceData {
  userId: string;
  analytics: Analytics[];
  productPerformance: ProductPerformance[];
  competitiveBenchmarks: CompetitiveBenchmark[];
  goals: any[];
  achievements: any[];
}

interface AIInsightGeneration {
  insightType: InsightType;
  title: string;
  content: string;
  priority: 'low' | 'medium' | 'high';
  actionItems: string[];
  metadata: Record<string, any>;
  validUntil?: Date;
}

export class AIGrowthCoachService {
  private aiClient: AIClient;

  constructor() {
    this.aiClient = new AIClient({
      provider: process.env.AI_PROVIDER as 'openai' | 'anthropic' || 'openai',
      apiKey: process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY!,
      model: process.env.AI_MODEL || 'gpt-4',
    });
  }

  /**
   * Generate comprehensive performance summary for a user
   */
  async generatePerformanceSummary(userId: string): Promise<AIInsightGeneration> {
    const userData = await this.getUserPerformanceData(userId);
    
    // Calculate key metrics
    const totalGMV = userData.analytics.reduce((sum, a) => sum + a.gmv, 0);
    const totalCommissions = userData.analytics.reduce((sum, a) => sum + a.commissions, 0);
    const totalOrders = userData.analytics.reduce((sum, a) => sum + a.orders, 0);
    const avgAOV = totalOrders > 0 ? totalGMV / totalOrders : 0;
    
    // Get growth trends
    const recentAnalytics = userData.analytics.slice(0, 7);
    const olderAnalytics = userData.analytics.slice(7, 14);
    const recentGMV = recentAnalytics.reduce((sum, a) => sum + a.gmv, 0);
    const olderGMV = olderAnalytics.reduce((sum, a) => sum + a.gmv, 0);
    const growthRate = olderGMV > 0 ? ((recentGMV - olderGMV) / olderGMV) * 100 : 0;

    const prompt = `
    Analyze this TikTok Shop affiliate's performance and provide a comprehensive summary:

    Performance Metrics (Last 30 Days):
    - Total GMV: $${totalGMV.toFixed(2)}
    - Total Commissions: $${totalCommissions.toFixed(2)}
    - Total Orders: ${totalOrders}
    - Average Order Value: $${avgAOV.toFixed(2)}
    - Growth Rate (Week over Week): ${growthRate.toFixed(1)}%

    Top Products: ${JSON.stringify(userData.productPerformance.slice(0, 3).map(p => ({
      product: p.productId,
      gmv: p.gmv,
      orders: p.orders,
      conversionRate: p.conversionRate
    })))}

    Active Goals: ${userData.goals.length} goals
    Achievements: ${userData.achievements.length} unlocked

    Provide a concise performance summary highlighting:
    1. Key strengths and wins
    2. Areas for improvement
    3. Growth trajectory analysis
    4. Overall performance grade (A-F)

    Keep it motivational but realistic. Focus on actionable insights.
    `;

    const analysis = await this.aiClient.generateInsight(prompt);
    
    return {
      insightType: 'PERFORMANCE_SUMMARY',
      title: `Performance Summary - ${this.getPerformanceGrade(totalCommissions)}`,
      content: analysis,
      priority: 'high',
      actionItems: await this.extractActionItems(analysis),
      metadata: {
        totalGMV,
        totalCommissions,
        totalOrders,
        avgAOV,
        growthRate,
        period: '30days'
      },
      validUntil: this.getValidUntilDate(7), // Valid for 7 days
    };
  }

  /**
   * Generate growth opportunity insights
   */
  async generateGrowthOpportunity(userId: string): Promise<AIInsightGeneration> {
    const userData = await this.getUserPerformanceData(userId);
    
    // Identify underperforming products
    const underperformingProducts = userData.productPerformance
      .filter(p => p.conversionRate < 2.0) // Below 2% conversion rate
      .sort((a, b) => b.clicks - a.clicks) // High clicks but low conversion
      .slice(0, 3);

    // Find seasonal trends
    const monthlyData = this.groupAnalyticsByMonth(userData.analytics);
    
    const prompt = `
    Identify growth opportunities for this TikTok Shop affiliate:

    Current Performance:
    ${JSON.stringify(userData.analytics.slice(0, 5).map(a => ({
      date: a.date,
      gmv: a.gmv,
      orders: a.orders,
      conversionRate: a.conversionRate
    })))}

    Underperforming Products (High Traffic, Low Conversion):
    ${JSON.stringify(underperformingProducts.map(p => ({
      productId: p.productId,
      clicks: p.clicks,
      orders: p.orders,
      conversionRate: p.conversionRate
    })))}

    Monthly Trends:
    ${JSON.stringify(monthlyData)}

    Competitive Position:
    ${userData.competitiveBenchmarks.length > 0 ? 
      JSON.stringify(userData.competitiveBenchmarks.slice(0, 3).map(b => ({
        metric: b.metric,
        userValue: b.userValue,
        competitorValue: b.competitorValue,
        performance: b.userValue > b.competitorValue ? 'ahead' : 'behind'
      }))) : 'No competitive data available'
    }

    Identify 3-5 specific growth opportunities focusing on:
    1. Product optimization strategies
    2. Conversion rate improvements
    3. Seasonal opportunities
    4. Competitive gaps to exploit
    5. New product categories to explore

    Be specific and actionable.
    `;

    const analysis = await this.aiClient.generateInsight(prompt);
    
    return {
      insightType: 'GROWTH_OPPORTUNITY',
      title: 'Growth Opportunities Identified',
      content: analysis,
      priority: 'high',
      actionItems: await this.extractActionItems(analysis),
      metadata: {
        underperformingProducts: underperformingProducts.length,
        competitiveBenchmarks: userData.competitiveBenchmarks.length,
        monthlyTrends: monthlyData
      },
      validUntil: this.getValidUntilDate(14), // Valid for 2 weeks
    };
  }

  /**
   * Generate product recommendations
   */
  async generateProductRecommendations(userId: string): Promise<AIInsightGeneration> {
    const userData = await this.getUserPerformanceData(userId);
    
    // Get trending products from database
    const trendingProducts = await prisma.product.findMany({
      where: {
        trendScore: { gte: 70 }
      },
      orderBy: { trendScore: 'desc' },
      take: 10
    });

    // Get user's top performing categories
    const userProductIds = userData.productPerformance.map(p => p.productId);
    const userProducts = await prisma.product.findMany({
      where: { id: { in: userProductIds } },
      select: { id: true, category: true, category1: true, category2: true }
    });

    const topCategories = this.getTopCategories(userProducts, userData.productPerformance);

    const prompt = `
    Recommend products for this TikTok Shop affiliate based on their performance:

    User's Top Performing Categories:
    ${JSON.stringify(topCategories)}

    Current Top Products:
    ${JSON.stringify(userData.productPerformance.slice(0, 5).map(p => ({
      productId: p.productId,
      gmv: p.gmv,
      orders: p.orders,
      conversionRate: p.conversionRate
    })))}

    Trending Products Available:
    ${JSON.stringify(trendingProducts.slice(0, 8).map(p => ({
      id: p.id,
      title: p.title,
      category: p.category,
      trendScore: p.trendScore,
      commissionRate: p.commissionRate,
      price: p.price
    })))}

    Recommend 5-7 specific products focusing on:
    1. Products in user's successful categories
    2. High-trend products with good commission rates
    3. Complementary products to current top performers
    4. Emerging trends in related categories
    5. Products with good conversion potential

    For each recommendation, explain:
    - Why it's a good fit
    - Expected performance potential
    - Best promotion strategies
    `;

    const analysis = await this.aiClient.generateInsight(prompt);
    
    return {
      insightType: 'PRODUCT_RECOMMENDATION',
      title: 'Recommended Products for You',
      content: analysis,
      priority: 'medium',
      actionItems: await this.extractActionItems(analysis),
      metadata: {
        topCategories,
        trendingProductsCount: trendingProducts.length,
        userTopProducts: userData.productPerformance.slice(0, 3).map(p => p.productId)
      },
      validUntil: this.getValidUntilDate(5), // Valid for 5 days (products change quickly)
    };
  }

  /**
   * Generate competitive gap analysis
   */
  async generateCompetitiveGapAnalysis(userId: string, competitorId?: string): Promise<AIInsightGeneration> {
    const userData = await this.getUserPerformanceData(userId);
    
    let competitorData: Creator | null = null;
    let specificBenchmarks: CompetitiveBenchmark[] = [];

    if (competitorId) {
      competitorData = await prisma.creator.findUnique({
        where: { id: competitorId }
      });
      
      specificBenchmarks = userData.competitiveBenchmarks.filter(
        b => b.competitorId === competitorId
      );
    }

    // Get user's current metrics
    const userMetrics = this.calculateUserMetrics(userData);
    
    const prompt = `
    Analyze competitive gaps for this TikTok Shop affiliate:

    User Performance:
    ${JSON.stringify(userMetrics)}

    ${competitorData ? `
    Specific Competitor Analysis (${competitorData.displayName || competitorData.username}):
    - Followers: ${competitorData.followerCount}
    - Engagement Rate: ${competitorData.engagementRate}%
    - Total GMV: $${competitorData.totalGMV}
    
    Direct Comparisons:
    ${JSON.stringify(specificBenchmarks.map(b => ({
      metric: b.metric,
      user: b.userValue,
      competitor: b.competitorValue,
      gap: b.competitorValue - b.userValue
    })))}
    ` : `
    General Competitive Benchmarks:
    ${JSON.stringify(userData.competitiveBenchmarks.slice(0, 5).map(b => ({
      metric: b.metric,
      user: b.userValue,
      competitor: b.competitorValue,
      gap: b.competitorValue - b.userValue
    })))}
    `}

    Identify key competitive gaps and provide:
    1. Specific areas where user is underperforming
    2. Competitor strategies to analyze and adapt
    3. Quick wins to close immediate gaps
    4. Long-term strategies for competitive advantage
    5. Unique strengths to leverage

    Be specific about metrics and provide actionable steps.
    `;

    const analysis = await this.aiClient.generateInsight(prompt);
    
    return {
      insightType: 'COMPETITIVE_GAP',
      title: competitorData ? 
        `Gap Analysis vs ${competitorData.displayName || competitorData.username}` :
        'Competitive Gap Analysis',
      content: analysis,
      priority: 'high',
      actionItems: await this.extractActionItems(analysis),
      metadata: {
        competitorId,
        competitorName: competitorData?.displayName || competitorData?.username,
        benchmarksAnalyzed: specificBenchmarks.length || userData.competitiveBenchmarks.length,
        userMetrics
      },
      validUntil: this.getValidUntilDate(10), // Valid for 10 days
    };
  }

  /**
   * Generate weekly action plan
   */
  async generateWeeklyActionPlan(userId: string): Promise<AIInsightGeneration> {
    const userData = await this.getUserPerformanceData(userId);
    
    // Get user's active goals
    const activeGoals = userData.goals.filter(g => !g.isCompleted);
    
    // Identify priorities based on recent performance
    const recentPerformance = userData.analytics.slice(0, 7);
    const priorities = this.identifyWeeklyPriorities(userData, recentPerformance);

    const prompt = `
    Create a weekly action plan for this TikTok Shop affiliate:

    Current Goals:
    ${JSON.stringify(activeGoals.map(g => ({
      type: g.goalType,
      title: g.title,
      target: g.targetValue,
      current: g.currentValue,
      progress: (g.currentValue / g.targetValue * 100).toFixed(1) + '%'
    })))}

    Recent Performance (Last 7 Days):
    ${JSON.stringify(recentPerformance.map(a => ({
      date: a.date,
      gmv: a.gmv,
      orders: a.orders,
      conversionRate: a.conversionRate
    })))}

    Identified Priorities:
    ${JSON.stringify(priorities)}

    Top Products to Focus On:
    ${JSON.stringify(userData.productPerformance.slice(0, 3).map(p => ({
      productId: p.productId,
      gmv: p.gmv,
      conversionRate: p.conversionRate,
      trend: 'analyze trend'
    })))}

    Create a 7-day action plan with:
    1. Daily specific tasks (Mon-Sun)
    2. Goal-focused activities
    3. Product optimization tasks
    4. Content creation suggestions
    5. Performance monitoring checkpoints

    Make each day's tasks achievable (2-3 tasks max) and directly tied to goals.
    Include specific metrics to track success.
    `;

    const analysis = await this.aiClient.generateInsight(prompt);
    
    return {
      insightType: 'ACTION_PLAN',
      title: 'Your Weekly Action Plan',
      content: analysis,
      priority: 'high',
      actionItems: await this.extractActionItems(analysis),
      metadata: {
        activeGoals: activeGoals.length,
        priorities,
        planPeriod: 'weekly',
        focusProducts: userData.productPerformance.slice(0, 3).map(p => p.productId)
      },
      validUntil: this.getValidUntilDate(7), // Valid for the week
    };
  }

  /**
   * Get comprehensive user performance data
   */
  private async getUserPerformanceData(userId: string): Promise<UserPerformanceData> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [analytics, productPerformance, competitiveBenchmarks, goals, achievements] = await Promise.all([
      prisma.analytics.findMany({
        where: {
          userId,
          date: { gte: thirtyDaysAgo }
        },
        orderBy: { date: 'desc' }
      }),
      
      prisma.productPerformance.findMany({
        where: {
          userId,
          date: { gte: thirtyDaysAgo }
        },
        orderBy: { gmv: 'desc' }
      }),
      
      prisma.competitiveBenchmark.findMany({
        where: {
          userId,
          date: { gte: thirtyDaysAgo }
        },
        orderBy: { date: 'desc' }
      }),
      
      prisma.affiliateGoal.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' }
      }),
      
      prisma.achievement.findMany({
        where: { userId },
        orderBy: { unlockedAt: 'desc' }
      })
    ]);

    return {
      userId,
      analytics,
      productPerformance,
      competitiveBenchmarks,
      goals,
      achievements
    };
  }

  /**
   * Extract action items from AI-generated content
   */
  private async extractActionItems(content: string): Promise<string[]> {
    const actionItemPrompt = `
    Extract 3-5 specific, actionable items from this content:
    
    ${content}
    
    Return only a JSON array of strings, each being a specific action item.
    Focus on concrete, measurable actions the user can take immediately.
    `;

    try {
      const response = await this.aiClient.generateInsight(actionItemPrompt);
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      // Fallback: extract action items manually
      const lines = content.split('\n');
      return lines
        .filter(line => line.includes('•') || line.includes('-') || line.includes('1.') || line.includes('2.'))
        .map(line => line.replace(/^[•\-\d\.]\s*/, '').trim())
        .filter(line => line.length > 10)
        .slice(0, 5);
    }
  }

  /**
   * Helper methods
   */
  private getPerformanceGrade(commissions: number): string {
    if (commissions >= 10000) return 'A+';
    if (commissions >= 5000) return 'A';
    if (commissions >= 2500) return 'B+';
    if (commissions >= 1000) return 'B';
    if (commissions >= 500) return 'C+';
    if (commissions >= 100) return 'C';
    return 'D';
  }

  private getValidUntilDate(days: number): Date {
    const date = new Date();
    date.setDate(date.getDate() + days);
    return date;
  }

  private groupAnalyticsByMonth(analytics: Analytics[]): Record<string, any> {
    const grouped: Record<string, { gmv: number; orders: number; commissions: number }> = {};
    
    analytics.forEach(a => {
      const month = new Date(a.date).toISOString().slice(0, 7); // YYYY-MM
      if (!grouped[month]) {
        grouped[month] = { gmv: 0, orders: 0, commissions: 0 };
      }
      grouped[month].gmv += a.gmv;
      grouped[month].orders += a.orders;
      grouped[month].commissions += a.commissions;
    });

    return grouped;
  }

  private getTopCategories(products: any[], performance: ProductPerformance[]): Array<{ category: string; gmv: number; orders: number }> {
    const categoryMap: Record<string, { gmv: number; orders: number }> = {};
    
    performance.forEach(p => {
      const product = products.find(prod => prod.id === p.productId);
      if (product) {
        const category = product.category || product.category1 || 'Unknown';
        if (!categoryMap[category]) {
          categoryMap[category] = { gmv: 0, orders: 0 };
        }
        categoryMap[category].gmv += p.gmv;
        categoryMap[category].orders += p.orders;
      }
    });

    return Object.entries(categoryMap)
      .map(([category, data]) => ({ category, ...data }))
      .sort((a, b) => b.gmv - a.gmv)
      .slice(0, 5);
  }

  private calculateUserMetrics(userData: UserPerformanceData) {
    const totalGMV = userData.analytics.reduce((sum, a) => sum + a.gmv, 0);
    const totalCommissions = userData.analytics.reduce((sum, a) => sum + a.commissions, 0);
    const totalOrders = userData.analytics.reduce((sum, a) => sum + a.orders, 0);
    const avgConversionRate = userData.analytics.length > 0 
      ? userData.analytics.reduce((sum, a) => sum + a.conversionRate, 0) / userData.analytics.length 
      : 0;

    return {
      totalGMV,
      totalCommissions,
      totalOrders,
      averageOrderValue: totalOrders > 0 ? totalGMV / totalOrders : 0,
      avgConversionRate,
      activeGoals: userData.goals.filter(g => !g.isCompleted).length,
      totalAchievements: userData.achievements.length
    };
  }

  private identifyWeeklyPriorities(userData: UserPerformanceData, recentPerformance: Analytics[]): string[] {
    const priorities: string[] = [];
    
    // Check if conversion rate is declining
    const avgConversion = recentPerformance.reduce((sum, a) => sum + a.conversionRate, 0) / recentPerformance.length;
    if (avgConversion < 2.0) {
      priorities.push('improve_conversion_rate');
    }

    // Check if orders are declining
    const recentOrders = recentPerformance.slice(0, 3).reduce((sum, a) => sum + a.orders, 0);
    const olderOrders = recentPerformance.slice(3, 6).reduce((sum, a) => sum + a.orders, 0);
    if (recentOrders < olderOrders * 0.8) {
      priorities.push('increase_order_volume');
    }

    // Check for goal progress
    const activeGoals = userData.goals.filter(g => !g.isCompleted);
    if (activeGoals.some(g => g.currentValue / g.targetValue < 0.3)) {
      priorities.push('focus_on_goals');
    }

    // Check product performance
    const lowPerformingProducts = userData.productPerformance.filter(p => p.conversionRate < 1.5);
    if (lowPerformingProducts.length > userData.productPerformance.length * 0.5) {
      priorities.push('optimize_product_mix');
    }

    return priorities.slice(0, 3); // Top 3 priorities
  }
}