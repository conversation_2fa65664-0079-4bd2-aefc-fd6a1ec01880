import { ApiResponse } from './types';

/**
 * Create a standardized API response
 */
export function createApiResponse<T>(
  success: boolean,
  data?: T,
  error?: string,
  message?: string
): ApiResponse<T> {
  return {
    success,
    data,
    error,
    message,
  };
}

/**
 * Format currency values
 */
export function formatCurrency(amount: number, currency = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Format percentage values
 */
export function formatPercentage(value: number, decimals = 2): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Calculate trend score based on various metrics
 */
export function calculateTrendScore(
  soldIn24h: number,
  creatorsCarrying: number,
  commissionRate: number,
  estimatedGMV: number
): number {
  // Simple algorithm - can be enhanced with ML
  const salesScore = Math.min(soldIn24h / 1000, 1) * 30;
  const creatorsScore = Math.min(creatorsCarrying / 100, 1) * 25;
  const commissionScore = (commissionRate / 100) * 20;
  const gmvScore = Math.min(estimatedGMV / 100000, 1) * 25;
  
  return Math.min(salesScore + creatorsScore + commissionScore + gmvScore, 100);
}

/**
 * Generate a random UUID (simple implementation)
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Validate environment variables
 */
export function validateEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Environment variable ${name} is required but not set`);
  }
  return value;
}

/**
 * Sleep utility for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}