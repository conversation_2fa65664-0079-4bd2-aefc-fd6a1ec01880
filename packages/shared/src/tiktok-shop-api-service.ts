import crypto from 'crypto';

// Fetch implementation for Node.js and browser compatibility
let fetchFunction: typeof fetch;

if (typeof globalThis !== 'undefined' && globalThis.fetch) {
  fetchFunction = globalThis.fetch;
} else if (typeof window !== 'undefined' && window.fetch) {
  fetchFunction = window.fetch;
} else {
  // Node.js environment - dynamically import node-fetch
  try {
    const nodeFetch = require('node-fetch');
    fetchFunction = nodeFetch.default || nodeFetch;
  } catch (e) {
    throw new Error('fetch is not available. Please install node-fetch for Node.js environments.');
  }
}

export interface TikTokShopConfig {
  apiKey: string;
  apiSecret: string;
  baseUrl?: string;
}

export interface TikTokShopOAuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  shop_id: string;
  shop_name: string;
  granted_scopes?: string[]; // Added for creator authorization partial scopes
}

export interface TikTokShopSellerInfo {
  shop_id: string;
  shop_name: string;
  shop_region: string;
  shop_type: string;
  seller_type: string;
  verification_status: string;
  created_time: number;
  updated_time: number;
}

export interface TikTokShopPerformanceData {
  gmv: number;
  orders: number;
  units_sold: number;
  buyers: number;
  avg_order_value: number;
  conversion_rate: number;
  impressions: number;
  clicks: number;
  date: string;
  granularity: string;
}

export class TikTokShopApiService {
  private config: TikTokShopConfig;
  private baseUrl: string;

  constructor(config: TikTokShopConfig) {
    this.config = config;
    this.baseUrl = config.baseUrl || 'https://open-api.tiktokglobalshop.com';
  }

  /**
   * Generate OAuth authorization URL for creators to connect their accounts (affiliate integration)
   */
  generateAuthUrl(redirectUri: string, state?: string): string {
    // For creator authorization, state parameter is required and must be provided manually
    if (!state) {
      throw new Error('State parameter is required for creator authorization');
    }

    const params = new URLSearchParams({
      app_key: this.config.apiKey,
      state: state,
    });

    // Use the creator authorization endpoint as specified in the documentation
    return `https://shop.tiktok.com/alliance/creator/auth?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access tokens
   */
  async exchangeCodeForTokens(code: string, redirectUri: string): Promise<TikTokShopOAuthTokens> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    console.log('TikTokShopApiService: Starting token exchange', {
      codeLength: code.length,
      redirectUri,
      timestamp
    });
    
    // Build parameters for OAuth token exchange
    const params: Record<string, string> = {
      app_key: this.config.apiKey,
      app_secret: this.config.apiSecret,
      timestamp: timestamp.toString(),
      auth_code: code,
      grant_type: 'authorized_code',
    };

    console.log('TikTokShopApiService: Token exchange params before signature', {
      app_key: params.app_key,
      has_app_secret: !!params.app_secret,
      timestamp: params.timestamp,
      auth_code_length: params.auth_code.length,
      grant_type: params.grant_type
    });

    // Generate OAuth signature (different from regular API signature)
    const signature = this.generateOAuthSignature(params);
    params.sign = signature;
    
    console.log('TikTokShopApiService: Generated signature', {
      signatureLength: signature.length
    });

    // Use the correct TikTok Shop API endpoint for token exchange
    // TikTok Shop API expects GET request with query parameters
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      queryParams.append(key, params[key]);
    });

    const url = `https://auth.tiktok-shops.com/api/v2/token/get?${queryParams.toString()}`;

    console.log('TikTokShopApiService: Making token exchange request', {
      url,
      method: 'GET',
      queryParamsLength: queryParams.toString().length
    });

    const response = await fetchFunction(url, {
      method: 'GET',
    });

    console.log('TikTokShopApiService: Received response', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('TikTokShopApiService: Token exchange failed', {
        status: response.status,
        statusText: response.statusText,
        errorData
      });
      throw new Error(`Failed to exchange code for tokens: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`TikTok Shop API error: ${data.message || 'Unknown error'}`);
    }

    return data.data;
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string): Promise<TikTokShopOAuthTokens> {
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Build parameters for OAuth token refresh
    const params: Record<string, string> = {
      app_key: this.config.apiKey,
      app_secret: this.config.apiSecret,
      timestamp: timestamp.toString(),
      refresh_token: refreshToken,
      grant_type: 'refresh_token',
    };

    // Generate OAuth signature (different from regular API signature)
    const signature = this.generateOAuthSignature(params);
    params.sign = signature;

    // TikTok Shop API expects GET request with query parameters for token refresh
    const queryParams = new URLSearchParams();
    Object.keys(params).forEach(key => {
      queryParams.append(key, params[key]);
    });

    const url = `https://auth.tiktok-shops.com/api/v2/token/refresh?${queryParams.toString()}`;

    const response = await fetchFunction(url, {
      method: 'GET',
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to refresh token: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`TikTok Shop API error: ${data.message || 'Unknown error'}`);
    }

    return data.data;
  }

  /**
   * Get creator information (replaces seller info for affiliate integration)
   */
  async getCreatorInfo(accessToken: string): Promise<TikTokShopSellerInfo> {
    const timestamp = Math.floor(Date.now() / 1000);
    const path = '/api/v2/creator/get_creator_info';
    
    const signature = this.generateSignature('GET', path, {}, timestamp);

    const response = await fetchFunction(`${this.baseUrl}${path}?app_key=${this.config.apiKey}&timestamp=${timestamp}&sign=${signature}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tts-access-token': accessToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get creator info: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`TikTok Shop API error: ${data.message || 'Unknown error'}`);
    }

    return data.data;
  }

  /**
   * Get seller information (deprecated - use getCreatorInfo for affiliate integration)
   * @deprecated Use getCreatorInfo instead for creator/affiliate integration
   */
  async getSellerInfo(accessToken: string): Promise<TikTokShopSellerInfo> {
    console.warn('getSellerInfo is deprecated for creator integration. Use getCreatorInfo instead.');
    return this.getCreatorInfo(accessToken);
  }

  /**
   * Get creator performance data for affiliate dashboard (updated for creator integration)
   */
  async getCreatorPerformance(
    accessToken: string,
    startDate: string,
    endDate: string,
    granularity: 'DAILY' | 'WEEKLY' | 'MONTHLY' = 'DAILY'
  ): Promise<TikTokShopPerformanceData[]> {
    const timestamp = Math.floor(Date.now() / 1000);
    const path = '/api/v2/creator/performance';
    
    const queryParams = {
      app_key: this.config.apiKey,
      timestamp: timestamp.toString(),
      start_date: startDate,
      end_date: endDate,
      granularity,
    };

    const signature = this.generateSignature('GET', path, queryParams, timestamp);
    queryParams['sign'] = signature;

    const urlParams = new URLSearchParams(queryParams).toString();

    const response = await fetchFunction(`${this.baseUrl}${path}?${urlParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tts-access-token': accessToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get creator performance: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`TikTok Shop API error: ${data.message || 'Unknown error'}`);
    }

    return data.data.performance_data || [];
  }

  /**
   * Get shop performance data (deprecated - use getCreatorPerformance for affiliate integration)
   * @deprecated Use getCreatorPerformance instead for creator/affiliate integration
   */
  async getShopPerformance(
    accessToken: string,
    startDate: string,
    endDate: string,
    granularity: 'DAILY' | 'WEEKLY' | 'MONTHLY' = 'DAILY'
  ): Promise<TikTokShopPerformanceData[]> {
    console.warn('getShopPerformance is deprecated for creator integration. Use getCreatorPerformance instead.');
    return this.getCreatorPerformance(accessToken, startDate, endDate, granularity);
  }

  /**
   * Get product performance data
   */
  async getProductPerformance(
    accessToken: string,
    startDate: string,
    endDate: string,
    productIds?: string[]
  ): Promise<any[]> {
    const timestamp = Math.floor(Date.now() / 1000);
    const path = '/api/v2/data/product_performance';
    
    const queryParams: any = {
      app_key: this.config.apiKey,
      timestamp: timestamp.toString(),
      start_date: startDate,
      end_date: endDate,
    };

    if (productIds && productIds.length > 0) {
      queryParams.product_ids = productIds.join(',');
    }

    const signature = this.generateSignature('GET', path, queryParams, timestamp);
    queryParams['sign'] = signature;

    const urlParams = new URLSearchParams(queryParams).toString();

    const response = await fetchFunction(`${this.baseUrl}${path}?${urlParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tts-access-token': accessToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get product performance: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`TikTok Shop API error: ${data.message || 'Unknown error'}`);
    }

    return data.data.product_performance || [];
  }

  /**
   * Get creator commission data (updated for creator integration)
   */
  async getCreatorCommissions(
    accessToken: string,
    startDate: string,
    endDate: string
  ): Promise<any[]> {
    const timestamp = Math.floor(Date.now() / 1000);
    const path = '/api/v2/creator/commission_list';
    
    const queryParams = {
      app_key: this.config.apiKey,
      timestamp: timestamp.toString(),
      start_date: startDate,
      end_date: endDate,
    };

    const signature = this.generateSignature('GET', path, queryParams, timestamp);
    queryParams['sign'] = signature;

    const urlParams = new URLSearchParams(queryParams).toString();

    const response = await fetchFunction(`${this.baseUrl}${path}?${urlParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tts-access-token': accessToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get creator commissions: ${response.status} - ${errorData}`);
    }

    const data = await response.json();
    
    if (data.code !== 0) {
      throw new Error(`TikTok Shop API error: ${data.message || 'Unknown error'}`);
    }

    return data.data.commission_list || [];
  }

  /**
   * Get affiliate commission data (deprecated - use getCreatorCommissions for creator integration)
   * @deprecated Use getCreatorCommissions instead for creator/affiliate integration
   */
  async getAffiliateCommissions(
    accessToken: string,
    startDate: string,
    endDate: string
  ): Promise<any[]> {
    console.warn('getAffiliateCommissions is deprecated for creator integration. Use getCreatorCommissions instead.');
    return this.getCreatorCommissions(accessToken, startDate, endDate);
  }

  /**
   * Generate OAuth signature for token exchange requests
   */
  private generateOAuthSignature(params: Record<string, string>): string {
    // Create the string to sign by sorting parameters
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}${params[key]}`)
      .join('');

    // Create the full string with app_secret at both ends (OAuth format)
    const stringToSign = `${this.config.apiSecret}${sortedParams}${this.config.apiSecret}`;
    
    // Generate HMAC-SHA256 signature
    return crypto.createHmac('sha256', this.config.apiSecret)
      .update(stringToSign)
      .digest('hex');
  }

  /**
   * Generate signature for API requests
   */
  private generateSignature(method: string, path: string, params: Record<string, any>, timestamp: number): string {
    // Sort parameters
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');

    // Create sign string
    const signString = `${method}${path}${sortedParams}${timestamp}`;
    
    // Generate HMAC-SHA256 signature
    const signature = crypto
      .createHmac('sha256', this.config.apiSecret)
      .update(signString)
      .digest('hex');

    return signature;
  }

  /**
   * Validate granted scopes for creator authorization
   */
  validateGrantedScopes(grantedScopes: string[], requiredScopes: string[]): { valid: boolean; missingScopes: string[] } {
    const missingScopes = requiredScopes.filter(scope => !grantedScopes.includes(scope));
    return {
      valid: missingScopes.length === 0,
      missingScopes
    };
  }

  /**
   * Get required creator scopes for the application
   */
  getRequiredCreatorScopes(): string[] {
    return [
      'creator.base',
      'creator.product',
      'creator.promotion',
      'creator.finance',
      'creator.analytics'
    ];
  }

  /**
   * Validate webhook signature
   */
  validateWebhookSignature(body: string, signature: string, timestamp: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha256', this.config.apiSecret)
      .update(`${timestamp}${body}`)
      .digest('hex');

    return signature === expectedSignature;
  }
}

export default TikTokShopApiService;