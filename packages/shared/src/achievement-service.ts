import { prisma } from '@xact-data/database';
import type { AchievementType } from './types';

interface AchievementConfig {
  type: AchievementType;
  title: string;
  description: string;
  badgeIcon: string;
  badgeColor: string;
  condition: (data: any) => boolean;
  value?: number;
}

export class AchievementService {
  private achievements: AchievementConfig[] = [
    // First Sale Achievement
    {
      type: 'FIRST_SALE',
      title: 'First Sale Champion',
      description: 'Congratulations on your first successful sale!',
      badgeIcon: 'star',
      badgeColor: '#FFD700',
      condition: (data) => data.totalOrders >= 1,
      value: 1,
    },

    // Monthly Milestone Achievements
    {
      type: 'MONTHLY_MILESTONE',
      title: '$1K Month Club',
      description: 'Achieved $1,000+ in monthly commissions',
      badgeIcon: 'trophy',
      badgeColor: '#10B981',
      condition: (data) => data.monthlyCommissions >= 1000,
      value: 1000,
    },
    {
      type: 'MONTHLY_MILESTONE',
      title: '$5K Month Elite',
      description: 'Reached $5,000+ in monthly commissions',
      badgeIcon: 'crown',
      badgeColor: '#3B82F6',
      condition: (data) => data.monthlyCommissions >= 5000,
      value: 5000,
    },
    {
      type: 'MONTHLY_MILESTONE',
      title: '$10K Month Master',
      description: 'Achieved the coveted $10,000+ monthly commissions',
      badgeIcon: 'diamond',
      badgeColor: '#8B5CF6',
      condition: (data) => data.monthlyCommissions >= 10000,
      value: 10000,
    },
    {
      type: 'MONTHLY_MILESTONE',
      title: '$50K Month Legend',
      description: 'Legendary status: $50,000+ in monthly commissions',
      badgeIcon: 'flame',
      badgeColor: '#F59E0B',
      condition: (data) => data.monthlyCommissions >= 50000,
      value: 50000,
    },

    // Streak Achievements
    {
      type: 'STREAK',
      title: 'Week Warrior',
      description: 'Made sales for 7 consecutive days',
      badgeIcon: 'fire',
      badgeColor: '#EF4444',
      condition: (data) => data.consecutiveDaysWithSales >= 7,
      value: 7,
    },
    {
      type: 'STREAK',
      title: 'Month Marathon',
      description: 'Made sales for 30 consecutive days',
      badgeIcon: 'calendar',
      badgeColor: '#F97316',
      condition: (data) => data.consecutiveDaysWithSales >= 30,
      value: 30,
    },

    // Product Master Achievements
    {
      type: 'PRODUCT_MASTER',
      title: 'Product Perfectionist',
      description: 'Achieved 5%+ conversion rate on a product',
      badgeIcon: 'target',
      badgeColor: '#06B6D4',
      condition: (data) => data.maxProductConversionRate >= 5.0,
      value: 5,
    },
    {
      type: 'PRODUCT_MASTER',
      title: 'Conversion King',
      description: 'Achieved 10%+ conversion rate on a product',
      badgeIcon: 'crown',
      badgeColor: '#8B5CF6',
      condition: (data) => data.maxProductConversionRate >= 10.0,
      value: 10,
    },

    // Growth Spurt Achievements
    {
      type: 'GROWTH_SPURT',
      title: 'Rapid Riser',
      description: '100%+ growth in monthly commissions',
      badgeIcon: 'trending-up',
      badgeColor: '#10B981',
      condition: (data) => data.monthlyGrowthRate >= 100,
      value: 100,
    },
    {
      type: 'GROWTH_SPURT',
      title: 'Explosive Growth',
      description: '500%+ growth in monthly commissions',
      badgeIcon: 'rocket',
      badgeColor: '#F59E0B',
      condition: (data) => data.monthlyGrowthRate >= 500,
      value: 500,
    },
  ];

  /**
   * Check and award achievements for a user
   */
  async checkAndAwardAchievements(userId: string): Promise<void> {
    const userData = await this.getUserAchievementData(userId);
    const existingAchievements = await this.getExistingAchievements(userId);
    
    for (const achievement of this.achievements) {
      // Skip if user already has this achievement
      const hasAchievement = existingAchievements.some(
        a => a.type === achievement.type && 
        (!achievement.value || a.value === achievement.value)
      );
      
      if (hasAchievement) continue;

      // Check if achievement condition is met
      if (achievement.condition(userData)) {
        await this.awardAchievement(userId, achievement);
      }
    }
  }

  /**
   * Get user data needed for achievement calculations
   */
  private async getUserAchievementData(userId: string): Promise<any> {
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get current month analytics
    const currentMonthAnalytics = await prisma.analytics.findMany({
      where: {
        userId,
        date: { gte: currentMonthStart },
      },
    });

    // Get previous month analytics for growth calculation
    const previousMonthAnalytics = await prisma.analytics.findMany({
      where: {
        userId,
        date: {
          gte: previousMonthStart,
          lte: previousMonthEnd,
        },
      },
    });

    // Get product performance data
    const productPerformance = await prisma.productPerformance.findMany({
      where: {
        userId,
        date: { gte: currentMonthStart },
      },
    });

    // Calculate totals and metrics
    const currentMonthCommissions = currentMonthAnalytics.reduce(
      (sum, a) => sum + a.commissions, 0
    );
    const previousMonthCommissions = previousMonthAnalytics.reduce(
      (sum, a) => sum + a.commissions, 0
    );
    const totalOrders = currentMonthAnalytics.reduce(
      (sum, a) => sum + a.orders, 0
    );

    // Calculate growth rate
    const monthlyGrowthRate = previousMonthCommissions > 0
      ? ((currentMonthCommissions - previousMonthCommissions) / previousMonthCommissions) * 100
      : 0;

    // Calculate max conversion rate
    const maxProductConversionRate = productPerformance.length > 0
      ? Math.max(...productPerformance.map(p => p.conversionRate))
      : 0;

    // Calculate consecutive days with sales
    const consecutiveDaysWithSales = await this.calculateConsecutiveDaysWithSales(userId);

    return {
      monthlyCommissions: currentMonthCommissions,
      previousMonthCommissions,
      totalOrders,
      monthlyGrowthRate,
      maxProductConversionRate,
      consecutiveDaysWithSales,
    };
  }

  /**
   * Calculate consecutive days with sales
   */
  private async calculateConsecutiveDaysWithSales(userId: string): Promise<number> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const analytics = await prisma.analytics.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
        orders: { gt: 0 },
      },
      orderBy: { date: 'desc' },
    });

    if (analytics.length === 0) return 0;

    let consecutiveDays = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Check for consecutive days starting from today
    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(checkDate.getDate() - i);
      
      const hasOrdersOnDate = analytics.some(a => {
        const analyticsDate = new Date(a.date);
        analyticsDate.setHours(0, 0, 0, 0);
        return analyticsDate.getTime() === checkDate.getTime();
      });

      if (hasOrdersOnDate) {
        consecutiveDays++;
      } else {
        break;
      }
    }

    return consecutiveDays;
  }

  /**
   * Get existing achievements for a user
   */
  private async getExistingAchievements(userId: string) {
    return prisma.achievement.findMany({
      where: { userId },
    });
  }

  /**
   * Award an achievement to a user
   */
  private async awardAchievement(userId: string, achievement: AchievementConfig): Promise<void> {
    try {
      await prisma.achievement.create({
        data: {
          userId,
          type: achievement.type,
          title: achievement.title,
          description: achievement.description,
          badgeIcon: achievement.badgeIcon,
          badgeColor: achievement.badgeColor,
          value: achievement.value,
          unlockedAt: new Date(),
        },
      });

      console.log(`🏆 Achievement awarded: ${achievement.title} to user ${userId}`);
    } catch (error) {
      console.error('Failed to award achievement:', error);
    }
  }

  /**
   * Get achievement progress for a user
   */
  async getAchievementProgress(userId: string): Promise<any> {
    const userData = await this.getUserAchievementData(userId);
    const existingAchievements = await this.getExistingAchievements(userId);
    
    const progress = this.achievements.map(achievement => {
      const hasAchievement = existingAchievements.some(
        a => a.type === achievement.type && 
        (!achievement.value || a.value === achievement.value)
      );

      const conditionMet = achievement.condition(userData);
      
      // Calculate progress percentage for certain achievements
      let progressPercentage = 0;
      if (!hasAchievement && achievement.value) {
        switch (achievement.type) {
          case 'MONTHLY_MILESTONE':
            progressPercentage = Math.min(
              (userData.monthlyCommissions / achievement.value) * 100, 
              100
            );
            break;
          case 'STREAK':
            progressPercentage = Math.min(
              (userData.consecutiveDaysWithSales / achievement.value) * 100, 
              100
            );
            break;
          case 'PRODUCT_MASTER':
          case 'CONVERSION_KING':
            progressPercentage = Math.min(
              (userData.maxProductConversionRate / achievement.value) * 100, 
              100
            );
            break;
          case 'GROWTH_SPURT':
            progressPercentage = Math.min(
              (userData.monthlyGrowthRate / achievement.value) * 100, 
              100
            );
            break;
        }
      }

      return {
        ...achievement,
        unlocked: hasAchievement,
        conditionMet,
        progressPercentage: hasAchievement ? 100 : progressPercentage,
        currentValue: this.getCurrentValue(userData, achievement),
      };
    });

    return {
      achievements: progress,
      totalUnlocked: existingAchievements.length,
      totalAvailable: this.achievements.length,
      completionPercentage: (existingAchievements.length / this.achievements.length) * 100,
    };
  }

  /**
   * Get current value for achievement progress
   */
  private getCurrentValue(userData: any, achievement: AchievementConfig): number {
    switch (achievement.type) {
      case 'MONTHLY_MILESTONE':
        return userData.monthlyCommissions;
      case 'STREAK':
        return userData.consecutiveDaysWithSales;
      case 'PRODUCT_MASTER':
      case 'CONVERSION_KING':
        return userData.maxProductConversionRate;
      case 'GROWTH_SPURT':
        return userData.monthlyGrowthRate;
      case 'FIRST_SALE':
        return userData.totalOrders;
      default:
        return 0;
    }
  }
}