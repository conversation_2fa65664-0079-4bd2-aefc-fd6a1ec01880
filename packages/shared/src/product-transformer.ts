import { TikTokProduct } from './tiktok-api-types';
import { TikTokApiService } from './tiktok-api-service';

/**
 * Transform TikTok API product to our database format
 */
export function transformTikTokProduct(tikTokProduct: TikTokProduct) {
  const commissionRate = TikTokApiService.parseCommissionRate(tikTokProduct.commission_rate);
  const price = TikTokApiService.parsePrice(tikTokProduct.price_usd);
  const estimatedGMV = TikTokApiService.calculateEstimatedGMV(tikTokProduct.total_sales_usd);
  const creatorsCarrying = TikTokApiService.estimateCreatorsCarrying(
    tikTokProduct.related_videos,
    tikTokProduct.related_author
  );
  
  // Calculate trend score
  const trendScore = TikTokApiService.calculateTrendScore(
    tikTokProduct.sold_count,
    tikTokProduct.week_sold_count,
    tikTokProduct.total_sales_usd,
    tikTokProduct.product_rating,
    tikTokProduct.related_videos
  );

  // Determine primary category
  const category = tikTokProduct.category1 || tikTokProduct.category2 || tikTokProduct.category3 || 'Uncategorized';

  // Get image URL from sale_props (actual working URLs)
  let imageUrl: string | undefined = undefined;
  
  // Look for the first available image URL in sale_props
  for (const saleProp of tikTokProduct.sale_props) {
    if (saleProp.has_image && saleProp.sale_prop_values.length > 0) {
      const firstValue = saleProp.sale_prop_values[0];
      if (firstValue.image && firstValue.image.startsWith('http')) {
        imageUrl = firstValue.image;
        break;
      }
    }
  }
  
  // If no image found in sale_props, we could potentially fall back to privatization
  // but those would need a proper CDN URL which we don't have
  if (!imageUrl && tikTokProduct.images_privatization.length > 0) {
    // Note: This is a fallback but won't work without proper CDN URL
    // imageUrl = `https://tiktok-cdn.example.com/${tikTokProduct.images_privatization[0]}`;
    imageUrl = undefined; // Keep as undefined since we don't have the proper CDN URL
  }

  // Generate affiliate link (this would typically come from TikTok Shop API or be generated)
  const affiliateLink = `https://tiktokshop.com/affiliate/${tikTokProduct.id}`;

  return {
    // Core fields
    id: tikTokProduct.id,
    title: tikTokProduct.title,
    category,
    commissionRate,
    soldIn24h: tikTokProduct.week_sold_count, // Using week sold as proxy for 24h
    creatorsCarrying,
    estimatedGMV,
    trendScore,
    affiliateLink,
    imageUrl,
    price,
    
    // TikTok API specific fields
    tiktokProductId: tikTokProduct.id,
    rank: tikTokProduct.rank,
    category1: tikTokProduct.category1,
    category2: tikTokProduct.category2,
    category3: tikTokProduct.category3,
    countryCode: tikTokProduct.country_code,
    soldCount: tikTokProduct.sold_count,
    totalSales: tikTokProduct.total_sales_usd,
    weekSoldCount: tikTokProduct.week_sold_count,
    weekSales: tikTokProduct.week_sales_usd,
    productRating: tikTokProduct.product_rating,
    sellerId: tikTokProduct.seller_id,
    sellerName: tikTokProduct.seller_name,
    shopId: tikTokProduct.shop.shop_id,
    shopName: tikTokProduct.shop.shop_name,
    stock: tikTokProduct.stock,
    relatedVideos: tikTokProduct.related_videos,
    relatedAuthors: <AUTHORS>
    freeShipping: tikTokProduct.free_shipping,
    lastTimeStamp: BigInt(tikTokProduct.last_time_stamp),
  };
}

/**
 * Transform multiple TikTok products
 */
export function transformTikTokProducts(tikTokProducts: TikTokProduct[]) {
  return tikTokProducts.map(transformTikTokProduct);
}