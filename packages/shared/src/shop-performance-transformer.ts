import { 
  TikTokShopPerformanceResponse, 
  TikTokShopPerformanceInterval 
} from './tiktok-api-types';
import { ShopPerformance, ShopPerformanceSummary } from './types';

/**
 * Transform TikTok Shop Performance API response to database format
 */
export function transformTikTokShopPerformanceToDb(
  userId: string,
  response: TikTokShopPerformanceResponse,
  granularity: string
): ShopPerformance[] {
  const results: ShopPerformance[] = [];
  
  // Transform main intervals
  response.data.performance.intervals.forEach(interval => {
    results.push(transformIntervalToShopPerformance(userId, interval, granularity, false, response.request_id));
  });
  
  // Transform comparison intervals if they exist
  if (response.data.performance.comparison_intervals) {
    response.data.performance.comparison_intervals.forEach(interval => {
      results.push(transformIntervalToShopPerformance(userId, interval, granularity, true, response.request_id));
    });
  }
  
  return results;
}

/**
 * Transform individual interval to ShopPerformance
 */
function transformIntervalToShopPerformance(
  userId: string,
  interval: TikTokShopPerformanceInterval,
  granularity: string,
  isComparison: boolean,
  requestId: string
): ShopPerformance {
  // Parse GMV breakdown
  const gmvLive = parseFloat(interval.gmv_breakdowns.find(b => b.type === 'LIVE')?.amount || '0');
  const gmvVideo = parseFloat(interval.gmv_breakdowns.find(b => b.type === 'VIDEO')?.amount || '0');
  const gmvShop = parseFloat(interval.gmv_breakdowns.find(b => b.type === 'SHOP')?.amount || '0');
  
  // Parse buyer breakdown
  const buyersLive = interval.buyer_breakdowns.find(b => b.type === 'LIVE')?.amount || 0;
  const buyersVideo = interval.buyer_breakdowns.find(b => b.type === 'VIDEO')?.amount || 0;
  const buyersShop = interval.buyer_breakdowns.find(b => b.type === 'SHOP')?.amount || 0;
  
  // Parse impression breakdown
  const impressionsLive = interval.product_impression_breakdowns.find(b => b.type === 'LIVE')?.amount || 0;
  const impressionsVideo = interval.product_impression_breakdowns.find(b => b.type === 'VIDEO')?.amount || 0;
  const impressionsShop = interval.product_impression_breakdowns.find(b => b.type === 'SHOP')?.amount || 0;
  
  // Parse page view breakdown
  const pageViewsLive = interval.product_page_view_breakdowns.find(b => b.type === 'LIVE')?.amount || 0;
  const pageViewsVideo = interval.product_page_view_breakdowns.find(b => b.type === 'VIDEO')?.amount || 0;
  const pageViewsShop = interval.product_page_view_breakdowns.find(b => b.type === 'SHOP')?.amount || 0;
  
  const gmv = parseFloat(interval.gmv.amount);
  const avgOrderValue = parseFloat(interval.avg_order_value.amount);
  const refunds = parseFloat(interval.refunds.amount);
  
  // Calculate conversion rate
  const conversionRate = interval.product_page_views > 0 
    ? (interval.orders / interval.product_page_views) * 100 
    : 0;
  
  // Estimate commissions (10% default commission rate)
  const estimatedCommissions = gmv * 0.1;
  
  return {
    id: '', // Will be set by Prisma
    userId,
    date: new Date(interval.start_date),
    granularity,
    gmv,
    currency: interval.gmv.currency,
    orders: interval.orders,
    skuOrders: interval.sku_orders,
    unitsSold: interval.units_sold,
    buyers: interval.buyers,
    avgOrderValue,
    productImpressions: interval.product_impressions,
    productPageViews: interval.product_page_views,
    avgProductPageVisitors: interval.avg_product_page_visitors,
    gmvLive,
    gmvVideo,
    gmvShop,
    buyersLive,
    buyersVideo,
    buyersShop,
    impressionsLive,
    impressionsVideo,
    impressionsShop,
    pageViewsLive,
    pageViewsVideo,
    pageViewsShop,
    refunds,
    cancellationsAndReturns: interval.cancellations_and_returns,
    conversionRate,
    estimatedCommissions,
    isComparison,
    tiktokRequestId: requestId,
    rawData: interval as unknown as Record<string, unknown>,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

/**
 * Transform database ShopPerformance records to dashboard summary
 */
export function transformShopPerformanceToSummary(
  currentPeriod: any[],
  comparisonPeriod?: any[]
): ShopPerformanceSummary {
  // Aggregate current period data
  const current = aggregateShopPerformance(currentPeriod.filter(p => !p.isComparison));
  
  // Aggregate comparison period data if available
  const comparison = comparisonPeriod ? aggregateShopPerformance(comparisonPeriod.filter(p => p.isComparison)) : undefined;
  
  // Calculate growth if comparison data is available
  const growth = comparison ? calculateGrowth(current, comparison) : undefined;
  
  // Get period info from the first record
  const firstRecord = currentPeriod[0];
  const lastRecord = currentPeriod[currentPeriod.length - 1];
  
  return {
    current,
    comparison,
    growth,
    breakdown: {
      live: {
        gmv: current.gmv > 0 ? currentPeriod.reduce((sum, p) => sum + p.gmvLive, 0) : 0,
        buyers: currentPeriod.reduce((sum, p) => sum + p.buyersLive, 0),
        impressions: currentPeriod.reduce((sum, p) => sum + p.impressionsLive, 0),
        pageViews: currentPeriod.reduce((sum, p) => sum + p.pageViewsLive, 0),
      },
      video: {
        gmv: current.gmv > 0 ? currentPeriod.reduce((sum, p) => sum + p.gmvVideo, 0) : 0,
        buyers: currentPeriod.reduce((sum, p) => sum + p.buyersVideo, 0),
        impressions: currentPeriod.reduce((sum, p) => sum + p.impressionsVideo, 0),
        pageViews: currentPeriod.reduce((sum, p) => sum + p.pageViewsVideo, 0),
      },
      shop: {
        gmv: current.gmv > 0 ? currentPeriod.reduce((sum, p) => sum + p.gmvShop, 0) : 0,
        buyers: currentPeriod.reduce((sum, p) => sum + p.buyersShop, 0),
        impressions: currentPeriod.reduce((sum, p) => sum + p.impressionsShop, 0),
        pageViews: currentPeriod.reduce((sum, p) => sum + p.pageViewsShop, 0),
      },
    },
    period: {
      startDate: firstRecord?.date.toISOString().split('T')[0] || '',
      endDate: lastRecord?.date.toISOString().split('T')[0] || '',
      granularity: firstRecord?.granularity || 'DAILY',
    },
  };
}

/**
 * Aggregate shop performance data
 */
function aggregateShopPerformance(records: any[]) {
  const totals = records.reduce(
    (acc, record) => ({
      gmv: acc.gmv + record.gmv,
      orders: acc.orders + record.orders,
      buyers: acc.buyers + record.buyers,
      unitsSold: acc.unitsSold + record.unitsSold,
      estimatedCommissions: acc.estimatedCommissions + record.estimatedCommissions,
      productPageViews: acc.productPageViews + record.productPageViews,
    }),
    {
      gmv: 0,
      orders: 0,
      buyers: 0,
      unitsSold: 0,
      estimatedCommissions: 0,
      productPageViews: 0,
    }
  );
  
  return {
    gmv: totals.gmv,
    orders: totals.orders,
    avgOrderValue: totals.orders > 0 ? totals.gmv / totals.orders : 0,
    conversionRate: totals.productPageViews > 0 ? (totals.orders / totals.productPageViews) * 100 : 0,
    estimatedCommissions: totals.estimatedCommissions,
    buyers: totals.buyers,
    unitsSold: totals.unitsSold,
  };
}

/**
 * Calculate growth percentages
 */
function calculateGrowth(current: any, comparison: any) {
  return {
    gmv: comparison.gmv > 0 ? ((current.gmv - comparison.gmv) / comparison.gmv) * 100 : 0,
    orders: comparison.orders > 0 ? ((current.orders - comparison.orders) / comparison.orders) * 100 : 0,
    avgOrderValue: comparison.avgOrderValue > 0 ? ((current.avgOrderValue - comparison.avgOrderValue) / comparison.avgOrderValue) * 100 : 0,
    conversionRate: comparison.conversionRate > 0 ? ((current.conversionRate - comparison.conversionRate) / comparison.conversionRate) * 100 : 0,
    estimatedCommissions: comparison.estimatedCommissions > 0 ? ((current.estimatedCommissions - comparison.estimatedCommissions) / comparison.estimatedCommissions) * 100 : 0,
    buyers: comparison.buyers > 0 ? ((current.buyers - comparison.buyers) / comparison.buyers) * 100 : 0,
    unitsSold: comparison.unitsSold > 0 ? ((current.unitsSold - comparison.unitsSold) / comparison.unitsSold) * 100 : 0,
  };
}