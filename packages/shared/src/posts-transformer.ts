import { TikTokPostItem } from './tiktok-api-types';
import { TikTokApiService } from './tiktok-api-service';

/**
 * Transform TikTok post data to CreatorVideo database format
 */
export function transformTikTokPostToCreatorVideo(post: TikTokPostItem, creatorId: string) {
  // Extract hashtags from textExtra
  const hashtags = post.textExtra
    ?.filter(extra => extra.type === 1) // Type 1 is hashtag
    .map(extra => extra.hashtagName)
    .filter(Boolean) || [];

  // Calculate engagement rate
  const engagementRate = TikTokApiService.calculatePostEngagementRate(
    post.stats.playCount,
    post.stats.diggCount,
    post.stats.shareCount,
    post.stats.commentCount
  );

  // Extract thumbnail URL from video data
  const thumbnailUrl = post.video?.bitrateInfo?.[0]?.PlayAddr?.UrlList?.[0] || null;

  return {
    id: post.id,
    creatorId,
    title: post.desc?.substring(0, 255) || null, // Truncate to fit DB constraints
    description: post.desc || null,
    viewCount: post.stats.playCount,
    likeCount: post.stats.diggCount,
    shareCount: post.stats.shareCount,
    commentCount: post.stats.commentCount,
    playTime: post.music?.duration || null,
    publishedAt: new Date(post.createTime * 1000), // Convert Unix timestamp to Date
    thumbnailUrl,
    videoUrl: post.video?.bitrateInfo?.[0]?.PlayAddr?.UrlList?.[0] || null,
    hashtags: hashtags.length > 0 ? hashtags : null,
    mentionedProducts: null, // Could be extracted from description if needed
    engagementRate,
  };
}

/**
 * Transform multiple TikTok posts to CreatorVideo format
 */
export function transformTikTokPostsToCreatorVideos(posts: TikTokPostItem[], creatorId: string) {
  return posts.map(post => transformTikTokPostToCreatorVideo(post, creatorId));
}

/**
 * Extract content insights from posts for AI analysis
 */
export function extractPostInsights(posts: TikTokPostItem[]) {
  const insights = {
    totalPosts: posts.length,
    averageViews: 0,
    averageLikes: 0,
    averageShares: 0,
    averageComments: 0,
    averageEngagement: 0,
    topHashtags: [] as string[],
    contentThemes: [] as string[],
    postFrequency: 0, // posts per day
    bestPerformingPosts: [] as any[],
    recentTrends: [] as string[],
  };

  if (posts.length === 0) return insights;

  // Calculate averages
  const totalViews = posts.reduce((sum, post) => sum + post.stats.playCount, 0);
  const totalLikes = posts.reduce((sum, post) => sum + post.stats.diggCount, 0);
  const totalShares = posts.reduce((sum, post) => sum + post.stats.shareCount, 0);
  const totalComments = posts.reduce((sum, post) => sum + post.stats.commentCount, 0);

  insights.averageViews = Math.round(totalViews / posts.length);
  insights.averageLikes = Math.round(totalLikes / posts.length);
  insights.averageShares = Math.round(totalShares / posts.length);
  insights.averageComments = Math.round(totalComments / posts.length);
  insights.averageEngagement = (insights.averageLikes + insights.averageShares + insights.averageComments) / insights.averageViews * 100;

  // Extract hashtags
  const hashtagCounts: Record<string, number> = {};
  posts.forEach(post => {
    post.textExtra?.forEach(extra => {
      if (extra.type === 1 && extra.hashtagName) {
        hashtagCounts[extra.hashtagName] = (hashtagCounts[extra.hashtagName] || 0) + 1;
      }
    });
  });

  insights.topHashtags = Object.entries(hashtagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([hashtag]) => hashtag);

  // Find best performing posts (top 20% by views)
  const sortedByViews = posts
    .map(post => ({
      id: post.id,
      desc: post.desc,
      views: post.stats.playCount,
      likes: post.stats.diggCount,
      engagementRate: TikTokApiService.calculatePostEngagementRate(
        post.stats.playCount,
        post.stats.diggCount,
        post.stats.shareCount,
        post.stats.commentCount
      ),
      hashtags: post.textExtra?.filter(e => e.type === 1).map(e => e.hashtagName).filter(Boolean) || []
    }))
    .sort((a, b) => b.views - a.views);

  insights.bestPerformingPosts = sortedByViews.slice(0, Math.ceil(posts.length * 0.2));

  // Calculate posting frequency (posts per day)
  if (posts.length > 1) {
    const oldestPost = Math.min(...posts.map(p => p.createTime));
    const newestPost = Math.max(...posts.map(p => p.createTime));
    const daysDiff = (newestPost - oldestPost) / (24 * 60 * 60); // Convert seconds to days
    insights.postFrequency = posts.length / Math.max(daysDiff, 1);
  }

  // Extract content themes from descriptions
  const descriptions = posts.map(p => p.desc).filter(Boolean);
  const words = descriptions
    .join(' ')
    .toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 3 && !word.startsWith('#') && !word.startsWith('@'));

  const wordCounts: Record<string, number> = {};
  words.forEach(word => {
    wordCounts[word] = (wordCounts[word] || 0) + 1;
  });

  insights.contentThemes = Object.entries(wordCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);

  // Recent trends (hashtags from last 7 days)
  const weekAgo = Date.now() / 1000 - (7 * 24 * 60 * 60);
  const recentPosts = posts.filter(p => p.createTime > weekAgo);
  const recentHashtags: Record<string, number> = {};
  
  recentPosts.forEach(post => {
    post.textExtra?.forEach(extra => {
      if (extra.type === 1 && extra.hashtagName) {
        recentHashtags[extra.hashtagName] = (recentHashtags[extra.hashtagName] || 0) + 1;
      }
    });
  });

  insights.recentTrends = Object.entries(recentHashtags)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([hashtag]) => hashtag);

  return insights;
}