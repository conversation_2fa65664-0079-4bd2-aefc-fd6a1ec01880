# This file has been removed as part of Supabase migration
# All database operations now use Supabase instead of local PostgreSQL
#
# To start development:
# 1. Ensure .env files are configured with Supabase credentials
# 2. Run: pnpm dev
#
# For database operations:
# cd packages/database
# pnpm db:push    # Push schema to Supabase
# pnpm db:seed    # Seed with initial data
# pnpm db:studio  # Open Prisma Studio