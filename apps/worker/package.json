{"name": "worker", "version": "1.0.0", "description": "Background worker for trend computation and alerts", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "pnpm --filter @xact-data/database build && pnpm --filter @xact-data/ai-wrapper build && pnpm --filter @xact-data/shared build && tsc", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"node-cron": "^3.0.3", "dotenv": "^16.3.1", "@xact-data/shared": "workspace:*", "@xact-data/database": "workspace:*", "@xact-data/ai-wrapper": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "@types/node-cron": "^3.0.11", "typescript": "^5.1.0", "tsx": "^4.0.0"}}