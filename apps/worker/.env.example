# Database Configuration
DATABASE_URL="****************************************************************************************************/postgres?pgbouncer=true"

# Direct connection to the database. Used for migrations
DIRECT_URL="****************************************************************************************************/postgres"

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://yufcxcwnwmlcykqpugkl.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.JvQ9PgX1I5LABhJN0BJBO0W_GVZb4Nn3vSopKmAUGFM"

# Server Configuration
NODE_ENV=development
PORT=8081

# AI Providers (get your API keys)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# TikTok Shop OAuth (for future integration)
TIKTOK_SHOP_CLIENT_ID=your_tiktok_shop_client_id
TIKTOK_SHOP_CLIENT_SECRET=your_tiktok_shop_client_secret