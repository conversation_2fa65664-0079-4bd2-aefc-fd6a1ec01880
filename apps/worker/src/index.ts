import * as cron from 'node-cron';
import dotenv from 'dotenv';
import { prisma } from '@xact-data/database';
import { trendComputationJob } from './jobs/trend-computation';
import { alertFireJob } from './jobs/alert-fire';
import { dataCleanupJob } from './jobs/data-cleanup';
import { tikTokSyncJob } from './jobs/tiktok-sync';
import { competitorMonitoringJob, checkMilestones, cleanupOldAlerts } from './jobs/competitor-monitoring';
import { affiliateInsightsJob, goalProgressUpdateJob, affiliateDataCleanupJob } from './jobs/affiliate-insights';

// Load environment variables
dotenv.config();

console.log('🚀 Starting Xact Data Worker...');
console.log(`📊 Environment: ${process.env.NODE_ENV}`);

// Check if this is the first run and run TikTok sync immediately if needed
async function checkAndRunInitialSync() {
  try {
    console.log('🔍 Checking if initial TikTok sync is needed...');
    
    const productCount = await prisma.product.count();
    
    if (productCount === 0) {
      console.log('🎯 No products found in database. Running initial TikTok sync...');
      await tikTokSyncJob();
      console.log('✅ Initial TikTok sync completed');
    } else {
      console.log(`📊 Found ${productCount} products in database. Skipping initial sync.`);
    }
  } catch (error) {
    console.error('❌ Initial sync check failed:', error);
  }
}

// Run initial sync check
checkAndRunInitialSync();

// Schedule trend computation job - runs every 15 minutes
cron.schedule('*/15 * * * *', async () => {
  console.log('⚡ Running trend computation job...');
  try {
    await trendComputationJob();
    console.log('✅ Trend computation completed');
  } catch (error) {
    console.error('❌ Trend computation failed:', error);
  }
});

// Schedule alert fire job - runs every 5 minutes
cron.schedule('*/5 * * * *', async () => {
  console.log('🔔 Running alert fire job...');
  try {
    await alertFireJob();
    console.log('✅ Alert fire job completed');
  } catch (error) {
    console.error('❌ Alert fire job failed:', error);
  }
});

// Schedule TikTok sync job - runs daily at 1 AM
cron.schedule('0 1 * * *', async () => {
  console.log('🚀 Running TikTok sync job...');
  try {
    await tikTokSyncJob();
    console.log('✅ TikTok sync completed');
  } catch (error) {
    console.error('❌ TikTok sync failed:', error);
  }
});

// Schedule data cleanup job - runs daily at 2 AM
cron.schedule('0 2 * * *', async () => {
  console.log('🧹 Running data cleanup job...');
  try {
    await dataCleanupJob();
    console.log('✅ Data cleanup completed');
  } catch (error) {
    console.error('❌ Data cleanup failed:', error);
  }
});

// Schedule competitor monitoring job - runs every 2 hours
cron.schedule('0 */2 * * *', async () => {
  console.log('🔍 Running competitor monitoring job...');
  try {
    await competitorMonitoringJob();
    console.log('✅ Competitor monitoring completed');
  } catch (error) {
    console.error('❌ Competitor monitoring failed:', error);
  }
});

// Schedule milestone check - runs every 6 hours
cron.schedule('0 */6 * * *', async () => {
  console.log('🎯 Running milestone check...');
  try {
    await checkMilestones();
    console.log('✅ Milestone check completed');
  } catch (error) {
    console.error('❌ Milestone check failed:', error);
  }
});

// Schedule old alerts cleanup - runs daily at 3 AM
cron.schedule('0 3 * * *', async () => {
  console.log('🧹 Running old alerts cleanup...');
  try {
    await cleanupOldAlerts();
    console.log('✅ Old alerts cleanup completed');
  } catch (error) {
    console.error('❌ Old alerts cleanup failed:', error);
  }
});

// Schedule affiliate insights job - runs daily at 9 AM
cron.schedule('0 9 * * *', async () => {
  console.log('🤖 Running affiliate insights job...');
  try {
    await affiliateInsightsJob();
    console.log('✅ Affiliate insights job completed');
  } catch (error) {
    console.error('❌ Affiliate insights job failed:', error);
  }
});

// Schedule goal progress update - runs every 4 hours
cron.schedule('0 */4 * * *', async () => {
  console.log('🎯 Running goal progress update...');
  try {
    await goalProgressUpdateJob();
    console.log('✅ Goal progress update completed');
  } catch (error) {
    console.error('❌ Goal progress update failed:', error);
  }
});

// Schedule affiliate data cleanup - runs daily at 4 AM
cron.schedule('0 4 * * *', async () => {
  console.log('🧹 Running affiliate data cleanup...');
  try {
    await affiliateDataCleanupJob();
    console.log('✅ Affiliate data cleanup completed');
  } catch (error) {
    console.error('❌ Affiliate data cleanup failed:', error);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Shutting down worker gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('🛑 Shutting down worker gracefully...');
  process.exit(0);
});

console.log('✅ Worker started successfully');
console.log('📅 Scheduled jobs:');
console.log('  - Trend computation: every 15 minutes');
console.log('  - Alert fires: every 5 minutes');
console.log('  - TikTok sync: daily at 1 AM');
console.log('  - Data cleanup: daily at 2 AM');
console.log('  - Competitor monitoring: every 2 hours');
console.log('  - Milestone checks: every 6 hours');
console.log('  - Old alerts cleanup: daily at 3 AM');
console.log('  - Affiliate insights: daily at 9 AM');
console.log('  - Goal progress update: every 4 hours');
console.log('  - Affiliate data cleanup: daily at 4 AM');