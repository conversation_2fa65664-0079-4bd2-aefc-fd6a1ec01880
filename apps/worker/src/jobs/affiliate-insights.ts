import { prisma } from '@xact-data/database';
import { AIGrowthCoachService, AchievementService } from '@xact-data/shared';

/**
 * Daily job to generate AI insights and check achievements for all users
 */
export async function affiliateInsightsJob() {
  console.log('🤖 Starting daily affiliate insights generation...');

  try {
    // Get all active users (users with recent analytics data)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeUsers = await prisma.user.findMany({
      where: {
        analytics: {
          some: {
            date: { gte: thirtyDaysAgo }
          }
        }
      },
      select: { id: true, email: true }
    });

    console.log(`📊 Found ${activeUsers.length} active users for insight generation`);

    const aiGrowthCoach = new AIGrowthCoachService();
    const achievementService = new AchievementService();

    let insightsGenerated = 0;
    let achievementsAwarded = 0;

    for (const user of activeUsers) {
      try {
        console.log(`🔍 Processing insights for user ${user.id}`);

        // Check and award achievements first
        await achievementService.checkAndAwardAchievements(user.id);
        
        // Check if user needs new insights (don't spam with daily insights)
        const recentInsights = await prisma.aIInsight.findMany({
          where: {
            userId: user.id,
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
            }
          }
        });

        // Only generate insights if user doesn't have recent ones
        if (recentInsights.length === 0) {
          // Generate weekly action plan on Mondays
          const today = new Date();
          const isMonday = today.getDay() === 1;

          if (isMonday) {
            console.log(`📋 Generating weekly action plan for user ${user.id}`);
            const actionPlan = await aiGrowthCoach.generateWeeklyActionPlan(user.id);
            
            await prisma.aIInsight.create({
              data: {
                userId: user.id,
                insightType: actionPlan.insightType,
                title: actionPlan.title,
                content: actionPlan.content,
                priority: actionPlan.priority,
                actionItems: actionPlan.actionItems,
                metadata: actionPlan.metadata,
                validUntil: actionPlan.validUntil,
              },
            });
            insightsGenerated++;
          }

          // Generate performance summary on the 1st of each month
          const isFirstOfMonth = today.getDate() === 1;

          if (isFirstOfMonth) {
            console.log(`📈 Generating performance summary for user ${user.id}`);
            const performanceSummary = await aiGrowthCoach.generatePerformanceSummary(user.id);
            
            await prisma.aIInsight.create({
              data: {
                userId: user.id,
                insightType: performanceSummary.insightType,
                title: performanceSummary.title,
                content: performanceSummary.content,
                priority: performanceSummary.priority,
                actionItems: performanceSummary.actionItems,
                metadata: performanceSummary.metadata,
                validUntil: performanceSummary.validUntil,
              },
            });
            insightsGenerated++;
          }

          // Generate growth opportunities for users with declining performance
          const recentAnalytics = await prisma.analytics.findMany({
            where: {
              userId: user.id,
              date: { gte: thirtyDaysAgo }
            },
            orderBy: { date: 'desc' },
            take: 14 // Last 2 weeks
          });

          if (recentAnalytics.length >= 7) {
            const firstWeek = recentAnalytics.slice(0, 7);
            const secondWeek = recentAnalytics.slice(7, 14);
            
            const firstWeekCommissions = firstWeek.reduce((sum, a) => sum + a.commissions, 0);
            const secondWeekCommissions = secondWeek.reduce((sum, a) => sum + a.commissions, 0);
            
            // If performance declined by more than 20%, generate growth opportunity insight
            if (secondWeekCommissions > 0 && 
                (firstWeekCommissions / secondWeekCommissions) < 0.8) {
              console.log(`💡 Generating growth opportunity for declining user ${user.id}`);
              const growthOpportunity = await aiGrowthCoach.generateGrowthOpportunity(user.id);
              
              await prisma.aIInsight.create({
                data: {
                  userId: user.id,
                  insightType: growthOpportunity.insightType,
                  title: growthOpportunity.title,
                  content: growthOpportunity.content,
                  priority: 'high', // High priority for declining performance
                  actionItems: growthOpportunity.actionItems,
                  metadata: growthOpportunity.metadata,
                  validUntil: growthOpportunity.validUntil,
                },
              });
              insightsGenerated++;
            }
          }
        }

        // Small delay to avoid overwhelming the AI service
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`❌ Failed to process insights for user ${user.id}:`, error);
        // Continue with next user
      }
    }

    console.log(`✅ Daily affiliate insights job completed:`);
    console.log(`   📊 Processed ${activeUsers.length} users`);
    console.log(`   🤖 Generated ${insightsGenerated} AI insights`);
    console.log(`   🏆 Checked achievements for all users`);

  } catch (error) {
    console.error('❌ Daily affiliate insights job failed:', error);
    throw error;
  }
}

/**
 * Job to update goal progress based on latest analytics
 */
export async function goalProgressUpdateJob() {
  console.log('🎯 Starting goal progress update job...');

  try {
    // Get all active goals
    const activeGoals = await prisma.affiliateGoal.findMany({
      where: {
        isCompleted: false
      },
      include: {
        user: true
      }
    });

    console.log(`📊 Found ${activeGoals.length} active goals to update`);

    let goalsUpdated = 0;
    let goalsCompleted = 0;

    for (const goal of activeGoals) {
      try {
        let currentValue = 0;

        // Calculate current value based on goal type
        const currentMonth = new Date();
        currentMonth.setDate(1); // First day of current month
        currentMonth.setHours(0, 0, 0, 0);

        switch (goal.goalType) {
          case 'MONTHLY_GMV':
            const gmvAnalytics = await prisma.analytics.findMany({
              where: {
                userId: goal.userId,
                date: { gte: currentMonth }
              }
            });
            currentValue = gmvAnalytics.reduce((sum, a) => sum + a.gmv, 0);
            break;

          case 'MONTHLY_COMMISSIONS':
            const commissionAnalytics = await prisma.analytics.findMany({
              where: {
                userId: goal.userId,
                date: { gte: currentMonth }
              }
            });
            currentValue = commissionAnalytics.reduce((sum, a) => sum + a.commissions, 0);
            break;

          case 'CONVERSION_RATE':
            const conversionAnalytics = await prisma.analytics.findMany({
              where: {
                userId: goal.userId,
                date: { gte: currentMonth }
              }
            });
            if (conversionAnalytics.length > 0) {
              currentValue = conversionAnalytics.reduce((sum, a) => sum + a.conversionRate, 0) / conversionAnalytics.length;
            }
            break;

          case 'PRODUCT_SALES':
            const productPerformance = await prisma.productPerformance.findMany({
              where: {
                userId: goal.userId,
                date: { gte: currentMonth }
              }
            });
            currentValue = productPerformance.reduce((sum, p) => sum + p.orders, 0);
            break;

          default:
            // For custom goals, we can't automatically calculate progress
            continue;
        }

        // Update goal progress
        const isCompleted = currentValue >= goal.targetValue;
        
        await prisma.affiliateGoal.update({
          where: { id: goal.id },
          data: {
            currentValue,
            isCompleted,
            completedAt: isCompleted && !goal.isCompleted ? new Date() : goal.completedAt,
          }
        });

        goalsUpdated++;
        
        if (isCompleted && !goal.isCompleted) {
          goalsCompleted++;
          console.log(`🎉 Goal completed: ${goal.title} for user ${goal.userId}`);
        }

      } catch (error) {
        console.error(`❌ Failed to update goal ${goal.id}:`, error);
        // Continue with next goal
      }
    }

    console.log(`✅ Goal progress update job completed:`);
    console.log(`   📊 Updated ${goalsUpdated} goals`);
    console.log(`   🎉 Completed ${goalsCompleted} goals`);

  } catch (error) {
    console.error('❌ Goal progress update job failed:', error);
    throw error;
  }
}

/**
 * Cleanup job to remove expired insights and old data
 */
export async function affiliateDataCleanupJob() {
  console.log('🧹 Starting affiliate data cleanup job...');

  try {
    const now = new Date();

    // Remove expired insights
    const expiredInsights = await prisma.aIInsight.deleteMany({
      where: {
        validUntil: {
          lt: now
        }
      }
    });

    // Remove old read insights (older than 30 days)
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const oldReadInsights = await prisma.aIInsight.deleteMany({
      where: {
        isRead: true,
        createdAt: {
          lt: thirtyDaysAgo
        }
      }
    });

    // Remove old competitive benchmarks (older than 90 days)
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
    const oldBenchmarks = await prisma.competitiveBenchmark.deleteMany({
      where: {
        createdAt: {
          lt: ninetyDaysAgo
        }
      }
    });

    console.log(`✅ Affiliate data cleanup completed:`);
    console.log(`   🗑️ Removed ${expiredInsights.count} expired insights`);
    console.log(`   📖 Removed ${oldReadInsights.count} old read insights`);
    console.log(`   📊 Removed ${oldBenchmarks.count} old benchmarks`);

  } catch (error) {
    console.error('❌ Affiliate data cleanup job failed:', error);
    throw error;
  }
}