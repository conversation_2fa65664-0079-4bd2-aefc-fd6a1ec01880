import { prisma } from '@xact-data/database';

export async function dataCleanupJob() {
  console.log('🧹 Starting data cleanup...');

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  try {
    // Clean up old alert fires (keep only last 30 days)
    const deletedAlertFires = await prisma.alertFire.deleteMany({
      where: {
        firedAt: {
          lt: thirtyDaysAgo,
        },
        notified: true, // Only delete notified alert fires
      },
    });

    console.log(`🗑️ Deleted ${deletedAlertFires.count} old alert fires`);

    // Clean up old analytics data (keep only last 90 days)
    const deletedAnalytics = await prisma.analytics.deleteMany({
      where: {
        date: {
          lt: ninetyDaysAgo,
        },
      },
    });

    console.log(`📊 Deleted ${deletedAnalytics.count} old analytics records`);

    // Archive inactive products (no sales in last 90 days)
    const inactiveProducts = await prisma.product.findMany({
      where: {
        soldIn24h: 0,
        updatedAt: {
          lt: ninetyDaysAgo,
        },
      },
    });

    if (inactiveProducts.length > 0) {
      console.log(`📦 Found ${inactiveProducts.length} inactive products`);
      // In a real implementation, you might move these to an archive table
      // or mark them as archived rather than deleting them
    }

    console.log('✅ Data cleanup completed successfully');

  } catch (error) {
    console.error('❌ Data cleanup failed:', error);
    throw error;
  }
}