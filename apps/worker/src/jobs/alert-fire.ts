import { prisma } from '@xact-data/database';

export async function alertFireJob() {
  console.log('🔔 Checking for alert triggers...');

  // Get all active alerts with their products
  const alerts = await prisma.alert.findMany({
    where: { isActive: true },
    include: { product: true, user: true },
  });

  let firedCount = 0;

  for (const alert of alerts) {
    const product = alert.product;
    
    // Check if product's trend score exceeds alert threshold
    if (product.trendScore >= alert.threshold) {
      // Check if we haven't fired this alert recently (within last hour)
      const recentFire = await prisma.alertFire.findFirst({
        where: {
          alertId: alert.id,
          firedAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
          },
        },
      });

      if (!recentFire) {
        // Fire the alert
        await prisma.alertFire.create({
          data: {
            alertId: alert.id,
            productId: product.id,
            userId: alert.userId,
            trendScore: product.trendScore,
          },
        });

        firedCount++;
        
        console.log(`🔥 Alert fired: ${product.title} (${product.trendScore.toFixed(1)}) for user ${alert.user.email}`);
        
        // In a real implementation, you would send notifications here
        // await sendNotification(alert.user, product, product.trendScore);
      }
    }
  }

  console.log(`🔔 Fired ${firedCount} alerts`);
}

// Placeholder for notification service
async function sendNotification(user: any, product: any, trendScore: number) {
  // This would integrate with email service, push notifications, etc.
  console.log(`📧 Would send notification to ${user.email} about ${product.title} (trend score: ${trendScore})`);
}