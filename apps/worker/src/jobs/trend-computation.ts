import { prisma } from '@xact-data/database';
import { TikTokApiService } from '@xact-data/shared';

export async function trendComputationJob() {
  console.log('📊 Starting trend computation...');

  // Get all products with TikTok data
  const products = await prisma.product.findMany({
    where: {
      tiktokProductId: {
        not: null,
      },
    },
  });

  let updatedCount = 0;

  for (const product of products) {
    // Calculate new trend score using TikTok data
    if (product.soldCount && product.weekSoldCount && product.totalSales && product.productRating) {
      const newTrendScore = TikTokApiService.calculateTrendScore(
        product.soldCount,
        product.weekSoldCount,
        product.totalSales,
        product.productRating,
        product.relatedVideos || undefined
      );

      // Update product if trend score changed significantly
      if (Math.abs(newTrendScore - product.trendScore) > 1) {
        await prisma.product.update({
          where: { id: product.id },
          data: {
            trendScore: newTrendScore,
          },
        });
        updatedCount++;
      }
    }
  }

  console.log(`📊 Updated ${updatedCount} products with new trend scores`);
}