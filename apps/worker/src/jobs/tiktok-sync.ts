import { prisma } from '@xact-data/database';
import { createTikTokApiService, transformTikTokProducts } from '@xact-data/shared';

/**
 * Daily job to sync winning products from TikTok Shop API
 */
export async function tikTokSyncJob() {
  console.log('🚀 Starting TikTok Shop API sync...');

  try {
    // Create TikTok API service
    const tikTokService = createTikTokApiService();

    // Fetch top 20 winning products
    console.log('📡 Fetching top 20 winning products from TikTok API...');
    const tikTokProducts = await tikTokService.getTop20WinningProducts();
    
    console.log(`✅ Fetched ${tikTokProducts.length} products from TikTok API`);

    // Transform products to our database format
    const transformedProducts = transformTikTokProducts(tikTokProducts);

    let upsertedCount = 0;
    let updatedCount = 0;
    let createdCount = 0;

    // Upsert each product
    for (const product of transformedProducts) {
      try {
        // Check if product already exists
        const existingProduct = await prisma.product.findUnique({
          where: { id: product.id },
        });

        if (existingProduct) {
          // Update existing product
          await prisma.product.update({
            where: { id: product.id },
            data: {
              title: product.title,
              category: product.category,
              commissionRate: product.commissionRate,
              soldIn24h: product.soldIn24h,
              creatorsCarrying: product.creatorsCarrying,
              estimatedGMV: product.estimatedGMV,
              trendScore: product.trendScore,
              affiliateLink: product.affiliateLink,
              imageUrl: product.imageUrl,
              price: product.price,
              
              // TikTok API specific fields
              tiktokProductId: product.tiktokProductId,
              rank: product.rank,
              category1: product.category1,
              category2: product.category2,
              category3: product.category3,
              countryCode: product.countryCode,
              soldCount: product.soldCount,
              totalSales: product.totalSales,
              weekSoldCount: product.weekSoldCount,
              weekSales: product.weekSales,
              productRating: product.productRating,
              sellerId: product.sellerId,
              sellerName: product.sellerName,
              shopId: product.shopId,
              shopName: product.shopName,
              stock: product.stock,
              relatedVideos: product.relatedVideos,
              relatedAuthors: <AUTHORS>
              freeShipping: product.freeShipping,
              lastTimeStamp: product.lastTimeStamp,
            },
          });
          updatedCount++;
        } else {
          // Create new product
          await prisma.product.create({
            data: {
              id: product.id,
              title: product.title,
              category: product.category,
              commissionRate: product.commissionRate,
              soldIn24h: product.soldIn24h,
              creatorsCarrying: product.creatorsCarrying,
              estimatedGMV: product.estimatedGMV,
              trendScore: product.trendScore,
              affiliateLink: product.affiliateLink,
              imageUrl: product.imageUrl,
              price: product.price,
              
              // TikTok API specific fields
              tiktokProductId: product.tiktokProductId,
              rank: product.rank,
              category1: product.category1,
              category2: product.category2,
              category3: product.category3,
              countryCode: product.countryCode,
              soldCount: product.soldCount,
              totalSales: product.totalSales,
              weekSoldCount: product.weekSoldCount,
              weekSales: product.weekSales,
              productRating: product.productRating,
              sellerId: product.sellerId,
              sellerName: product.sellerName,
              shopId: product.shopId,
              shopName: product.shopName,
              stock: product.stock,
              relatedVideos: product.relatedVideos,
              relatedAuthors: <AUTHORS>
              freeShipping: product.freeShipping,
              lastTimeStamp: product.lastTimeStamp,
            },
          });
          createdCount++;
        }
        
        upsertedCount++;
      } catch (error) {
        console.error(`❌ Error upserting product ${product.id}:`, error);
      }
    }

    // Clean up old products that are no longer in top 20
    const currentProductIds = transformedProducts.map(p => p.id);
    const deletedResult = await prisma.product.deleteMany({
      where: {
        tiktokProductId: {
          not: null,
        },
        id: {
          notIn: currentProductIds,
        },
      },
    });

    console.log(`🎉 TikTok sync completed successfully!`);
    console.log(`   📊 Products processed: ${tikTokProducts.length}`);
    console.log(`   ✅ Products upserted: ${upsertedCount}`);
    console.log(`   🆕 Products created: ${createdCount}`);
    console.log(`   🔄 Products updated: ${updatedCount}`);
    console.log(`   🗑️ Old products cleaned up: ${deletedResult.count}`);

    return {
      processed: tikTokProducts.length,
      upserted: upsertedCount,
      created: createdCount,
      updated: updatedCount,
      deleted: deletedResult.count,
    };

  } catch (error) {
    console.error('❌ TikTok sync job failed:', error);
    throw error;
  }
}

/**
 * Test function to run the sync job manually
 */
export async function testTikTokSync() {
  console.log('🧪 Testing TikTok sync job...');
  
  try {
    const result = await tikTokSyncJob();
    console.log('✅ Test completed successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}