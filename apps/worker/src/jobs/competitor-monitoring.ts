import { prisma } from '@xact-data/database';
import { createTikTokApiService, transformTikTokUserToCreator, transformTikTokPostsToCreatorVideos } from '@xact-data/shared';

/**
 * Monitor competitors for significant changes and generate alerts
 */
export async function competitorMonitoringJob() {
  console.log('🔍 Starting competitor monitoring job...');
  
  try {
    // Get all active competitor trackings
    const activeTrackings = await prisma.competitorTracking.findMany({
      where: { isActive: true },
      include: {
        creator: true,
      },
    });

    console.log(`📊 Monitoring ${activeTrackings.length} competitors...`);

    const tikTokService = createTikTokApiService();
    let updatedCount = 0;
    let alertsCreated = 0;

    for (const tracking of activeTrackings) {
      try {
        // Fetch fresh data from TikTok API
        const userInfo = await tikTokService.getUserInfo(tracking.creator.username);
        const updatedCreatorData = transformTikTokUserToCreator(userInfo);

        // Store previous values for comparison
        const previousFollowers = tracking.creator.followerCount;
        const previousLikes = tracking.creator.likesCount;
        const previousVideos = tracking.creator.videoCount;
        const previousEngagement = tracking.creator.engagementRate;

        // Update creator with fresh data
        const updatedCreator = await prisma.creator.update({
          where: { id: tracking.creatorId },
          data: {
            displayName: updatedCreatorData.displayName,
            bio: updatedCreatorData.bio,
            followerCount: updatedCreatorData.followerCount,
            followingCount: updatedCreatorData.followingCount,
            likesCount: updatedCreatorData.likesCount,
            videoCount: updatedCreatorData.videoCount,
            averageViews: updatedCreatorData.averageViews,
            engagementRate: updatedCreatorData.engagementRate,
            profileImageUrl: updatedCreatorData.profileImageUrl,
            isVerified: updatedCreatorData.isVerified,
            updatedAt: new Date(),
          },
        });

        // Fetch and store posts data if secUid is available
        if (tracking.creator.secUid) {
          try {
            console.log(`📡 Fetching posts for ${tracking.creator.username}...`);
            const postsResponse = await tikTokService.getUserPosts(tracking.creator.secUid, 20);
            
            if (postsResponse.data?.itemList && postsResponse.data.itemList.length > 0) {
              // Transform posts to database format
              const videoData = transformTikTokPostsToCreatorVideos(
                postsResponse.data.itemList,
                tracking.creator.id
              );

              // Store videos in database (upsert to avoid duplicates)
              for (const video of videoData) {
                await prisma.creatorVideo.upsert({
                  where: { id: video.id },
                  update: {
                    viewCount: video.viewCount,
                    likeCount: video.likeCount,
                    shareCount: video.shareCount,
                    commentCount: video.commentCount,
                    engagementRate: video.engagementRate,
                    updatedAt: new Date(),
                  },
                  create: video,
                });
              }

              console.log(`✅ Stored ${videoData.length} posts for ${tracking.creator.username}`);
            }
          } catch (postsError) {
            console.error(`⚠️ Failed to fetch posts for ${tracking.creator.username}:`, postsError);
            // Continue with monitoring - posts fetch failure shouldn't stop the job
          }
        }

        updatedCount++;

        // Generate alerts for significant changes
        const alerts = [];

        // Follower milestone alert (10k+ increase)
        const followerIncrease = updatedCreator.followerCount - previousFollowers;
        if (followerIncrease >= 10000) {
          alerts.push({
            competitorTrackingId: tracking.id,
            alertType: 'FOLLOWER_MILESTONE' as const,
            title: 'Follower Milestone Reached',
            description: `${updatedCreator.displayName || updatedCreator.username} gained ${followerIncrease.toLocaleString()} followers (now ${updatedCreator.followerCount.toLocaleString()})`,
            metadata: { 
              followerIncrease, 
              newFollowerCount: updatedCreator.followerCount,
              previousFollowerCount: previousFollowers
            },
          });
        }

        // Engagement spike alert (2%+ increase)
        const engagementIncrease = updatedCreator.engagementRate - previousEngagement;
        if (engagementIncrease >= 2) {
          alerts.push({
            competitorTrackingId: tracking.id,
            alertType: 'ENGAGEMENT_SPIKE' as const,
            title: 'Engagement Rate Spike',
            description: `${updatedCreator.displayName || updatedCreator.username} had a ${engagementIncrease.toFixed(1)}% engagement increase (now ${updatedCreator.engagementRate.toFixed(1)}%)`,
            metadata: { 
              engagementIncrease, 
              newEngagementRate: updatedCreator.engagementRate,
              previousEngagementRate: previousEngagement
            },
          });
        }

        // New video alert (5+ new videos)
        const videoIncrease = updatedCreator.videoCount - previousVideos;
        if (videoIncrease >= 5) {
          alerts.push({
            competitorTrackingId: tracking.id,
            alertType: 'NEW_VIDEO' as const,
            title: 'Content Publishing Spike',
            description: `${updatedCreator.displayName || updatedCreator.username} published ${videoIncrease} new videos (now ${updatedCreator.videoCount} total)`,
            metadata: { 
              videoIncrease, 
              newVideoCount: updatedCreator.videoCount,
              previousVideoCount: previousVideos
            },
          });
        }

        // Viral growth alert (100k+ likes increase)
        const likesIncrease = updatedCreator.likesCount - previousLikes;
        if (likesIncrease >= 100000) {
          alerts.push({
            competitorTrackingId: tracking.id,
            alertType: 'VIRAL_POST' as const,
            title: 'Viral Growth Detected',
            description: `${updatedCreator.displayName || updatedCreator.username} gained ${(likesIncrease / 1000000).toFixed(1)}M likes (now ${(updatedCreator.likesCount / 1000000).toFixed(1)}M total)`,
            metadata: { 
              likesIncrease, 
              newLikesCount: updatedCreator.likesCount,
              previousLikesCount: previousLikes
            },
          });
        }

        // Create alerts if any were generated
        if (alerts.length > 0) {
          await prisma.competitorAlert.createMany({
            data: alerts,
          });
          alertsCreated += alerts.length;
          
          console.log(`🚨 Created ${alerts.length} alerts for ${updatedCreator.username}`);
        }

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`❌ Failed to monitor competitor ${tracking.creator.username}:`, error);
        // Continue with next competitor
      }
    }

    console.log(`✅ Competitor monitoring completed: ${updatedCount} updated, ${alertsCreated} alerts created`);

  } catch (error) {
    console.error('❌ Competitor monitoring job failed:', error);
    throw error;
  }
}

/**
 * Check for milestone achievements and create alerts
 */
export async function checkMilestones() {
  console.log('🎯 Checking competitor milestones...');

  try {
    const activeTrackings = await prisma.competitorTracking.findMany({
      where: { isActive: true },
      include: {
        creator: true,
      },
    });

    let milestonesDetected = 0;

    for (const tracking of activeTrackings) {
      const creator = tracking.creator;
      const alerts = [];

      // Check follower milestones
      const followerMilestones = [100000, 500000, 1000000, 5000000, 10000000];
      for (const milestone of followerMilestones) {
        if (creator.followerCount >= milestone) {
          // Check if we already have an alert for this milestone
          const existingAlert = await prisma.competitorAlert.findFirst({
            where: {
              competitorTrackingId: tracking.id,
              alertType: 'FOLLOWER_MILESTONE',
              metadata: {
                path: ['milestone'],
                equals: milestone,
              },
            },
          });

          if (!existingAlert) {
            alerts.push({
              competitorTrackingId: tracking.id,
              alertType: 'FOLLOWER_MILESTONE' as const,
              title: `${(milestone / 1000000).toFixed(0)}M Followers Milestone`,
              description: `${creator.displayName || creator.username} has reached ${(milestone / 1000000).toFixed(1)}M followers!`,
              metadata: { 
                milestone,
                currentFollowers: creator.followerCount,
                milestoneType: 'followers'
              },
            });
          }
        }
      }

      // Check likes milestones
      const likesMilestones = [1000000, 10000000, 100000000, 500000000, 1000000000];
      for (const milestone of likesMilestones) {
        if (creator.likesCount >= milestone) {
          const existingAlert = await prisma.competitorAlert.findFirst({
            where: {
              competitorTrackingId: tracking.id,
              alertType: 'SALES_MILESTONE',
              metadata: {
                path: ['milestone'],
                equals: milestone,
              },
            },
          });

          if (!existingAlert) {
            alerts.push({
              competitorTrackingId: tracking.id,
              alertType: 'SALES_MILESTONE' as const,
              title: `${(milestone / 1000000).toFixed(0)}M Likes Milestone`,
              description: `${creator.displayName || creator.username} has reached ${(milestone / 1000000).toFixed(1)}M total likes!`,
              metadata: { 
                milestone,
                currentLikes: creator.likesCount,
                milestoneType: 'likes'
              },
            });
          }
        }
      }

      // Create alerts if any were generated
      if (alerts.length > 0) {
        await prisma.competitorAlert.createMany({
          data: alerts,
        });
        milestonesDetected += alerts.length;
        
        console.log(`🎯 Detected ${alerts.length} milestones for ${creator.username}`);
      }
    }

    console.log(`✅ Milestone check completed: ${milestonesDetected} milestones detected`);

  } catch (error) {
    console.error('❌ Milestone check failed:', error);
    throw error;
  }
}

/**
 * Clean up old alerts (older than 30 days)
 */
export async function cleanupOldAlerts() {
  console.log('🧹 Cleaning up old competitor alerts...');

  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const deleteResult = await prisma.competitorAlert.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo,
        },
        isRead: true, // Only delete read alerts
      },
    });

    console.log(`✅ Cleaned up ${deleteResult.count} old competitor alerts`);

  } catch (error) {
    console.error('❌ Alert cleanup failed:', error);
    throw error;
  }
}