{"name": "api", "version": "1.0.0", "description": "Xact Data API Server", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "pnpm --filter @xact-data/database build && pnpm --filter @xact-data/ai-wrapper build && pnpm --filter @xact-data/shared build && tsc", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "zod": "^3.22.0", "@xact-data/shared": "workspace:*", "@xact-data/database": "workspace:*", "@xact-data/ai-wrapper": "workspace:*", "express-rate-limit": "^7.1.5", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/node": "^20.0.0", "@types/compression": "^1.7.5", "typescript": "^5.1.0", "tsx": "^4.0.0"}}