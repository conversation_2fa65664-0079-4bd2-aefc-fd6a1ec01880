import { Router } from 'express';
import { z } from 'zod';
import { createApiResponse, TikTokShopApiService } from '@xact-data/shared';
import { asyncHandler } from '../middleware/error-handler';
import { db } from '@xact-data/database';
import crypto from 'crypto';

const router = Router();

// Schema for user creation/update
const WhopUserSchema = z.object({
  whopId: z.string(),
  email: z.string().email(),
  name: z.string().optional(),
  image: z.string().url().optional(),
});

/**
 * Create or update user from Whop OAuth
 */
router.post('/whop/user', asyncHandler(async (req, res) => {
  const userData = WhopUserSchema.parse(req.body);

  // Check if user already exists by whopId
  let user = await db.user.findUnique({
    where: { whopId: userData.whopId }
  });

  if (user) {
    // Update existing user
    user = await db.user.update({
      where: { whopId: userData.whopId },
      data: {
        email: userData.email,
        name: userData.name,
        image: userData.image,
        updatedAt: new Date(),
      },
    });
  } else {
    // Check if user exists with same email
    const existingUser = await db.user.findUnique({
      where: { email: userData.email }
    });

    if (existingUser) {
      // Link whopId to existing user
      user = await db.user.update({
        where: { email: userData.email },
        data: {
          whopId: userData.whopId,
          name: userData.name || existingUser.name,
          image: userData.image || existingUser.image,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new user
      user = await db.user.create({
        data: {
          whopId: userData.whopId,
          email: userData.email,
          name: userData.name,
          image: userData.image,
        },
      });
    }
  }

  res.json(createApiResponse(true, user, 'User created/updated successfully'));
}));

/**
 * Get user by whopId
 */
router.get('/whop/user/:whopId', asyncHandler(async (req, res) => {
  const { whopId } = req.params;

  const user = await db.user.findUnique({
    where: { whopId },
    include: {
      analytics: true,
      alerts: true,
      affiliateGoals: true,
      achievements: true,
    },
  });

  if (!user) {
    return res.status(404).json(
      createApiResponse(false, null, 'User not found')
    );
  }

  res.json(createApiResponse(true, user, 'User retrieved successfully'));
}));


// Initialize TikTok Shop API service
const tikTokShopApiService = new TikTokShopApiService({
  apiKey: process.env.TIKTOK_SHOP_API_KEY || '6hi6rl0brml5g',
  apiSecret: process.env.TIKTOK_SHOP_API_SECRET || '85aca6a18764530340e7f57cd24316d3bf5816c9',
});

/**
 * Generate TikTok Shop OAuth authorization URL
 */
router.get('/tiktok-shop/auth-url', asyncHandler(async (req, res) => {
  const { userId, redirectUri } = req.query;
  
  if (!userId || !redirectUri) {
    return res.status(400).json(
      createApiResponse(false, null, 'userId and redirectUri are required')
    );
  }

  // Generate state parameter for security (include userId) - required for creator authorization
  const state = Buffer.from(JSON.stringify({ userId, timestamp: Date.now() })).toString('base64');
  
  // For creator authorization, state parameter is required and must be provided manually
  const authUrl = tikTokShopApiService.generateAuthUrl(redirectUri as string, state);
  
  res.json(createApiResponse(true, { authUrl, state }, 'Authorization URL generated successfully'));
}));

/**
 * Handle TikTok Shop OAuth callback
 */
router.post('/tiktok-shop/callback', asyncHandler(async (req, res) => {
  console.log('TikTok Shop OAuth callback received:', { 
    body: { 
      code: !!req.body.code, 
      state: !!req.body.state, 
      redirectUri: req.body.redirectUri 
    } 
  });

  const callbackSchema = z.object({
    code: z.string(),
    state: z.string(),
    redirectUri: z.string(),
  });

  const { code, state, redirectUri } = callbackSchema.parse(req.body);

  try {
    // Decode and validate state
    const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
    const { userId } = stateData;
    console.log('Decoded state data:', { userId });

    if (!userId) {
      console.error('Invalid state parameter - no userId found');
      return res.status(400).json(
        createApiResponse(false, null, 'Invalid state parameter')
      );
    }

    // Verify user exists
    const existingUser = await db.user.findUnique({
      where: { whopId: userId },
      select: { id: true, whopId: true, email: true }
    });

    if (!existingUser) {
      console.error('User not found:', userId);
      return res.status(404).json(
        createApiResponse(false, null, 'User not found')
      );
    }

    console.log('Found user:', existingUser);

    // Exchange code for tokens
    console.log('Exchanging code for tokens...');
    console.log('Token exchange parameters:', {
      codeLength: code.length,
      redirectUri,
      apiKey: process.env.TIKTOK_SHOP_API_KEY || '6hi6rl0brml5g',
      hasApiSecret: !!(process.env.TIKTOK_SHOP_API_SECRET || '85aca6a18764530340e7f57cd24316d3bf5816c9')
    });
    
    const tokens = await tikTokShopApiService.exchangeCodeForTokens(code, redirectUri);
    console.log('Token exchange successful:', { 
      hasAccessToken: !!tokens.access_token,
      hasRefreshToken: !!tokens.refresh_token,
      expiresIn: tokens.expires_in,
      hasGrantedScopes: !!tokens.granted_scopes,
      grantedScopes: tokens.granted_scopes
    });

    // Validate granted scopes for creator authorization
    if (tokens.granted_scopes) {
      const requiredScopes = tikTokShopApiService.getRequiredCreatorScopes();
      const scopeValidation = tikTokShopApiService.validateGrantedScopes(tokens.granted_scopes, requiredScopes);
      
      if (!scopeValidation.valid) {
        console.warn('Missing required scopes:', scopeValidation.missingScopes);
        return res.status(400).json(
          createApiResponse(false, {
            missingScopes: scopeValidation.missingScopes,
            grantedScopes: tokens.granted_scopes,
            requiredScopes
          }, 'Creator has not authorized all necessary scopes. Please ask the creator to remove all access and re-authorize.')
        );
      }
    }
    
    // Get creator information (updated for affiliate integration)
    console.log('Fetching creator information...');
    const creatorInfo = await tikTokShopApiService.getCreatorInfo(tokens.access_token);
    console.log('Creator info retrieved:', {
      shopId: creatorInfo.shop_id,
      shopName: creatorInfo.shop_name,
      shopRegion: creatorInfo.shop_region
    });
    
    // Calculate token expiration times
    const tokenExpiresAt = new Date(Date.now() + tokens.expires_in * 1000);
    const refreshExpiresAt = new Date(Date.now() + tokens.refresh_expires_in * 1000);

    // Update user with TikTok Shop tokens and info
    console.log('Updating user with TikTok Shop creator data...');
    const user = await db.user.update({
      where: { whopId: userId },
      data: {
        tiktokShopId: creatorInfo.shop_id,
        tiktokShopAccessToken: tokens.access_token,
        tiktokShopRefreshToken: tokens.refresh_token,
        tiktokShopTokenExpiresAt: tokenExpiresAt,
        tiktokShopRefreshExpiresAt: refreshExpiresAt,
        tiktokShopName: creatorInfo.shop_name,
        tiktokShopRegion: creatorInfo.shop_region,
        tiktokShopConnectedAt: new Date(),
        updatedAt: new Date(),
      },
    });

    console.log('User updated successfully:', {
      userId: user.id,
      tiktokShopId: user.tiktokShopId,
      tiktokShopName: user.tiktokShopName,
      connectedAt: user.tiktokShopConnectedAt
    });

    res.json(createApiResponse(true, {
      user: {
        id: user.id,
        tiktokShopId: user.tiktokShopId,
        tiktokShopName: user.tiktokShopName,
        tiktokShopRegion: user.tiktokShopRegion,
        tiktokShopConnectedAt: user.tiktokShopConnectedAt,
      },
      creatorInfo,
    }, 'TikTok Shop creator account connected successfully'));
    
  } catch (error) {
    console.error('TikTok Shop OAuth callback error:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    
    res.status(400).json(
      createApiResponse(false, null, `OAuth callback failed: ${error.message}`)
    );
  }
}));

/**
 * Disconnect TikTok Shop account
 */
router.post('/tiktok-shop/disconnect', asyncHandler(async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json(
      createApiResponse(false, null, 'userId is required')
    );
  }

  // Clear TikTok Shop tokens and info
  const user = await db.user.update({
    where: { whopId: userId },
    data: {
      tiktokShopId: null,
      tiktokShopAccessToken: null,
      tiktokShopRefreshToken: null,
      tiktokShopTokenExpiresAt: null,
      tiktokShopRefreshExpiresAt: null,
      tiktokShopName: null,
      tiktokShopRegion: null,
      tiktokShopConnectedAt: null,
      updatedAt: new Date(),
    },
  });

  res.json(createApiResponse(true, { userId: user.id }, 'TikTok Shop account disconnected successfully'));
}));

/**
 * Check TikTok Shop connection status
 */
router.get('/tiktok-shop/status/:userId', asyncHandler(async (req, res) => {
  const { userId } = req.params;
  console.log('Checking TikTok Shop status for user:', userId);

  const user = await db.user.findUnique({
    where: { whopId: userId },
    select: {
      id: true,
      whopId: true,
      tiktokShopId: true,
      tiktokShopName: true,
      tiktokShopRegion: true,
      tiktokShopConnectedAt: true,
      tiktokShopTokenExpiresAt: true,
      tiktokShopAccessToken: true,
    },
  });

  if (!user) {
    console.error('User not found for status check:', userId);
    return res.status(404).json(
      createApiResponse(false, null, 'User not found')
    );
  }

  console.log('User found for status check:', {
    userId: user.whopId,
    hasShopId: !!user.tiktokShopId,
    hasAccessToken: !!user.tiktokShopAccessToken,
    tokenExpiresAt: user.tiktokShopTokenExpiresAt,
    connectedAt: user.tiktokShopConnectedAt
  });

  const hasTokens = !!(user.tiktokShopId && user.tiktokShopTokenExpiresAt && user.tiktokShopAccessToken);
  const tokenExpired = user.tiktokShopTokenExpiresAt ? user.tiktokShopTokenExpiresAt <= new Date() : false;
  const isConnected = hasTokens && !tokenExpired;

  const statusData = {
    isConnected,
    tokenExpired,
    shopInfo: hasTokens ? {
      shopId: user.tiktokShopId,
      shopName: user.tiktokShopName,
      shopRegion: user.tiktokShopRegion,
      connectedAt: user.tiktokShopConnectedAt,
      tokenExpiresAt: user.tiktokShopTokenExpiresAt,
    } : null,
  };

  console.log('Status response:', statusData);

  res.json(createApiResponse(true, statusData, 'TikTok Shop status retrieved successfully'));
}));

/**
 * Refresh TikTok Shop access token
 */
router.post('/tiktok-shop/refresh-token', asyncHandler(async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json(
      createApiResponse(false, null, 'userId is required')
    );
  }

  const user = await db.user.findUnique({
    where: { whopId: userId },
    select: {
      id: true,
      whopId: true,
      tiktokShopRefreshToken: true,
      tiktokShopRefreshExpiresAt: true,
    },
  });

  if (!user || !user.tiktokShopRefreshToken) {
    return res.status(400).json(
      createApiResponse(false, null, 'No refresh token found for user')
    );
  }

  if (user.tiktokShopRefreshExpiresAt && user.tiktokShopRefreshExpiresAt <= new Date()) {
    return res.status(400).json(
      createApiResponse(false, null, 'Refresh token has expired. Please reconnect your TikTok Shop account.')

    );
  }

  try {
    // Refresh the access token
    const tokens = await tikTokShopApiService.refreshAccessToken(user.tiktokShopRefreshToken);
    
    // Calculate new expiration times
    const tokenExpiresAt = new Date(Date.now() + tokens.expires_in * 1000);
    const refreshExpiresAt = new Date(Date.now() + tokens.refresh_expires_in * 1000);


    // Update user with new tokens
    await db.user.update({
      where: { whopId: userId },
      data: {
        tiktokShopAccessToken: tokens.access_token,
        tiktokShopRefreshToken: tokens.refresh_token,
        tiktokShopTokenExpiresAt: tokenExpiresAt,
        tiktokShopRefreshExpiresAt: refreshExpiresAt,

        updatedAt: new Date(),
      },
    });

    res.json(createApiResponse(true, {

      tokenExpiresAt,
      refreshExpiresAt,
    }, 'Access token refreshed successfully'));
    
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(400).json(
      createApiResponse(false, null, `Token refresh failed: ${error.message}`)

    );
  }
}));

/**
 * Handle TikTok Shop webhooks for real-time updates
 */
router.post('/tiktok-shop/webhook', asyncHandler(async (req, res) => {
  console.log('TikTok Shop webhook received:', {
    headers: req.headers,
    body: req.body
  });

  try {
    // Verify webhook signature (implement based on TikTok Shop webhook documentation)
    const signature = req.headers['x-tiktok-shop-signature'];
    const timestamp = req.headers['x-tiktok-shop-timestamp'];
    
    if (!signature || !timestamp) {
      console.error('Missing webhook signature or timestamp');
      return res.status(400).json({ error: 'Missing required headers' });
    }

    const { event_type, data } = req.body;
    console.log('Webhook event:', { event_type, data });

    switch (event_type) {
      case 'AUTHORIZATION_REVOKED':
        // Handle when user revokes authorization
        if (data.shop_id) {
          await db.user.updateMany({
            where: { tiktokShopId: data.shop_id },
            data: {
              tiktokShopAccessToken: null,
              tiktokShopRefreshToken: null,
              tiktokShopTokenExpiresAt: null,
              tiktokShopRefreshExpiresAt: null,
              tiktokShopConnectedAt: null,
              updatedAt: new Date(),
            },
          });
          console.log('Authorization revoked for shop:', data.shop_id);
        }
        break;

      case 'SHOP_UPDATE':
        // Handle shop information updates
        if (data.shop_id) {
          await db.user.updateMany({
            where: { tiktokShopId: data.shop_id },
            data: {
              tiktokShopName: data.shop_name,
              tiktokShopRegion: data.shop_region,
              updatedAt: new Date(),
            },
          });
          console.log('Shop updated:', data.shop_id);
        }
        break;

      default:
        console.log('Unhandled webhook event type:', event_type);
    }

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}));

/**
 * Health check endpoint for TikTok Shop integration
 */
router.get('/tiktok-shop/health', asyncHandler(async (req, res) => {
  const connectedUsers = await db.user.count({
    where: {
      AND: [
        { tiktokShopAccessToken: { not: null } },
        { tiktokShopId: { not: null } },
        { tiktokShopTokenExpiresAt: { gt: new Date() } }
      ]
    }
  });

  const expiredTokens = await db.user.count({
    where: {
      AND: [
        { tiktokShopAccessToken: { not: null } },
        { tiktokShopTokenExpiresAt: { lte: new Date() } }
      ]
    }
  });

  res.json(createApiResponse(true, {
    connectedUsers,
    expiredTokens,
    timestamp: new Date().toISOString()
  }, 'TikTok Shop integration health check'));
}));

export default router;