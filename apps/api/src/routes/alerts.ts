import { Router } from 'express';
import {
  getAlerts,
  createAlert,
  updateAlert,
  deleteAlert,
  getAlertFires,
  markAlertFireAsNotified,
} from '../controllers/alerts';

const router = Router();

router.get('/user/:userId', getAlerts);
router.post('/', createAlert);
router.put('/:id', updateAlert);
router.delete('/:id', deleteAlert);
router.get('/fires/user/:userId', getAlertFires);
router.patch('/fires/:id/notified', markAlertFireAsNotified);

export default router;