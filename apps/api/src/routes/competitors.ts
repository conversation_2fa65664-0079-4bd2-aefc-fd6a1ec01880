import { Router } from 'express';
import {
  addCompetitor,
  getCompetitors,
  getCompetitor,
  updateCompetitor,
  removeCompetitor,
  refreshCompetitor,
  compareCompetitors,
  getCompetitorAlerts,
  markAlertsAsRead,
  generateAnalysis,
  getAnalyses,
  getAnalysis,
} from '../controllers/competitors';

const router = Router();

// Competitor management routes
router.post('/', addCompetitor);
router.get('/', getCompetitors);
router.get('/:id', getCompetitor);
router.put('/:id', updateCompetitor);
router.delete('/:id', removeCompetitor);

// Competitor data refresh
router.post('/:id/refresh', refreshCompetitor);

// Competitor comparison
router.get('/:id1/compare/:id2', compareCompetitors);

// Competitor alerts
router.get('/:id/alerts', getCompetitorAlerts);
router.post('/:id/alerts/mark-read', markAlertsAsRead);

// Competitor analysis
router.post('/:id/analysis', generateAnalysis);
router.get('/:id/analyses', getAnalyses);
router.get('/:id/analyses/:analysisId', getAnalysis);

export default router;