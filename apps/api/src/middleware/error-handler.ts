import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { createApiResponse } from '@xact-data/shared';

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('Error:', error);

  // Zod validation errors
  if (error instanceof ZodError) {
    return res.status(400).json(
      createApiResponse(
        false,
        null,
        'Validation error',
        error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
      )
    );
  }

  // Operational errors
  if (error instanceof AppError) {
    return res.status(error.statusCode).json(
      createApiResponse(false, null, error.message)
    );
  }

  // Prisma errors
  if (error.name === 'PrismaClientKnownRequestError') {
    return res.status(400).json(
      createApiResponse(false, null, 'Database error')
    );
  }

  // Default error
  return res.status(500).json(
    createApiResponse(
      false,
      null,
      'Internal server error',
      process.env.NODE_ENV === 'development' ? error.message : undefined
    )
  );
};

export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};