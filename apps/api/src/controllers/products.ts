import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse, ProductSchema } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';

// Helper function to convert BigInt to string for JSON serialization
function serializeProduct(product: any) {
  return {
    ...product,
    lastTimeStamp: product.lastTimeStamp ? product.lastTimeStamp.toString() : null,
  };
}

const GetProductsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  category: z.string().optional(),
  minTrendScore: z.coerce.number().min(0).max(100).optional(),
  sortBy: z.enum(['trendScore', 'soldIn24h', 'estimatedGMV', 'commissionRate']).default('trendScore'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const getProducts = asyncHandler(async (req: Request, res: Response) => {
  const query = GetProductsQuerySchema.parse(req.query);
  
  const where = {
    ...(query.category && { category: query.category }),
    ...(query.minTrendScore && { trendScore: { gte: query.minTrendScore } }),
  };

  const orderBy = {
    [query.sortBy]: query.sortOrder,
  };

  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      orderBy,
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.product.count({ where }),
  ]);

  const totalPages = Math.ceil(total / query.limit);

  // Serialize products to handle BigInt values
  const serializedProducts = products.map(serializeProduct);

  res.json(
    createApiResponse(true, {
      products: serializedProducts,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNext: query.page < totalPages,
        hasPrev: query.page > 1,
      },
    })
  );
});

export const getProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      creatorProducts: {
        include: {
          creator: true,
        },
        orderBy: {
          gmv: 'desc',
        },
        take: 10,
      },
    },
  });

  if (!product) {
    throw new AppError('Product not found', 404);
  }

  res.json(createApiResponse(true, serializeProduct(product)));
});

const CreateProductSchema = ProductSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const createProduct = asyncHandler(async (req: Request, res: Response) => {
  const data = CreateProductSchema.parse(req.body);

  const product = await prisma.product.create({
    data: {
      id: `prod_${Date.now()}`, // Simple ID generation
      title: data.title,
      category: data.category,
      commissionRate: data.commissionRate,
      soldIn24h: data.soldIn24h,
      creatorsCarrying: data.creatorsCarrying,
      estimatedGMV: data.estimatedGMV,
      trendScore: data.trendScore,
      affiliateLink: data.affiliateLink,
      imageUrl: data.imageUrl,
      price: data.price,
    },
  });

  res.status(201).json(createApiResponse(true, serializeProduct(product), 'Product created successfully'));
});

export const updateProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const data = CreateProductSchema.partial().parse(req.body);

  const product = await prisma.product.update({
    where: { id },
    data,
  });

  res.json(createApiResponse(true, serializeProduct(product), 'Product updated successfully'));
});

export const deleteProduct = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  await prisma.product.delete({
    where: { id },
  });

  res.json(createApiResponse(true, null, 'Product deleted successfully'));
});

export const getTrendingProducts = asyncHandler(async (req: Request, res: Response) => {
  const products = await prisma.product.findMany({
    where: {
      trendScore: { gte: 70 },
    },
    orderBy: {
      trendScore: 'desc',
    },
    take: 10,
  });

  // Serialize products to handle BigInt values
  const serializedProducts = products.map(serializeProduct);

  res.json(createApiResponse(true, serializedProducts));
});