import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse, transformTikTokUserToCreator, createTikTokApiService } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';
import { competitorAnalysisService } from '../services/competitor-analysis';

// Validation schemas
const AddCompetitorSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  nickname: z.string().optional(),
});

const UpdateCompetitorSchema = z.object({
  nickname: z.string().optional(),
  isActive: z.boolean().optional(),
});

const GetCompetitorsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  isActive: z.coerce.boolean().optional(),
});

// Add a new competitor to track
export const addCompetitor = asyncHandler(async (req: Request, res: Response) => {
  const { username, nickname } = AddCompetitorSchema.parse(req.body);
  const userId = 'user_test'; // TODO: Get from auth when implemented
  
  const tikTokService = createTikTokApiService();
  
  try {
    // Fetch user info from TikTok API
    const userInfo = await tikTokService.getUserInfo(username);
    const creatorData = transformTikTokUserToCreator(userInfo);
    
    // Upsert creator in database
    const creator = await prisma.creator.upsert({
      where: { id: creatorData.id },
      update: {
        displayName: creatorData.displayName,
        bio: creatorData.bio,
        followerCount: creatorData.followerCount,
        followingCount: creatorData.followingCount,
        likesCount: creatorData.likesCount,
        videoCount: creatorData.videoCount,
        averageViews: creatorData.averageViews,
        engagementRate: creatorData.engagementRate,
        profileImageUrl: creatorData.profileImageUrl,
        isVerified: creatorData.isVerified,
        tiktokUserId: creatorData.tiktokUserId,
        secUid: creatorData.secUid,
        region: creatorData.region,
        updatedAt: new Date(),
      },
      create: creatorData,
    });

    // Check if already tracking this competitor
    const existingTracking = await prisma.competitorTracking.findUnique({
      where: {
        userId_creatorId: {
          userId,
          creatorId: creator.id,
        },
      },
    });

    if (existingTracking) {
      throw new AppError('Already tracking this competitor', 409);
    }

    // Create competitor tracking record
    const competitorTracking = await prisma.competitorTracking.create({
      data: {
        userId,
        creatorId: creator.id,
        nickname,
        isActive: true,
      },
      include: {
        creator: true,
      },
    });

    res.status(201).json(
      createApiResponse(true, competitorTracking, 'Competitor added successfully')
    );
  } catch (error: any) {
    if (error.message.includes('TikTok')) {
      throw new AppError('Failed to fetch competitor data from TikTok', 400);
    }
    throw error;
  }
});

// Get all tracked competitors for a user
export const getCompetitors = asyncHandler(async (req: Request, res: Response) => {
  const query = GetCompetitorsQuerySchema.parse(req.query);
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const where: any = { userId };
  if (query.isActive !== undefined) {
    where.isActive = query.isActive;
  }

  const [competitors, total] = await Promise.all([
    prisma.competitorTracking.findMany({
      where,
      include: {
        creator: true,
        alerts: {
          where: { isRead: false },
          take: 3,
          orderBy: { createdAt: 'desc' },
        },
        analyses: {
          take: 1,
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip: (query.page - 1) * query.limit,
      take: query.limit,
    }),
    prisma.competitorTracking.count({ where }),
  ]);

  const totalPages = Math.ceil(total / query.limit);

  res.json(
    createApiResponse(true, {
      competitors,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNext: query.page < totalPages,
        hasPrev: query.page > 1,
      },
    })
  );
});

// Get a specific competitor's details
export const getCompetitor = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
    include: {
      creator: {
        include: {
          videos: {
            orderBy: { publishedAt: 'desc' },
            take: 10,
          },
          creatorProducts: {
            include: { product: true },
            orderBy: { gmv: 'desc' },
            take: 5,
          },
        },
      },
      alerts: {
        orderBy: { createdAt: 'desc' },
        take: 20,
      },
      analyses: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
    },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  res.json(createApiResponse(true, competitor));
});

// Update competitor tracking settings
export const updateCompetitor = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updateData = UpdateCompetitorSchema.parse(req.body);
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  const updatedCompetitor = await prisma.competitorTracking.update({
    where: { id },
    data: {
      ...updateData,
      updatedAt: new Date(),
    },
    include: {
      creator: true,
    },
  });

  res.json(
    createApiResponse(true, updatedCompetitor, 'Competitor updated successfully')
  );
});

// Remove competitor from tracking
export const removeCompetitor = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  await prisma.competitorTracking.delete({
    where: { id },
  });

  res.json(createApiResponse(true, null, 'Competitor removed successfully'));
});

// Refresh competitor data from TikTok API
export const refreshCompetitor = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
    include: { creator: true },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  const tikTokService = createTikTokApiService();

  try {
    // Fetch fresh data from TikTok API
    const userInfo = await tikTokService.getUserInfo(competitor.creator.username);
    const updatedCreatorData = transformTikTokUserToCreator(userInfo);

    // Update creator with fresh data
    const updatedCreator = await prisma.creator.update({
      where: { id: competitor.creatorId },
      data: {
        displayName: updatedCreatorData.displayName,
        bio: updatedCreatorData.bio,
        followerCount: updatedCreatorData.followerCount,
        followingCount: updatedCreatorData.followingCount,
        likesCount: updatedCreatorData.likesCount,
        videoCount: updatedCreatorData.videoCount,
        averageViews: updatedCreatorData.averageViews,
        engagementRate: updatedCreatorData.engagementRate,
        profileImageUrl: updatedCreatorData.profileImageUrl,
        isVerified: updatedCreatorData.isVerified,
        updatedAt: new Date(),
      },
    });

    // Check for significant changes and create alerts if needed
    const followerIncrease = updatedCreator.followerCount - competitor.creator.followerCount;
    const engagementIncrease = updatedCreator.engagementRate - competitor.creator.engagementRate;

    const alerts = [];

    // Follower milestone alert (10k+ increase)
    if (followerIncrease >= 10000) {
      alerts.push({
        competitorTrackingId: competitor.id,
        alertType: 'FOLLOWER_MILESTONE' as const,
        title: 'Follower Milestone Reached',
        description: `${competitor.creator.displayName || competitor.creator.username} gained ${followerIncrease.toLocaleString()} followers`,
        metadata: { followerIncrease, newFollowerCount: updatedCreator.followerCount },
      });
    }

    // Engagement spike alert (2%+ increase)
    if (engagementIncrease >= 2) {
      alerts.push({
        competitorTrackingId: competitor.id,
        alertType: 'ENGAGEMENT_SPIKE' as const,
        title: 'Engagement Rate Spike',
        description: `${competitor.creator.displayName || competitor.creator.username} had a ${engagementIncrease.toFixed(1)}% engagement increase`,
        metadata: { engagementIncrease, newEngagementRate: updatedCreator.engagementRate },
      });
    }

    // Create alerts if any were generated
    if (alerts.length > 0) {
      await prisma.competitorAlert.createMany({
        data: alerts,
      });
    }

    const refreshedCompetitor = await prisma.competitorTracking.findUnique({
      where: { id },
      include: { creator: true },
    });

    res.json(
      createApiResponse(true, refreshedCompetitor, 'Competitor data refreshed successfully')
    );
  } catch (error: any) {
    if (error.message.includes('TikTok')) {
      throw new AppError('Failed to refresh competitor data from TikTok', 400);
    }
    throw error;
  }
});

// Compare two competitors
export const compareCompetitors = asyncHandler(async (req: Request, res: Response) => {
  const { id1, id2 } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const [competitor1, competitor2] = await Promise.all([
    prisma.competitorTracking.findFirst({
      where: { id: id1, userId },
      include: { creator: true },
    }),
    prisma.competitorTracking.findFirst({
      where: { id: id2, userId },
      include: { creator: true },
    }),
  ]);

  if (!competitor1 || !competitor2) {
    throw new AppError('One or both competitors not found', 404);
  }

  const comparison = {
    competitor1: competitor1.creator,
    competitor2: competitor2.creator,
    metrics: {
      followerDifference: competitor1.creator.followerCount - competitor2.creator.followerCount,
      followerDifferencePercent: competitor2.creator.followerCount > 0 
        ? ((competitor1.creator.followerCount - competitor2.creator.followerCount) / competitor2.creator.followerCount) * 100
        : 0,
      engagementDifference: competitor1.creator.engagementRate - competitor2.creator.engagementRate,
      likesDifference: competitor1.creator.likesCount - competitor2.creator.likesCount,
      videosDifference: competitor1.creator.videoCount - competitor2.creator.videoCount,
    },
    winner: {
      followers: competitor1.creator.followerCount > competitor2.creator.followerCount 
        ? competitor1.creator.username 
        : competitor2.creator.username,
      engagement: competitor1.creator.engagementRate > competitor2.creator.engagementRate 
        ? competitor1.creator.username 
        : competitor2.creator.username,
      content: competitor1.creator.videoCount > competitor2.creator.videoCount 
        ? competitor1.creator.username 
        : competitor2.creator.username,
    },
  };

  res.json(createApiResponse(true, comparison));
});

// Get competitor alerts
export const getCompetitorAlerts = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented
  
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const isRead = req.query.isRead === 'true' ? true : req.query.isRead === 'false' ? false : undefined;

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  const where: any = { competitorTrackingId: id };
  if (isRead !== undefined) {
    where.isRead = isRead;
  }

  const [alerts, total] = await Promise.all([
    prisma.competitorAlert.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    }),
    prisma.competitorAlert.count({ where }),
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json(
    createApiResponse(true, {
      alerts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })
  );
});

// Mark alerts as read
export const markAlertsAsRead = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { alertIds } = req.body;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  const updateResult = await prisma.competitorAlert.updateMany({
    where: {
      id: { in: alertIds },
      competitorTrackingId: id,
    },
    data: { isRead: true },
  });

  res.json(
    createApiResponse(true, { updatedCount: updateResult.count }, 'Alerts marked as read')
  );
});

// Generate competitor analysis
export const generateAnalysis = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { analysisType, userCreatorData } = req.body;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  let analysis;

  try {
    switch (analysisType) {
      case 'STRENGTHS_WEAKNESSES':
        analysis = await competitorAnalysisService.generateStrengthsWeaknessesAnalysis(id, userCreatorData);
        break;
      case 'CONTENT_ANALYSIS':
        analysis = await competitorAnalysisService.generateContentAnalysis(id);
        break;
      case 'GAP_OPPORTUNITIES':
        if (!userCreatorData) {
          throw new AppError('User creator data is required for gap analysis', 400);
        }
        analysis = await competitorAnalysisService.generateGapAnalysis(id, userCreatorData);
        break;
      case 'GROWTH_STRATEGY':
        analysis = await competitorAnalysisService.generateGrowthStrategy(id);
        break;
      default:
        throw new AppError('Invalid analysis type', 400);
    }

    res.json(
      createApiResponse(true, analysis, 'Analysis generated successfully')
    );
  } catch (error: any) {
    if (error.message.includes('AI') || error.message.includes('analysis')) {
      throw new AppError('Failed to generate analysis', 500);
    }
    throw error;
  }
});

// Get competitor analyses
export const getAnalyses = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented
  
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
  const analysisType = req.query.analysisType as string;

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  const where: any = { competitorTrackingId: id };
  if (analysisType) {
    where.analysisType = analysisType;
  }

  const [analyses, total] = await Promise.all([
    prisma.competitorAnalysis.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip: (page - 1) * limit,
      take: limit,
    }),
    prisma.competitorAnalysis.count({ where }),
  ]);

  const totalPages = Math.ceil(total / limit);

  res.json(
    createApiResponse(true, {
      analyses,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    })
  );
});

// Get a specific analysis
export const getAnalysis = asyncHandler(async (req: Request, res: Response) => {
  const { id, analysisId } = req.params;
  const userId = 'user_test'; // TODO: Get from auth when implemented

  const competitor = await prisma.competitorTracking.findFirst({
    where: { id, userId },
  });

  if (!competitor) {
    throw new AppError('Competitor not found', 404);
  }

  const analysis = await prisma.competitorAnalysis.findFirst({
    where: {
      id: analysisId,
      competitorTrackingId: id,
    },
  });

  if (!analysis) {
    throw new AppError('Analysis not found', 404);
  }

  res.json(createApiResponse(true, analysis));
});