import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';

const GetCreatorsQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.enum(['followerCount', 'totalGMV', 'engagementRate']).default('totalGMV'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const getCreators = asyncHandler(async (req: Request, res: Response) => {
  const query = GetCreatorsQuerySchema.parse(req.query);

  const orderBy = {
    [query.sortBy]: query.sortOrder,
  };

  const [creators, total] = await Promise.all([
    prisma.creator.findMany({
      orderBy,
      skip: (query.page - 1) * query.limit,
      take: query.limit,
      include: {
        creatorProducts: {
          include: {
            product: true,
          },
          orderBy: {
            gmv: 'desc',
          },
          take: 3, // Top 3 products
        },
      },
    }),
    prisma.creator.count(),
  ]);

  const totalPages = Math.ceil(total / query.limit);

  res.json(
    createApiResponse(true, {
      creators,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNext: query.page < totalPages,
        hasPrev: query.page > 1,
      },
    })
  );
});

export const getCreator = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const creator = await prisma.creator.findUnique({
    where: { id },
    include: {
      creatorProducts: {
        include: {
          product: true,
        },
        orderBy: {
          gmv: 'desc',
        },
      },
    },
  });

  if (!creator) {
    throw new AppError('Creator not found', 404);
  }

  res.json(createApiResponse(true, creator));
});

export const getTopCreatorsByProduct = asyncHandler(async (req: Request, res: Response) => {
  const { productId } = req.params;

  const creatorProducts = await prisma.creatorProduct.findMany({
    where: { productId },
    include: {
      creator: true,
    },
    orderBy: [
      { gmv: 'desc' },
      { sales: 'desc' },
    ],
    take: 10,
  });

  const creators = creatorProducts.map(cp => ({
    ...cp.creator,
    productPerformance: {
      gmv: cp.gmv,
      sales: cp.sales,
    },
  }));

  res.json(createApiResponse(true, creators));
});

export const compareCreators = asyncHandler(async (req: Request, res: Response) => {
  const { creatorId1, creatorId2 } = req.params;

  const [creator1, creator2] = await Promise.all([
    prisma.creator.findUnique({
      where: { id: creatorId1 },
      include: {
        creatorProducts: {
          include: {
            product: true,
          },
        },
      },
    }),
    prisma.creator.findUnique({
      where: { id: creatorId2 },
      include: {
        creatorProducts: {
          include: {
            product: true,
          },
        },
      },
    }),
  ]);

  if (!creator1 || !creator2) {
    throw new AppError('One or both creators not found', 404);
  }

  const comparison = {
    creator1,
    creator2,
    metrics: {
      followerDifference: creator1.followerCount - creator2.followerCount,
      gmvDifference: creator1.totalGMV - creator2.totalGMV,
      engagementDifference: creator1.engagementRate - creator2.engagementRate,
    },
  };

  res.json(createApiResponse(true, comparison));
});