import { Request, Response } from 'express';
import { prisma } from '@xact-data/database';
import { createApiResponse, TikTokShopApiService } from '@xact-data/shared';
import { asyncHand<PERSON> } from '../middleware/error-handler';
import { z } from 'zod';
import { 
  AIGrowthCoachService, 
  AchievementService,
  createTikTokShopPerformanceService,
  transformTikTokShopPerformanceToDb,
  transformShopPerformanceToSummary,
  TikTokShopPerformanceQuery
} from '@xact-data/shared';
import type { 
  AffiliateDashboardStats, 
  AffiliateGoal, 
  Achievement, 
  AIInsight,
  CompetitiveBenchmark,
  ProductPerformance,
  ShopPerformanceSummary
} from '@xact-data/shared';

// Validation schemas
const CreateGoalSchema = z.object({
  goalType: z.enum(['MONTHLY_GMV', 'MONTHLY_COMMISSIONS', 'CONVERSION_RATE', 'PRODUCT_SALES', 'CUSTOM']),
  title: z.string().min(1).max(100),
  description: z.string().optional(),
  targetValue: z.number().min(0),
  targetDate: z.string().datetime().optional(),
});

const UpdateGoalSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  targetValue: z.number().min(0).optional(),
  targetDate: z.string().datetime().optional(),
  currentValue: z.number().min(0).optional(),
  isCompleted: z.boolean().optional(),
});

const CreateInsightSchema = z.object({
  insightType: z.enum(['PERFORMANCE_SUMMARY', 'GROWTH_OPPORTUNITY', 'PRODUCT_RECOMMENDATION', 'COMPETITIVE_GAP', 'ACTION_PLAN', 'TREND_ALERT']),
  title: z.string().min(1).max(200),
  content: z.string().min(1),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  actionItems: z.array(z.string()).optional(),
  metadata: z.record(z.unknown()).optional(),
  validUntil: z.string().datetime().optional(),
});

const ShopPerformanceQuerySchema = z.object({
  granularity: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'ALL']).default('DAILY'),
  currency: z.enum(['LOCAL', 'USD']).default('USD'),
  start_date_ge: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  end_date_lt: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  with_comparison: z.boolean().default(true),
  force_refresh: z.boolean().default(false),
});

// Helper function for successful API responses
const successResponse = <T>(data: T) => createApiResponse(true, data);
const errorResponse = (error: string) => createApiResponse(false, undefined, error);

// Get comprehensive affiliate dashboard stats
export const getDashboardStats = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  // Get date ranges
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);

  // Parallel queries for dashboard data
  const [
    currentAnalytics,
    previousAnalytics,
    activeGoals,
    completedGoals,
    recentAchievements,
    totalAchievements,
    unreadInsights,
    priorityInsights,
    topProductPerformance,
    competitiveBenchmarks,
  ] = await Promise.all([
    // Current period analytics
    prisma.analytics.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      orderBy: { date: 'desc' },
    }),
    
    // Previous period analytics for growth calculation
    prisma.analytics.findMany({
      where: {
        userId,
        date: {
          gte: sixtyDaysAgo,
          lt: thirtyDaysAgo,
        },
      },
    }),

    // Active goals
    prisma.affiliateGoal.findMany({
      where: {
        userId,
        isCompleted: false,
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    }),

    // Completed goals count
    prisma.affiliateGoal.count({
      where: {
        userId,
        isCompleted: true,
      },
    }),

    // Recent achievements
    prisma.achievement.findMany({
      where: { userId },
      orderBy: { unlockedAt: 'desc' },
      take: 5,
    }),

    // Total achievements count
    prisma.achievement.count({
      where: { userId },
    }),

    // Unread insights
    prisma.aIInsight.findMany({
      where: {
        userId,
        isRead: false,
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    }),

    // Priority insights
    prisma.aIInsight.findMany({
      where: {
        userId,
        priority: 'high',
        isRead: false,
      },
      orderBy: { createdAt: 'desc' },
      take: 5,
    }),

    // Top performing products
    prisma.productPerformance.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      include: {
        product: true,
      },
      orderBy: { gmv: 'desc' },
      take: 10,
    }),

    // Competitive benchmarks
    prisma.competitiveBenchmark.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      include: {
        competitor: true,
      },
      orderBy: { date: 'desc' },
      take: 5,
    }),
  ]);

  // Calculate current period totals
  const currentTotals = currentAnalytics.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  // Calculate previous period totals
  const previousTotals = previousAnalytics.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  // Calculate growth percentages
  const growth = {
    gmv: previousTotals.gmv > 0 
      ? ((currentTotals.gmv - previousTotals.gmv) / previousTotals.gmv) * 100 
      : 0,
    commissions: previousTotals.commissions > 0 
      ? ((currentTotals.commissions - previousTotals.commissions) / previousTotals.commissions) * 100 
      : 0,
    orders: previousTotals.orders > 0 
      ? ((currentTotals.orders - previousTotals.orders) / previousTotals.orders) * 100 
      : 0,
  };

  // Calculate average order value and conversion rate
  const averageOrderValue = currentTotals.orders > 0 ? currentTotals.gmv / currentTotals.orders : 0;
  const totalClicks = topProductPerformance.reduce((sum, p) => sum + p.clicks, 0);
  const conversionRate = totalClicks > 0 ? (currentTotals.orders / totalClicks) * 100 : 0;

  // Calculate goal progress
  const goalProgress = activeGoals.length > 0 
    ? activeGoals.reduce((sum, goal) => sum + (goal.currentValue / goal.targetValue * 100), 0) / activeGoals.length
    : 0;

  // Calculate achievement points (simple scoring system)
  const achievementPoints = totalAchievements * 10;

  // Process top products
  const topProducts = topProductPerformance
    .reduce((acc, curr) => {
      const existing = acc.find(p => p.productId === curr.productId);
      if (existing) {
        existing.gmv += curr.gmv;
        existing.commissions += curr.commissions;
        existing.orders += curr.orders;
        existing.clicks += curr.clicks;
      } else {
        acc.push(curr);
      }
      return acc;
    }, [] as typeof topProductPerformance)
    .sort((a, b) => b.gmv - a.gmv)
    .slice(0, 5)
    .map((perf, index) => ({
      product: perf.product,
      performance: {
        id: perf.id,
        userId: perf.userId,
        productId: perf.productId,
        date: perf.date,
        gmv: perf.gmv,
        commissions: perf.commissions,
        orders: perf.orders,
        clicks: perf.clicks,
        conversionRate: perf.conversionRate,
      },
      rank: index + 1,
    }));

  // Process competitive benchmarks
  const processedBenchmarks = competitiveBenchmarks.map(benchmark => ({
    competitor: benchmark.competitor,
    benchmark: {
      id: benchmark.id,
      userId: benchmark.userId,
      competitorId: benchmark.competitorId,
      metric: benchmark.metric,
      userValue: benchmark.userValue,
      competitorValue: benchmark.competitorValue,
      period: benchmark.period,
      date: benchmark.date,
      createdAt: benchmark.createdAt,
    },
    performance: benchmark.userValue > benchmark.competitorValue 
      ? 'ahead' as const
      : benchmark.userValue < benchmark.competitorValue 
        ? 'behind' as const 
        : 'tied' as const,
  }));

  const dashboardStats: AffiliateDashboardStats = {
    overview: {
      totalGMV: currentTotals.gmv,
      totalCommissions: currentTotals.commissions,
      totalOrders: currentTotals.orders,
      averageOrderValue,
      conversionRate,
      growth,
    },
    goals: {
      active: activeGoals as AffiliateGoal[],
      completed: completedGoals,
      progress: goalProgress,
    },
    achievements: {
      recent: recentAchievements as Achievement[],
      total: totalAchievements,
      points: achievementPoints,
    },
    insights: {
      unread: unreadInsights as AIInsight[],
      priority: priorityInsights as AIInsight[],
    },
    topProducts,
    competitiveBenchmarks: processedBenchmarks,
  };

  res.json(successResponse(dashboardStats));
});

// Goals Management
export const getGoals = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { completed } = req.query;

  const goals = await prisma.affiliateGoal.findMany({
    where: {
      userId,
      ...(completed !== undefined && { isCompleted: completed === 'true' }),
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json(successResponse({ goals }));
});

export const createGoal = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const validatedData = CreateGoalSchema.parse(req.body);

  const goal = await prisma.affiliateGoal.create({
    data: {
      userId,
      goalType: validatedData.goalType,
      title: validatedData.title,
      description: validatedData.description,
      targetValue: validatedData.targetValue,
      targetDate: validatedData.targetDate ? new Date(validatedData.targetDate) : null,
    },
  });

  res.status(201).json(successResponse(goal));
});

export const updateGoal = asyncHandler(async (req: Request, res: Response) => {
  const { userId, goalId } = req.params;
  const validatedData = UpdateGoalSchema.parse(req.body);

  const goal = await prisma.affiliateGoal.updateMany({
    where: {
      id: goalId,
      userId, // Ensure user can only update their own goals
    },
    data: {
      ...validatedData,
      targetDate: validatedData.targetDate ? new Date(validatedData.targetDate) : undefined,
      completedAt: validatedData.isCompleted ? new Date() : undefined,
    },
  });

  if (goal.count === 0) {
    return res.status(404).json(errorResponse('Goal not found'));
  }

  const updatedGoal = await prisma.affiliateGoal.findUnique({
    where: { id: goalId },
  });

  res.json(successResponse(updatedGoal));
});

export const deleteGoal = asyncHandler(async (req: Request, res: Response) => {
  const { userId, goalId } = req.params;

  const goal = await prisma.affiliateGoal.deleteMany({
    where: {
      id: goalId,
      userId, // Ensure user can only delete their own goals
    },
  });

  if (goal.count === 0) {
    return res.status(404).json(errorResponse('Goal not found'));
  }

  res.json(successResponse({ deleted: true }));
});

// Achievements
export const getAchievements = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  const achievements = await prisma.achievement.findMany({
    where: { userId },
    orderBy: { unlockedAt: 'desc' },
  });

  res.json(successResponse({ achievements }));
});

export const getAchievementProgress = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const achievementService = new AchievementService();

  const progress = await achievementService.getAchievementProgress(userId);
  res.json(successResponse(progress));
});

export const checkAchievements = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const achievementService = new AchievementService();

  await achievementService.checkAndAwardAchievements(userId);
  
  // Return updated achievements
  const achievements = await prisma.achievement.findMany({
    where: { userId },
    orderBy: { unlockedAt: 'desc' },
  });

  res.json(successResponse({ achievements, checked: true }));
});

// AI Insights
export const getInsights = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { unread, priority } = req.query;

  const insights = await prisma.aIInsight.findMany({
    where: {
      userId,
      ...(unread === 'true' && { isRead: false }),
      ...(priority && { priority: priority as string }),
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json(successResponse({ insights }));
});

export const createInsight = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const validatedData = CreateInsightSchema.parse(req.body);

  const insight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: validatedData.insightType,
      title: validatedData.title,
      content: validatedData.content,
      priority: validatedData.priority,
      actionItems: validatedData.actionItems,
      metadata: validatedData.metadata as any,
      validUntil: validatedData.validUntil ? new Date(validatedData.validUntil) : null,
    },
  });

  res.status(201).json(successResponse(insight));
});

export const markInsightRead = asyncHandler(async (req: Request, res: Response) => {
  const { userId, insightId } = req.params;

  const insight = await prisma.aIInsight.updateMany({
    where: {
      id: insightId,
      userId, // Ensure user can only update their own insights
    },
    data: {
      isRead: true,
    },
  });

  if (insight.count === 0) {
    return res.status(404).json(errorResponse('Insight not found'));
  }

  res.json(successResponse({ marked: true }));
});

// Product Performance
export const getProductPerformance = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { days = '30', productId } = req.query;

  const daysAgo = new Date();
  daysAgo.setDate(daysAgo.getDate() - parseInt(days as string));

  const performance = await prisma.productPerformance.findMany({
    where: {
      userId,
      date: { gte: daysAgo },
      ...(productId && { productId: productId as string }),
    },
    include: {
      product: true,
    },
    orderBy: { date: 'desc' },
  });

  res.json(successResponse({ performance }));
});

// Competitive Benchmarks
export const getCompetitiveBenchmarks = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { days = '30', competitorId, metric } = req.query;

  const daysAgo = new Date();
  daysAgo.setDate(daysAgo.getDate() - parseInt(days as string));

  const benchmarks = await prisma.competitiveBenchmark.findMany({
    where: {
      userId,
      date: { gte: daysAgo },
      ...(competitorId && { competitorId: competitorId as string }),
      ...(metric && { metric: metric as string }),
    },
    include: {
      competitor: true,
    },
    orderBy: { date: 'desc' },
  });

  res.json(successResponse({ benchmarks }));
});

// AI Growth Coach Endpoints
export const generatePerformanceSummary = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generatePerformanceSummary(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateGrowthOpportunity = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateGrowthOpportunity(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateProductRecommendations = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateProductRecommendations(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateCompetitiveGapAnalysis = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { competitorId } = req.body;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateCompetitiveGapAnalysis(userId, competitorId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

export const generateWeeklyActionPlan = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const growthCoach = new AIGrowthCoachService();

  const insight = await growthCoach.generateWeeklyActionPlan(userId);
  
  // Save insight to database
  const savedInsight = await prisma.aIInsight.create({
    data: {
      userId,
      insightType: insight.insightType,
      title: insight.title,
      content: insight.content,
      priority: insight.priority,
      actionItems: insight.actionItems,
      metadata: insight.metadata,
      validUntil: insight.validUntil,
    },
  });

  res.json(successResponse(savedInsight));
});

// Shop Performance Endpoints

// Fetch and sync shop performance data from TikTok Shop API
export const syncShopPerformance = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const validatedQuery = ShopPerformanceQuerySchema.parse(req.query);

  // Get user's TikTok Shop credentials
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      tiktokShopAccessToken: true,
      tiktokShopId: true,
      tiktokShopTokenExpiresAt: true,
    },
  });

  if (!user) {
    return res.status(404).json(errorResponse('User not found'));
  }

  if (!user.tiktokShopAccessToken || !user.tiktokShopId) {
    return res.status(400).json(
      errorResponse('TikTok Shop not connected. Please connect your TikTok Shop account first.')
    );
  }

  // Check if token is expired
  if (user.tiktokShopTokenExpiresAt && new Date() >= user.tiktokShopTokenExpiresAt) {
    return res.status(401).json(
      errorResponse('TikTok Shop access token expired. Please refresh your connection.')
    );
  }

  const shopPerformanceService = createTikTokShopPerformanceService(
    user.tiktokShopAccessToken,
    user.tiktokShopId
  );

  try {
    // Check if we have recent data unless force refresh is requested
    if (!validatedQuery.force_refresh) {
      const existingData = await prisma.shopPerformance.findFirst({
        where: {
          userId,
          date: {
            gte: new Date(validatedQuery.start_date_ge),
            lt: new Date(validatedQuery.end_date_lt),
          },
          granularity: validatedQuery.granularity,
          isComparison: false,
        },
        orderBy: { createdAt: 'desc' },
      });

      // If we have data from the last 4 hours, return it
      if (existingData && existingData.createdAt > new Date(Date.now() - 4 * 60 * 60 * 1000)) {
        const allData = await prisma.shopPerformance.findMany({
          where: {
            userId,
            date: {
              gte: new Date(validatedQuery.start_date_ge),
              lt: new Date(validatedQuery.end_date_lt),
            },
            granularity: validatedQuery.granularity,
          },
          orderBy: { date: 'asc' },
        });

        const summary = transformShopPerformanceToSummary(allData);
        return res.json(successResponse(summary));
      }
    }

    // Fetch fresh data from TikTok Shop API
    const apiQuery: TikTokShopPerformanceQuery = {
      granularity: validatedQuery.granularity,
      currency: validatedQuery.currency,
      start_date_ge: validatedQuery.start_date_ge,
      end_date_lt: validatedQuery.end_date_lt,
      with_comparison: validatedQuery.with_comparison,
    };

    const apiResponse = await shopPerformanceService.getShopPerformance(apiQuery);
    
    // Transform and save to database
    const transformedData = transformTikTokShopPerformanceToDb(
      userId,
      apiResponse,
      validatedQuery.granularity
    );

    // Upsert data (update if exists, create if not)
    for (const record of transformedData) {
      await prisma.shopPerformance.upsert({
        where: {
          userId_date_granularity_isComparison: {
            userId: record.userId,
            date: record.date,
            granularity: record.granularity,
            isComparison: record.isComparison,
          },
        },
        update: {
          gmv: record.gmv,
          currency: record.currency,
          orders: record.orders,
          skuOrders: record.skuOrders,
          unitsSold: record.unitsSold,
          buyers: record.buyers,
          avgOrderValue: record.avgOrderValue,
          productImpressions: record.productImpressions,
          productPageViews: record.productPageViews,
          avgProductPageVisitors: record.avgProductPageVisitors,
          gmvLive: record.gmvLive,
          gmvVideo: record.gmvVideo,
          gmvShop: record.gmvShop,
          buyersLive: record.buyersLive,
          buyersVideo: record.buyersVideo,
          buyersShop: record.buyersShop,
          impressionsLive: record.impressionsLive,
          impressionsVideo: record.impressionsVideo,
          impressionsShop: record.impressionsShop,
          pageViewsLive: record.pageViewsLive,
          pageViewsVideo: record.pageViewsVideo,
          pageViewsShop: record.pageViewsShop,
          refunds: record.refunds,
          cancellationsAndReturns: record.cancellationsAndReturns,
          conversionRate: record.conversionRate,
          estimatedCommissions: record.estimatedCommissions,
          tiktokRequestId: record.tiktokRequestId,
          rawData: record.rawData as any,
          updatedAt: new Date(),
        },
        create: record as any,
      });
    }

    // Fetch the saved data and return summary
    const savedData = await prisma.shopPerformance.findMany({
      where: {
        userId,
        date: {
          gte: new Date(validatedQuery.start_date_ge),
          lt: new Date(validatedQuery.end_date_lt),
        },
        granularity: validatedQuery.granularity,
      },
      orderBy: { date: 'asc' },
    });

    const summary = transformShopPerformanceToSummary(savedData);
    res.json(successResponse(summary));

  } catch (error: any) {
    console.error('Error syncing shop performance:', error);
    if (error.message.includes('TikTok Shop Performance API')) {
      return res.status(503).json(errorResponse(`TikTok Shop API error: ${error.message}`));
    }
    throw error;
  }
});

// Get shop performance summary for dashboard
export const getShopPerformanceSummary = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { days = '30', granularity = 'DAILY' } = req.query;

  const daysAgo = new Date();
  daysAgo.setDate(daysAgo.getDate() - parseInt(days as string));

  const performanceData = await prisma.shopPerformance.findMany({
    where: {
      userId,
      date: { gte: daysAgo },
      granularity: granularity as string,
    },
    orderBy: { date: 'asc' },
  });

  if (performanceData.length === 0) {
    // If no data, try to sync for the last 30 days
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 30);

    return res.status(404).json(
      errorResponse(
        'No shop performance data found. Use the sync endpoint to fetch data from TikTok Shop API first.'
      )
    );
  }

  const summary = transformShopPerformanceToSummary(performanceData);
  res.json(successResponse(summary));
});

// Get detailed shop performance data
export const getShopPerformanceDetails = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { 
    start_date, 
    end_date, 
    granularity = 'DAILY',
    include_comparison = 'true' 
  } = req.query;

  const whereClause: any = {
    userId,
    granularity: granularity as string,
  };

  if (start_date && end_date) {
    whereClause.date = {
      gte: new Date(start_date as string),
      lt: new Date(end_date as string),
    };
  }

  if (include_comparison !== 'true') {
    whereClause.isComparison = false;
  }

  const performanceData = await prisma.shopPerformance.findMany({
    where: whereClause,
    orderBy: { date: 'asc' },
  });

  res.json(successResponse({ performance: performanceData }));
});

// Get shop performance for last 30 days (quick endpoint)
export const getLast30DaysPerformance = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  // Try to get existing data first
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const existingData = await prisma.shopPerformance.findMany({
    where: {
      userId,
      date: { gte: thirtyDaysAgo },
      granularity: 'DAILY',
    },
    orderBy: { date: 'asc' },
  });

  if (existingData.length > 0) {
    const summary = transformShopPerformanceToSummary(existingData);
    return res.json(successResponse(summary));
  }

  // If no data exists, try to sync from API
  try {
    // Get user's TikTok Shop credentials
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        tiktokShopAccessToken: true,
        tiktokShopId: true,
        tiktokShopTokenExpiresAt: true,
      },
    });

    if (!user || !user.tiktokShopAccessToken || !user.tiktokShopId) {
      return res.status(400).json(
        errorResponse('TikTok Shop not connected. Please connect your TikTok Shop account first.')
      );
    }

    // Check if token is expired
    if (user.tiktokShopTokenExpiresAt && new Date() >= user.tiktokShopTokenExpiresAt) {
      return res.status(401).json(
        errorResponse('TikTok Shop access token expired. Please refresh your connection.')
      );
    }

    const shopPerformanceService = createTikTokShopPerformanceService(
      user.tiktokShopAccessToken,
      user.tiktokShopId
    );
    const apiResponse = await shopPerformanceService.getLast30DaysPerformance();
    
    // Transform and save
    const transformedData = transformTikTokShopPerformanceToDb(userId, apiResponse, 'DAILY');
    
    // Save to database
    for (const record of transformedData) {
      await prisma.shopPerformance.create({
        data: record as any,
      });
    }

    const summary = transformShopPerformanceToSummary(transformedData);
    res.json(successResponse(summary));

  } catch (error: any) {
    console.error('Error fetching last 30 days performance:', error);
    res.status(503).json(
      errorResponse(
        'Unable to fetch shop performance data. Please check your TikTok Shop API credentials.'
      )
    );
  }
});

// Initialize TikTok Shop API service
const tikTokShopApiService = new TikTokShopApiService({
  apiKey: process.env.TIKTOK_SHOP_API_KEY || '6hi6rl0brml5g',
  apiSecret: process.env.TIKTOK_SHOP_API_SECRET || '85aca6a18764530340e7f57cd24316d3bf5816c9',
});

// Helper function to get user's TikTok Shop access token
const getUserTikTokShopToken = async (userId: string) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      tiktokShopAccessToken: true,
      tiktokShopRefreshToken: true,
      tiktokShopTokenExpiresAt: true,
      tiktokShopRefreshExpiresAt: true,
    },
  });

  if (!user || !user.tiktokShopAccessToken) {
    throw new Error('TikTok Shop account not connected');
  }

  // Check if token is expired
  if (user.tiktokShopTokenExpiresAt && user.tiktokShopTokenExpiresAt <= new Date()) {
    if (!user.tiktokShopRefreshToken || (user.tiktokShopRefreshExpiresAt && user.tiktokShopRefreshExpiresAt <= new Date())) {
      throw new Error('TikTok Shop tokens expired. Please reconnect your account.');
    }

    // Refresh the token
    const newTokens = await tikTokShopApiService.refreshAccessToken(user.tiktokShopRefreshToken);
    
    // Update user with new tokens
    const tokenExpiresAt = new Date(Date.now() + newTokens.expires_in * 1000);
    const refreshExpiresAt = new Date(Date.now() + newTokens.refresh_expires_in * 1000);
    
    await prisma.user.update({
      where: { id: userId },
      data: {
        tiktokShopAccessToken: newTokens.access_token,
        tiktokShopRefreshToken: newTokens.refresh_token,
        tiktokShopTokenExpiresAt: tokenExpiresAt,
        tiktokShopRefreshExpiresAt: refreshExpiresAt,
        updatedAt: new Date(),
      },
    });

    return newTokens.access_token;
  }

  return user.tiktokShopAccessToken;
};

// TikTok Shop Partner API Integration Controllers

/**
 * Sync data from TikTok Shop Partner API and store in our database
 */
export const syncTikTokShopData = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { startDate, endDate, granularity = 'DAILY' } = req.body;

  try {
    const accessToken = await getUserTikTokShopToken(userId);
    
    // Default to last 30 days if no dates provided
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const startDateStr = start.toISOString().split('T')[0];
    const endDateStr = end.toISOString().split('T')[0];

    // Fetch shop performance data
    const performanceData = await tikTokShopApiService.getShopPerformance(
      accessToken,
      startDateStr,
      endDateStr,
      granularity as 'DAILY' | 'WEEKLY' | 'MONTHLY'
    );

    // Fetch affiliate commission data
    const commissionData = await tikTokShopApiService.getAffiliateCommissions(
      accessToken,
      startDateStr,
      endDateStr
    );

    // Fetch product performance data
    const productData = await tikTokShopApiService.getProductPerformance(
      accessToken,
      startDateStr,
      endDateStr
    );

    // Store performance data in ShopPerformance table
    for (const perf of performanceData) {
      await prisma.shopPerformance.upsert({
        where: {
          userId_date_granularity_isComparison: {
            userId,
            date: new Date(perf.date),
            granularity: perf.granularity,
            isComparison: false,
          },
        },
        update: {
          gmv: perf.gmv,
          orders: perf.orders,
          unitsSold: perf.units_sold,
          buyers: perf.buyers,
          avgOrderValue: perf.avg_order_value,
          conversionRate: perf.conversion_rate,
          productImpressions: perf.impressions,
          estimatedCommissions: perf.gmv * 0.05, // Assume 5% commission rate
          rawData: perf as any,
          updatedAt: new Date(),
        },
        create: {
          userId,
          date: new Date(perf.date),
          granularity: perf.granularity,
          gmv: perf.gmv,
          orders: perf.orders,
          unitsSold: perf.units_sold,
          buyers: perf.buyers,
          avgOrderValue: perf.avg_order_value,
          conversionRate: perf.conversion_rate,
          productImpressions: perf.impressions,
          estimatedCommissions: perf.gmv * 0.05,
          isComparison: false,
          rawData: perf as any,
        },
      });
    }

    // Store analytics data from commissions
    for (const commission of commissionData) {
      await prisma.analytics.upsert({
        where: {
          userId_date: {
            userId,
            date: new Date(commission.date || commission.created_time),
          },
        },
        update: {
          commissions: commission.commission_amount || 0,
          gmv: commission.order_amount || 0,
          orders: 1, // Each commission record represents one order
          aov: commission.order_amount || 0,
          conversionRate: 0, // Will be calculated separately
        },
        create: {
          userId,
          date: new Date(commission.date || commission.created_time),
          commissions: commission.commission_amount || 0,
          gmv: commission.order_amount || 0,
          orders: 1,
          aov: commission.order_amount || 0,
          conversionRate: 0,
        },
      });
    }

    // Store product performance data
    for (const product of productData) {
      await prisma.productPerformance.upsert({
        where: {
          userId_productId_date: {
            userId,
            productId: product.product_id,
            date: new Date(product.date),
          },
        },
        update: {
          gmv: product.gmv || 0,
          commissions: product.commission || 0,
          orders: product.orders || 0,
          clicks: product.clicks || 0,
          conversionRate: product.conversion_rate || 0,
        },
        create: {
          userId,
          productId: product.product_id,
          date: new Date(product.date),
          gmv: product.gmv || 0,
          commissions: product.commission || 0,
          orders: product.orders || 0,
          clicks: product.clicks || 0,
          conversionRate: product.conversion_rate || 0,
        },
      });
    }

    res.json(successResponse({
      synced: true,
      performanceRecords: performanceData.length,
      commissionRecords: commissionData.length,
      productRecords: productData.length,
      dateRange: { startDate: startDateStr, endDate: endDateStr },
    }));

  } catch (error: any) {
    console.error('Error syncing TikTok Shop data:', error);
    if (error.message.includes('not connected') || error.message.includes('expired')) {
      return res.status(401).json(errorResponse(error.message));
    }
    res.status(500).json(errorResponse(`Failed to sync TikTok Shop data: ${error.message}`));
  }
});

/**
 * Get TikTok Shop commission data for affiliate dashboard
 */
export const getTikTokShopCommissions = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { startDate, endDate, limit = '100' } = req.query;

  try {
    const accessToken = await getUserTikTokShopToken(userId);
    
    // Default to last 30 days if no dates provided
    const end = endDate ? new Date(endDate as string) : new Date();
    const start = startDate ? new Date(startDate as string) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const startDateStr = start.toISOString().split('T')[0];
    const endDateStr = end.toISOString().split('T')[0];

    const commissionData = await tikTokShopApiService.getAffiliateCommissions(
      accessToken,
      startDateStr,
      endDateStr
    );

    // Calculate summary statistics
    const summary = {
      totalCommissions: commissionData.reduce((sum, c) => sum + (c.commission_amount || 0), 0),
      totalOrders: commissionData.length,
      totalGMV: commissionData.reduce((sum, c) => sum + (c.order_amount || 0), 0),
      averageOrderValue: commissionData.length > 0 
        ? commissionData.reduce((sum, c) => sum + (c.order_amount || 0), 0) / commissionData.length 
        : 0,
      averageCommissionRate: commissionData.length > 0
        ? commissionData.reduce((sum, c) => {
            const rate = (c.commission_amount || 0) / (c.order_amount || 1);
            return sum + rate;
          }, 0) / commissionData.length
        : 0,
    };

    res.json(successResponse({
      summary,
      commissions: commissionData.slice(0, parseInt(limit as string)),
      dateRange: { startDate: startDateStr, endDate: endDateStr },
      totalRecords: commissionData.length,
    }));

  } catch (error: any) {
    console.error('Error fetching TikTok Shop commissions:', error);
    if (error.message.includes('not connected') || error.message.includes('expired')) {
      return res.status(401).json(errorResponse(error.message));
    }
    res.status(500).json(errorResponse(`Failed to fetch commissions: ${error.message}`));
  }
});

/**
 * Get TikTok Shop product performance data
 */
export const getTikTokShopProductPerformance = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { startDate, endDate, productIds, limit = '50' } = req.query;

  try {
    const accessToken = await getUserTikTokShopToken(userId);
    
    // Default to last 30 days if no dates provided
    const end = endDate ? new Date(endDate as string) : new Date();
    const start = startDate ? new Date(startDate as string) : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const startDateStr = start.toISOString().split('T')[0];
    const endDateStr = end.toISOString().split('T')[0];

    const productIdsArray = productIds ? (productIds as string).split(',') : undefined;
    const productData = await tikTokShopApiService.getProductPerformance(
      accessToken,
      startDateStr,
      endDateStr,
      productIdsArray
    );

    // Calculate summary by product
    const productSummaries = productData.reduce((acc, curr) => {
      const productId = curr.product_id;
      if (!acc[productId]) {
        acc[productId] = {
          productId,
          productName: curr.product_name || `Product ${productId}`,
          totalGMV: 0,
          totalCommissions: 0,
          totalOrders: 0,
          totalClicks: 0,
          conversionRate: 0,
        };
      }
      
      acc[productId].totalGMV += curr.gmv || 0;
      acc[productId].totalCommissions += curr.commission || 0;
      acc[productId].totalOrders += curr.orders || 0;
      acc[productId].totalClicks += curr.clicks || 0;
      
      return acc;
    }, {} as Record<string, any>);

    // Calculate conversion rates
    Object.values(productSummaries).forEach((summary: any) => {
      summary.conversionRate = summary.totalClicks > 0 
        ? (summary.totalOrders / summary.totalClicks) * 100 
        : 0;
    });

    const sortedProducts = Object.values(productSummaries)
      .sort((a: any, b: any) => b.totalGMV - a.totalGMV)
      .slice(0, parseInt(limit as string));

    // Overall summary
    const overallSummary = {
      totalGMV: productData.reduce((sum, p) => sum + (p.gmv || 0), 0),
      totalCommissions: productData.reduce((sum, p) => sum + (p.commission || 0), 0),
      totalOrders: productData.reduce((sum, p) => sum + (p.orders || 0), 0),
      totalClicks: productData.reduce((sum, p) => sum + (p.clicks || 0), 0),
      uniqueProducts: Object.keys(productSummaries).length,
    };

    overallSummary['conversionRate'] = overallSummary.totalClicks > 0 
      ? (overallSummary.totalOrders / overallSummary.totalClicks) * 100 
      : 0;

    res.json(successResponse({
      summary: overallSummary,
      products: sortedProducts,
      rawData: productData,
      dateRange: { startDate: startDateStr, endDate: endDateStr },
      totalRecords: productData.length,
    }));

  } catch (error: any) {
    console.error('Error fetching TikTok Shop product performance:', error);
    if (error.message.includes('not connected') || error.message.includes('expired')) {
      return res.status(401).json(errorResponse(error.message));
    }
    res.status(500).json(errorResponse(`Failed to fetch product performance: ${error.message}`));
  }
});