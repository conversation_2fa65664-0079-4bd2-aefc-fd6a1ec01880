import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse, AlertSchema } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';

const CreateAlertSchema = AlertSchema.omit({ 
  id: true, 
  createdAt: true, 
  updatedAt: true 
});

export const getAlerts = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  const alerts = await prisma.alert.findMany({
    where: { userId },
    include: {
      product: true,
      alertFires: {
        orderBy: { firedAt: 'desc' },
        take: 5,
      },
    },
    orderBy: { createdAt: 'desc' },
  });

  res.json(createApiResponse(true, alerts));
});

export const createAlert = asyncHandler(async (req: Request, res: Response) => {
  const data = CreateAlertSchema.parse(req.body);

  // Check if alert already exists for this user/product combination
  const existingAlert = await prisma.alert.findFirst({
    where: {
      userId: data.userId,
      productId: data.productId,
    },
  });

  if (existingAlert) {
    throw new AppError('Alert already exists for this product', 400);
  }

  const alert = await prisma.alert.create({
    data: {
      userId: data.userId,
      productId: data.productId,
      threshold: data.threshold,
      isActive: data.isActive ?? true,
    },
    include: {
      product: true,
    },
  });

  res.status(201).json(createApiResponse(true, alert, 'Alert created successfully'));
});

export const updateAlert = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const data = CreateAlertSchema.partial().parse(req.body);

  const alert = await prisma.alert.update({
    where: { id },
    data,
    include: {
      product: true,
    },
  });

  res.json(createApiResponse(true, alert, 'Alert updated successfully'));
});

export const deleteAlert = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  await prisma.alert.delete({
    where: { id },
  });

  res.json(createApiResponse(true, null, 'Alert deleted successfully'));
});

export const getAlertFires = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  
  const alertFires = await prisma.alertFire.findMany({
    where: { userId },
    include: {
      alert: true,
      product: true,
    },
    orderBy: { firedAt: 'desc' },
    take: 50,
  });

  res.json(createApiResponse(true, alertFires));
});

export const markAlertFireAsNotified = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const alertFire = await prisma.alertFire.update({
    where: { id },
    data: { notified: true },
  });

  res.json(createApiResponse(true, alertFire, 'Alert fire marked as notified'));
});