import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '@xact-data/database';
import { createApiResponse } from '@xact-data/shared';
import { asyncHandler, AppError } from '../middleware/error-handler';

const GetAnalyticsQuerySchema = z.object({
  startDate: z.coerce.date(),
  endDate: z.coerce.date(),
  groupBy: z.enum(['day', 'week', 'month']).default('day'),
});

export const getUserAnalytics = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const query = GetAnalyticsQuerySchema.parse(req.query);

  const analytics = await prisma.analytics.findMany({
    where: {
      userId,
      date: {
        gte: query.startDate,
        lte: query.endDate,
      },
    },
    orderBy: {
      date: 'asc',
    },
  });

  // Calculate totals and averages
  const totals = analytics.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  const averages = {
    aov: analytics.length > 0 ? totals.gmv / totals.orders : 0,
    conversionRate: analytics.length > 0 
      ? analytics.reduce((sum, a) => sum + a.conversionRate, 0) / analytics.length 
      : 0,
  };

  res.json(
    createApiResponse(true, {
      analytics,
      summary: {
        ...totals,
        ...averages,
        period: {
          startDate: query.startDate,
          endDate: query.endDate,
          days: analytics.length,
        },
      },
    })
  );
});

export const getDashboardStats = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;

  // Get last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const [analytics, alerts, recentAlertFires] = await Promise.all([
    prisma.analytics.findMany({
      where: {
        userId,
        date: { gte: thirtyDaysAgo },
      },
      orderBy: { date: 'desc' },
    }),
    prisma.alert.count({
      where: { userId, isActive: true },
    }),
    prisma.alertFire.count({
      where: {
        userId,
        firedAt: { gte: thirtyDaysAgo },
        notified: false,
      },
    }),
  ]);

  const last30Days = analytics.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  // Calculate growth compared to previous 30 days
  const sixtyDaysAgo = new Date();
  sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

  const previous30Days = await prisma.analytics.findMany({
    where: {
      userId,
      date: {
        gte: sixtyDaysAgo,
        lt: thirtyDaysAgo,
      },
    },
  });

  const previousPeriod = previous30Days.reduce(
    (acc, curr) => ({
      gmv: acc.gmv + curr.gmv,
      commissions: acc.commissions + curr.commissions,
      orders: acc.orders + curr.orders,
    }),
    { gmv: 0, commissions: 0, orders: 0 }
  );

  const growth = {
    gmv: previousPeriod.gmv > 0 
      ? ((last30Days.gmv - previousPeriod.gmv) / previousPeriod.gmv) * 100 
      : 0,
    commissions: previousPeriod.commissions > 0 
      ? ((last30Days.commissions - previousPeriod.commissions) / previousPeriod.commissions) * 100 
      : 0,
    orders: previousPeriod.orders > 0 
      ? ((last30Days.orders - previousPeriod.orders) / previousPeriod.orders) * 100 
      : 0,
  };

  res.json(
    createApiResponse(true, {
      last30Days,
      growth,
      activeAlerts: alerts,
      unreadAlertFires: recentAlertFires,
      chartData: analytics.slice(-7), // Last 7 days for chart
    })
  );
});