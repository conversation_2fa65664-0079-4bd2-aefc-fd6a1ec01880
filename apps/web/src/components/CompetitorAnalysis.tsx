'use client';

import { useState, useEffect } from 'react';
import {
  ArrowLeftIcon,
  BarChart3Icon,
  TrendingUpIcon,
  TargetIcon,
  LightbulbIcon,
  CheckCircleIcon,
  LoaderIcon,
  RefreshCwIcon,
  FileTextIcon
} from 'lucide-react';
import { MagicWand01 } from '@untitled-ui/icons-react';
import { Competitor } from '@xact-data/shared';
import { apiGet, apiPost } from '../lib/api';

// Using shared Competitor type from @xact-data/shared

interface Analysis {
  id: string;
  analysisType: string;
  title: string;
  content: string;
  strengths?: string[];
  weaknesses?: string[];
  opportunities?: string[];
  actionItems?: string[];
  score?: number;
  createdAt: string;
}

interface CompetitorAnalysisProps {
  competitor: Competitor;
  onBack: () => void;
}

export function CompetitorAnalysis({ competitor, onBack }: CompetitorAnalysisProps) {
  const [analyses, setAnalyses] = useState<Analysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState<string | null>(null);
  const [selectedAnalysis, setSelectedAnalysis] = useState<Analysis | null>(null);

  useEffect(() => {
    fetchAnalyses();
  }, [competitor.id]);

  const fetchAnalyses = async () => {
    try {
      setLoading(true);
      const response = await apiGet(`api/competitors/${competitor.id}/analyses`);
      const data = await response.json();
      
      if (data.success) {
        setAnalyses(data.data.analyses || []);
      }
    } catch (error) {
      console.error('Failed to fetch analyses:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateAnalysis = async (analysisType: string) => {
    try {
      setGenerating(analysisType);
      const response = await apiPost(`api/competitors/${competitor.id}/analysis`, {
        analysisType,
        // TODO: Add user creator data for gap analysis
        userCreatorData: analysisType === 'GAP_OPPORTUNITIES' ? {} : undefined
      });

      const data = await response.json();
      
      if (data.success) {
        setAnalyses(prev => [data.data, ...prev]);
        setSelectedAnalysis(data.data);
      } else {
        alert(data.error || 'Failed to generate analysis');
      }
    } catch (error) {
      console.error('Failed to generate analysis:', error);
      alert('Failed to generate analysis');
    } finally {
      setGenerating(null);
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getAnalysisIcon = (type: string) => {
    switch (type) {
      case 'STRENGTHS_WEAKNESSES':
        return <BarChart3Icon className="h-5 w-5" />;
      case 'CONTENT_ANALYSIS':
        return <FileTextIcon className="h-5 w-5" />;
      case 'GAP_OPPORTUNITIES':
        return <TargetIcon className="h-5 w-5" />;
      case 'GROWTH_STRATEGY':
        return <TrendingUpIcon className="h-5 w-5" />;
      default:
        return <MagicWand01 className="h-5 w-5" />;
    }
  };

  const getAnalysisColor = (type: string) => {
    switch (type) {
      case 'STRENGTHS_WEAKNESSES':
        return 'bg-blue-100 text-blue-800';
      case 'CONTENT_ANALYSIS':
        return 'bg-purple-100 text-purple-800';
      case 'GAP_OPPORTUNITIES':
        return 'bg-orange-100 text-orange-800';
      case 'GROWTH_STRATEGY':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const analysisTypes = [
    {
      type: 'STRENGTHS_WEAKNESSES',
      title: 'Strengths & Weaknesses',
      description: 'Analyze what makes this competitor successful and where they can improve',
      icon: <BarChart3Icon className="h-6 w-6" />,
    },
    {
      type: 'CONTENT_ANALYSIS',
      title: 'Content Strategy',
      description: 'Deep dive into their content patterns, themes, and engagement tactics',
      icon: <FileTextIcon className="h-6 w-6" />,
    },
    {
      type: 'GAP_OPPORTUNITIES',
      title: 'Gap Analysis',
      description: 'Identify opportunities where you can outperform this competitor',
      icon: <TargetIcon className="h-6 w-6" />,
    },
    {
      type: 'GROWTH_STRATEGY',
      title: 'Growth Strategy',
      description: 'Learn from their success and create your own growth playbook',
      icon: <TrendingUpIcon className="h-6 w-6" />,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <button onClick={onBack} className="mr-4 p-2 hover:bg-gray-100 rounded-md">
              <ArrowLeftIcon className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI Analysis</h1>
              <p className="text-gray-600">
                {competitor.creator.displayName || competitor.creator.username}
                {competitor.nickname && ` (${competitor.nickname})`}
              </p>
            </div>
          </div>

          <button
            onClick={fetchAnalyses}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCwIcon className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Analysis Controls */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Generate Analysis</h2>
            <div className="space-y-3">
              {analysisTypes.map((analysisType) => (
                <button
                  key={analysisType.type}
                  onClick={() => generateAnalysis(analysisType.type)}
                  disabled={generating !== null}
                  className="w-full flex items-start p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="flex-shrink-0 text-blue-600 mt-1">
                    {generating === analysisType.type ? (
                      <LoaderIcon className="h-6 w-6 animate-spin" />
                    ) : (
                      analysisType.icon
                    )}
                  </div>
                  <div className="ml-3 text-left">
                    <h3 className="text-sm font-medium text-gray-900">
                      {analysisType.title}
                    </h3>
                    <p className="text-xs text-gray-600 mt-1">
                      {analysisType.description}
                    </p>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Previous Analyses */}
          <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Previous Analyses ({analyses.length})
            </h2>
            
            {loading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : analyses.length === 0 ? (
              <p className="text-sm text-gray-500">
                No analyses yet. Generate your first analysis using the buttons above.
              </p>
            ) : (
              <div className="space-y-2">
                {analyses.map((analysis) => (
                  <button
                    key={analysis.id}
                    onClick={() => setSelectedAnalysis(analysis)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedAnalysis?.id === analysis.id
                        ? 'border-blue-300 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center">
                        <span className="text-gray-600 mr-2">
                          {getAnalysisIcon(analysis.analysisType)}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getAnalysisColor(analysis.analysisType)}`}>
                          {analysis.analysisType.replace('_', ' ')}
                        </span>
                      </div>
                      {analysis.score && (
                        <span className="text-sm font-medium text-gray-900">
                          {analysis.score}/100
                        </span>
                      )}
                    </div>
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {analysis.title}
                    </h3>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(analysis.createdAt)}
                    </p>
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Analysis Display */}
        <div className="lg:col-span-2">
          {selectedAnalysis ? (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-xl font-bold text-gray-900">
                    {selectedAnalysis.title}
                  </h2>
                  {selectedAnalysis.score && (
                    <div className="flex items-center">
                      <span className="text-sm text-gray-500 mr-2">Score:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {selectedAnalysis.score}/100
                      </span>
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getAnalysisColor(selectedAnalysis.analysisType)}`}>
                    {selectedAnalysis.analysisType.replace('_', ' ')}
                  </span>
                  <span className="text-sm text-gray-500">
                    Generated {formatDate(selectedAnalysis.createdAt)}
                  </span>
                </div>
              </div>

              {/* Structured Data */}
              {(selectedAnalysis.strengths || selectedAnalysis.weaknesses || selectedAnalysis.opportunities || selectedAnalysis.actionItems) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {selectedAnalysis.strengths && selectedAnalysis.strengths.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-green-800 mb-3 flex items-center">
                        <CheckCircleIcon className="h-5 w-5 mr-2" />
                        Strengths
                      </h3>
                      <ul className="space-y-2">
                        {selectedAnalysis.strengths.map((strength, index) => (
                          <li key={index} className="flex items-start">
                            <span className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></span>
                            <span className="text-sm text-gray-700">{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedAnalysis.weaknesses && selectedAnalysis.weaknesses.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-red-800 mb-3 flex items-center">
                        <TargetIcon className="h-5 w-5 mr-2" />
                        Weaknesses
                      </h3>
                      <ul className="space-y-2">
                        {selectedAnalysis.weaknesses.map((weakness, index) => (
                          <li key={index} className="flex items-start">
                            <span className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2 mr-3"></span>
                            <span className="text-sm text-gray-700">{weakness}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedAnalysis.opportunities && selectedAnalysis.opportunities.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-yellow-800 mb-3 flex items-center">
                        <LightbulbIcon className="h-5 w-5 mr-2" />
                        Opportunities
                      </h3>
                      <ul className="space-y-2">
                        {selectedAnalysis.opportunities.map((opportunity, index) => (
                          <li key={index} className="flex items-start">
                            <span className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3"></span>
                            <span className="text-sm text-gray-700">{opportunity}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {selectedAnalysis.actionItems && selectedAnalysis.actionItems.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-blue-800 mb-3 flex items-center">
                        <CheckCircleIcon className="h-5 w-5 mr-2" />
                        Action Items
                      </h3>
                      <ul className="space-y-2">
                        {selectedAnalysis.actionItems.map((item, index) => (
                          <li key={index} className="flex items-start">
                            <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                            <span className="text-sm text-gray-700">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Full Content */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Full Analysis</h3>
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans leading-relaxed">
                    {selectedAnalysis.content}
                  </pre>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-center py-12">
                <LightbulbIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No analysis selected</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Generate a new analysis or select an existing one from the sidebar.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}