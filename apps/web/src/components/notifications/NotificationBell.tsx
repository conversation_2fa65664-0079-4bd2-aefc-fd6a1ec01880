'use client';

import React from 'react';
import { useNotifications } from '../../contexts/NotificationContext';
import Button from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Bell01 as BellIcon } from '@untitled-ui/icons-react';

export function NotificationBell() {
  const { unreadCount, togglePanel } = useNotifications();

  return (
    <div className="relative">
      <Button
        intent="gray"
        variant="ghost"
        size="sm"
        onClick={togglePanel}
        className="relative p-2"
      >
        <BellIcon className="w-5 h-5" />
        {unreadCount > 0 && (
          <div className="absolute -top-1 -right-1">
            <Badge 
              variant="solid" 
              intent="primary" 
              size="sm"
              className="min-w-[18px] h-[18px] text-xs flex items-center justify-center px-1"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          </div>
        )}
      </Button>
    </div>
  );
}
