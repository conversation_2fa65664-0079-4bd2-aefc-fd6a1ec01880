'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import toast from 'react-hot-toast';
import { 
  ShoppingBag, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Unplug,
  Calendar,
  Store
} from 'lucide-react';

interface TikTokShopConnectionStatus {
  isConnected: boolean;
  tokenExpired: boolean;
  shopInfo?: {
    shopId: string;
    shopName: string;
    shopRegion: string;
    connectedAt: string;
    tokenExpiresAt: string;
  };
}

export default function TikTokShopConnect() {
  const { data: session } = useSession();
  const [connectionStatus, setConnectionStatus] = useState<TikTokShopConnectionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);
  const [disconnecting, setDisconnecting] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api-production-7bd1.up.railway.app';

  // Check connection status on component mount
  useEffect(() => {
    if (session?.user?.id) {
      checkConnectionStatus();
    }
  }, [session]);

  const checkConnectionStatus = async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/status/${session.user.id}`);
      const data = await response.json();
      
      if (data.success) {
        setConnectionStatus(data.data);
      } else {
        console.error('Failed to check connection status:', data.message);
      }
    } catch (error) {
      console.error('Error checking connection status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    if (!session?.user?.id) return;

    setConnecting(true);
    
    try {
      // Get authorization URL
      const redirectUri = `${window.location.origin}/auth/tiktok-shop/callback`;
      const response = await fetch(
        `${apiUrl}/api/auth/tiktok-shop/auth-url?userId=${session.user.id}&redirectUri=${encodeURIComponent(redirectUri)}`
      );
      
      const data = await response.json();
      
      if (data.success) {
        // Redirect to TikTok Shop authorization
        window.location.href = data.data.authUrl;
      } else {
        toast.error(data.message || "Failed to generate authorization URL");
      }
    } catch (error) {
      console.error('Error connecting to TikTok Shop:', error);
      toast.error("An error occurred while connecting to TikTok Shop");
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!session?.user?.id) return;

    setDisconnecting(true);
    
    try {
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/disconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setConnectionStatus({
          isConnected: false,
          tokenExpired: false,
          shopInfo: undefined,
        });
        
        toast.success("Your TikTok Shop account has been disconnected");
      } else {
        toast.error(data.message || "Failed to disconnect TikTok Shop account");
      }
    } catch (error) {
      console.error('Error disconnecting TikTok Shop:', error);
      toast.error("An error occurred while disconnecting TikTok Shop");
    } finally {
      setDisconnecting(false);
    }
  };

  const handleRefreshToken = async () => {
    if (!session?.user?.id) return;

    setRefreshing(true);
    
    try {
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.id,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        await checkConnectionStatus(); // Refresh status
        toast.success("Your TikTok Shop access token has been refreshed");
      } else {
        toast.error(data.message || "Failed to refresh access token");
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
      toast.error("An error occurred while refreshing the token");
    } finally {
      setRefreshing(false);
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            TikTok Shop Connection
          </h3>
        </div>
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <ShoppingBag className="h-5 w-5" />
          TikTok Shop Connection
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Connect your TikTok Shop account to sync your affiliate dashboard data
        </p>
      </div>
      <div className="space-y-6">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="font-medium">Status:</span>
            {connectionStatus?.isConnected ? (
              connectionStatus.tokenExpired ? (
                <Badge variant="solid" intent="danger" className="flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Token Expired
                </Badge>
              ) : (
                <Badge variant="solid" intent="success" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Connected
                </Badge>
              )
            ) : (
              <Badge variant="soft" intent="gray">Not Connected</Badge>
            )}
          </div>
          
          {connectionStatus?.isConnected && (
            <Button
              onClick={checkConnectionStatus}
              variant="ghost"
              size="sm"
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          )}
        </div>

        {/* Shop Information */}
        {connectionStatus?.isConnected && connectionStatus.shopInfo && (
          <div className="space-y-3">
            <hr className="border-gray-200" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Store className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Shop Name</p>
                  <p className="text-sm text-muted-foreground">{connectionStatus.shopInfo.shopName}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <div>
                  <p className="text-sm font-medium">Shop ID</p>
                  <p className="text-sm text-muted-foreground font-mono">{connectionStatus.shopInfo.shopId}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <div>
                  <p className="text-sm font-medium">Region</p>
                  <p className="text-sm text-muted-foreground">{connectionStatus.shopInfo.shopRegion}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">Connected</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(connectionStatus.shopInfo.connectedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Token expires: {new Date(connectionStatus.shopInfo.tokenExpiresAt).toLocaleString()}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {!connectionStatus?.isConnected ? (
            <Button
              onClick={handleConnect}
              disabled={connecting}
              className="flex items-center gap-2"
            >
              {connecting && <RefreshCw className="h-4 w-4 animate-spin" />}
              <ExternalLink className="h-4 w-4" />
              Connect TikTok Shop
            </Button>
          ) : (
            <>
              {connectionStatus.tokenExpired && (
                <Button
                  onClick={handleRefreshToken}
                  disabled={refreshing}
                  variant="filled"
                  className="flex items-center gap-2"
                >
                  {refreshing && <RefreshCw className="h-4 w-4 animate-spin" />}
                  <RefreshCw className="h-4 w-4" />
                  Refresh Token
                </Button>
              )}
              
              <Button
                onClick={handleDisconnect}
                disabled={disconnecting}
                variant="filled"
                intent="danger"
                className="flex items-center gap-2"
              >
                {disconnecting && <RefreshCw className="h-4 w-4 animate-spin" />}
                <Unplug className="h-4 w-4" />
                Disconnect
              </Button>
            </>
          )}
        </div>

        {/* Help Text */}
        <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
          <p className="font-medium mb-1">How it works:</p>
          <ul className="text-xs space-y-1">
            <li>• Click "Connect TikTok Shop" to authorize our app</li>
            <li>• Grant permissions in your TikTok Shop seller dashboard</li>
            <li>• We'll sync your sales data, commissions, and product performance</li>
            <li>• Your data will appear in the affiliate dashboard automatically</li>
          </ul>
        </div>
      </div>
    </Card>
  );
}