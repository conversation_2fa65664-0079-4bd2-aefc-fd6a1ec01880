'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import Button from '@/components/ui/Button';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { MetricsCard } from '@/components/charts/MetricsCard';
import {
  MagicWand01 as MagicWandIcon,
  TrendUp01 as TrendUpIcon,
  Target04 as TargetIcon
} from '@untitled-ui/icons-react';
import Link from 'next/link';
import { CoachCommentary } from './coach/CoachCommentary';
import { FeaturedIcon } from './ui/FeaturedIcon';
import { KanbanProvider, KanbanBoard, KanbanHeader, KanbanCards, KanbanCard } from './ui/kibo-ui/kanban';



export function AffiliateDashboard() {
  const [metricsTimeframe, setMetricsTimeframe] = useState('30d');

  const timeframeOptions = [
    { label: '7 days', value: '7d' },
    { label: '30 days', value: '30d' },
    { label: '90 days', value: '90d' },
    { label: '1 year', value: '1y' }
  ];

  // Mock data that changes based on timeframe
  const getMetricsData = (timeframe: string) => {
    const baseData = {
      '7d': {
        totalGMV: 12500,
        totalCommissions: 625,
        totalOrders: 45,
        conversionRate: 3.2,
        growth: { gmv: 8.5, commissions: 12.3, orders: 15.2 }
      },
      '30d': {
        totalGMV: 85000,
        totalCommissions: 4250,
        totalOrders: 340,
        conversionRate: 3.8,
        growth: { gmv: 15.2, commissions: 18.7, orders: 22.1 }
      },
      '90d': {
        totalGMV: 245000,
        totalCommissions: 12250,
        totalOrders: 980,
        conversionRate: 4.1,
        growth: { gmv: 28.4, commissions: 32.1, orders: 35.6 }
      },
      '1y': {
        totalGMV: 980000,
        totalCommissions: 49000,
        totalOrders: 3920,
        conversionRate: 4.5,
        growth: { gmv: 45.8, commissions: 52.3, orders: 48.9 }
      }
    };
    return baseData[timeframe as keyof typeof baseData] || baseData['30d'];
  };

  const currentMetrics = getMetricsData(metricsTimeframe);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };



  // Mock chart data
  const revenueData = [
    { name: 'Jan', revenue: 4000, commission: 800 },
    { name: 'Feb', revenue: 3000, commission: 600 },
    { name: 'Mar', revenue: 8000, commission: 1600 },
    { name: 'Apr', revenue: 4500, commission: 900 },
    { name: 'May', revenue: 6000, commission: 1200 },
    { name: 'Jun', revenue: 5500, commission: 1100 },
    { name: 'Jul', revenue: 7000, commission: 1400 },
  ];

  const ordersData = [
    { name: 'Week 1', value: 120 },
    { name: 'Week 2', value: 150 },
    { name: 'Week 3', value: 180 },
    { name: 'Week 4', value: 200 },
    { name: 'Week 5', value: 170 },
    { name: 'Week 6', value: 220 },
    { name: 'Week 7', value: 250 },
  ];



  return (
    <div className="space-y-4" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
      {/* Key Metrics Grid */}
      <Card className="p-6 relative overflow-hidden" data-rounded="default">
        <div className="absolute top-0 right-0 w-20 h-20 bg-gray-500/5 dark:bg-gray-400/5 rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-gray-400/5 dark:bg-gray-500/5 rounded-full blur-lg"></div>
        <div className="relative mb-3 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans">Key Performance Metrics</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-sans">Your affiliate performance at a glance</p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 dark:text-gray-400 font-sans">Timeframe:</span>
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
              {timeframeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setMetricsTimeframe(option.value)}
                  className={`px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200 ${
                    metricsTimeframe === option.value
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50'
                  }`}
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 min-h-[120px]">
          <MetricsCard
            title="Total GMV"
            value={formatCurrency(currentMetrics.totalGMV)}
            change={currentMetrics.growth.gmv}
            changeLabel={`vs last ${metricsTimeframe === '7d' ? 'week' : metricsTimeframe === '30d' ? 'month' : metricsTimeframe === '90d' ? 'quarter' : 'year'}`}
            description="Gross Merchandise Value"
          />
          <MetricsCard
            title="Commissions"
            value={formatCurrency(currentMetrics.totalCommissions)}
            change={currentMetrics.growth.commissions}
            changeLabel={`vs last ${metricsTimeframe === '7d' ? 'week' : metricsTimeframe === '30d' ? 'month' : metricsTimeframe === '90d' ? 'quarter' : 'year'}`}
            description="Your earnings"
          />
          <MetricsCard
            title="Total Orders"
            value={formatNumber(currentMetrics.totalOrders)}
            change={currentMetrics.growth.orders}
            changeLabel={`vs last ${metricsTimeframe === '7d' ? 'week' : metricsTimeframe === '30d' ? 'month' : metricsTimeframe === '90d' ? 'quarter' : 'year'}`}
            description="Orders processed"
          />
          <MetricsCard
            title="Conversion Rate"
            value={`${currentMetrics.conversionRate}%`}
            change={2.1}
            changeLabel={`vs last ${metricsTimeframe === '7d' ? 'week' : metricsTimeframe === '30d' ? 'month' : metricsTimeframe === '90d' ? 'quarter' : 'year'}`}
            description="Click to purchase rate"
          />
        </div>
      </Card>

      {/* Coach Commentary */}
      <CoachCommentary
        type="default"
        title="Coach Insights"
        message={`Great momentum this ${metricsTimeframe === '7d' ? 'week' : metricsTimeframe === '30d' ? 'month' : metricsTimeframe === '90d' ? 'quarter' : 'year'}! Your GMV is ${currentMetrics.growth.gmv > 0 ? 'up' : 'down'} ${Math.abs(currentMetrics.growth.gmv)}%. Focus on the AI Growth Coach recommendations below to accelerate your progress toward the next milestone.`}
        actionable={true}
      />

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Revenue Chart */}
        <Card className="p-6" data-rounded="default">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 font-sans">Revenue Trend</h3>
            <p className="text-sm text-gray-600 font-sans">GMV and Commissions over time</p>
          </div>
          <div className="h-64 flex items-center justify-start">
            <ResponsiveContainer width="100%" height="90%">
              <AreaChart data={revenueData} margin={{ top: 10, right: 40, left: 5, bottom: 10 }}>
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#9ca3af', fontFamily: 'General Sans' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#9ca3af', fontFamily: 'General Sans' }}
                  tickFormatter={(value) => `$${(value / 1000)}k`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    fontFamily: 'General Sans'
                  }}
                  formatter={(value, name) => [
                    `$${Number(value).toLocaleString()}`,
                    name === 'revenue' ? 'GMV' : 'Commissions'
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stroke="#93c5fd"
                  fill="url(#colorRevenue)"
                  strokeWidth={1.5}
                />
                <Area
                  type="monotone"
                  dataKey="commission"
                  stroke="#007FFF"
                  fill="url(#colorCommission)"
                  strokeWidth={2}
                />
                <defs>
                  <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#93c5fd" stopOpacity={0.2}/>
                    <stop offset="95%" stopColor="#93c5fd" stopOpacity={0.03}/>
                  </linearGradient>
                  <linearGradient id="colorCommission" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#007FFF" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#007FFF" stopOpacity={0.05}/>
                  </linearGradient>
                </defs>
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Orders Chart */}
        <Card className="p-6" data-rounded="default">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 font-sans">Orders Growth</h3>
            <p className="text-sm text-gray-600 font-sans">Weekly order volume</p>
          </div>
          <div className="h-64 flex items-center justify-start">
            <ResponsiveContainer width="100%" height="90%">
              <AreaChart data={ordersData} margin={{ top: 10, right: 40, left: 5, bottom: 10 }}>
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#9ca3af', fontFamily: 'General Sans' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#9ca3af', fontFamily: 'General Sans' }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    fontFamily: 'General Sans'
                  }}
                  formatter={(value) => [Number(value).toLocaleString(), 'Orders']}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="#007FFF"
                  fill="url(#colorOrders)"
                  strokeWidth={2}
                />
                <defs>
                  <linearGradient id="colorOrders" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#007FFF" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#007FFF" stopOpacity={0.05}/>
                  </linearGradient>
                </defs>
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      {/* GMV Goal Tracker */}
      <Card className="p-6" data-rounded="default">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 font-sans mb-2">Revenue Milestones</h3>
          <p className="text-sm text-gray-600 font-sans">Track your progress to the next goal</p>
        </div>

        {/* Revenue Progress Chart */}
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={[
                { name: 'Jan', value: 45000 },
                { name: 'Feb', value: 52000 },
                { name: 'Mar', value: 48000 },
                { name: 'Apr', value: currentMetrics.totalGMV },
                { name: 'May', value: 38000 },
                { name: 'Jun', value: 42000 },
                { name: 'Jul', value: 55000 }
              ]}
              margin={{ top: 20, right: 30, left: 40, bottom: 5 }}
            >
              <YAxis
                className="text-caption"
                width={40}
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => `$${(value / 1000)}K`}
                domain={[0, 60000]}
              />
              <XAxis
                className="text-caption"
                dataKey="name"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <CartesianGrid
                vertical={false}
                stroke="rgb(229 231 235)"
                strokeDasharray={3}
              />
              <Tooltip
                cursor={false}
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg font-sans">
                        <p className="text-sm font-semibold text-gray-900">
                          {label}: ${formatCurrency(payload[0].value as number)}
                        </p>
                      </div>
                    );
                  }
                  return null;
                }}
              />

              {/* Revenue Bars with Stripes */}
              <Bar
                radius={[4, 4, 0, 0]}
                fill="url(#blueStripes)"
                dataKey="value"
                barSize={20}
              />

              {/* Definitions */}
              <defs>
                {/* Blue Striped Pattern */}
                <pattern
                  id="blueStripes"
                  patternUnits="userSpaceOnUse"
                  width="4"
                  height="4"
                  patternTransform="rotate(45)"
                >
                  <rect width="4" height="4" fill="#3B82F6" />
                  <rect width="2" height="4" fill="rgba(255,255,255,0.2)" />
                </pattern>
              </defs>
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Milestone Legend */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          {[
            { amount: 10000, label: 'Starter', color: '#10B981', achieved: currentMetrics.totalGMV >= 10000 },
            { amount: 50000, label: 'Rising Star', color: '#8B5CF6', achieved: currentMetrics.totalGMV >= 50000 },
            { amount: 100000, label: 'Revenue Royalty', color: '#F59E0B', achieved: currentMetrics.totalGMV >= 100000 },
            { amount: 500000, label: 'Elite Creator', color: '#EF4444', achieved: currentMetrics.totalGMV >= 500000, striped: true }
          ].map((milestone) => (
            <div key={milestone.amount} className="flex items-center gap-2">
              <div
                className={`w-3 h-3 rounded-sm ${milestone.achieved ? 'opacity-100' : 'opacity-50'}`}
                style={{
                  backgroundColor: milestone.color,
                  backgroundImage: milestone.striped ? 'repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255,255,255,0.3) 2px, rgba(255,255,255,0.3) 4px)' : 'none'
                }}
              ></div>
              <span className={`text-xs font-sans ${milestone.achieved ? 'text-gray-900 font-semibold' : 'text-gray-500'}`}>
                {milestone.label}
              </span>
              <span className="text-xs text-gray-400 font-sans">
                ${(milestone.amount / 1000)}K
              </span>
            </div>
          ))}
        </div>
      </Card>

      {/* AI Growth Coach Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 min-h-[320px]">
        {/* Personalized Insights */}
        <Link href="/insights" className="block">
          <Card className="p-6 cursor-pointer transition-all duration-200 border border-gray-200 hover:border-gray-300 h-80" data-rounded="default">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <FeaturedIcon
                  icon={MagicWandIcon}
                  color="brand"
                  theme="outline"
                  size="lg"
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans">Smart Insights</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-sans">AI-powered recommendations</p>
                </div>
              </div>
              <Badge intent="gray" size="sm" className="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                3 New
              </Badge>
            </div>

            <div className="space-y-3">
              <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 font-sans">70% of sales from Product X</p>
                    <p className="text-xs text-gray-600 font-sans mt-1">Double down on livestreams for maximum impact</p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 font-sans">Peak conversion: 2-4 PM EST</p>
                    <p className="text-xs text-gray-600 font-sans mt-1">Schedule content during high-engagement hours</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 border-t border-dashed border-gray-300 pt-4">
              <Button
                intent="gray"
                variant="outlined"
                size="sm"
                className="w-full justify-center font-medium font-sans"
              >
                View All →
              </Button>
            </div>
        </Card>
        </Link>

        {/* Gap Analysis */}
        <Link href="/benchmarks" className="block">
          <Card className="p-6 cursor-pointer transition-all duration-200 border border-gray-200 hover:border-gray-300 h-80" data-rounded="default">
            <div className="flex items-center gap-3 mb-4">
              <FeaturedIcon
                icon={TrendUpIcon}
                color="brand"
                theme="outline"
                size="lg"
              />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 font-sans">Gap Analysis</h3>
                <p className="text-sm text-gray-600 font-sans">vs niche & competitors</p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div>
                  <p className="text-sm font-medium text-gray-900 font-sans">Conversion Rate</p>
                  <p className="text-xs text-gray-600 font-sans">vs niche average</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-green-600 font-sans">+15%</p>
                  <p className="text-xs text-gray-500 font-sans">Above avg</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div>
                  <p className="text-sm font-medium text-gray-900 font-sans">Content Frequency</p>
                  <p className="text-xs text-gray-600 font-sans">vs top performers</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-bold text-orange-600 font-sans">-23%</p>
                  <p className="text-xs text-gray-500 font-sans">Opportunity</p>
                </div>
              </div>
            </div>

            <div className="mt-4 border-t border-dashed border-gray-300 pt-4">
              <Button
                intent="gray"
                variant="outlined"
                size="sm"
                className="w-full justify-center font-medium font-sans"
              >
                View Details →
              </Button>
            </div>
        </Card>
        </Link>

        {/* Action Plans */}
        <Link href="/action-plans" className="block">
          <Card className="p-6 cursor-pointer transition-all duration-200 border border-gray-200 hover:border-gray-300 h-80" data-rounded="default">
            <div className="flex items-center gap-3 mb-4">
              <FeaturedIcon
                icon={TargetIcon}
                color="brand"
                theme="outline"
                size="lg"
              />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 font-sans">Action Plans</h3>
                <p className="text-sm text-gray-600 font-sans">Task progress overview</p>
              </div>
            </div>

            {/* Kanban Progress View */}
            <div className="h-48 overflow-hidden">
              <KanbanProvider
                columns={[
                  { id: 'in-progress', name: 'In Progress', color: '#F59E0B' }
                ]}
                data={[
                  { id: '1', name: 'Create 3 livestreams', description: 'Focus on Product X', priority: 'High', type: 'Task', column: 'in-progress' },
                  { id: '2', name: 'Post 2 TikToks daily', description: 'Increase frequency by 23%', priority: 'Medium', type: 'Task', column: 'in-progress' }
                ]}
                onDataChange={() => {}}
              >
                {(column) => (
                  <KanbanBoard id={column.id} key={column.id}>
                    <KanbanHeader>
                      <div className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full" style={{ backgroundColor: column.color }} />
                        <span className="text-sm font-medium text-gray-900 font-sans">{column.name}</span>
                        <span className="text-xs text-gray-500 font-sans">(2)</span>
                      </div>
                    </KanbanHeader>
                    <KanbanCards id={column.id}>
                      {(task: any) => (
                        <KanbanCard column={column.id} id={task.id} key={task.id} name={task.name}>
                          <div className="space-y-2">
                            <div className="text-xs font-medium text-gray-900 font-sans">{task.name}</div>
                            <div className="text-xs text-gray-600 font-sans">{task.description}</div>
                            <div className="flex items-center gap-1">
                              <span className="text-xs px-2 py-1 bg-orange-100 text-orange-700 rounded font-sans">{task.priority}</span>
                            </div>
                          </div>
                        </KanbanCard>
                      )}
                    </KanbanCards>
                  </KanbanBoard>
                )}
              </KanbanProvider>
            </div>


        </Card>
        </Link>
      </div>

    </div>
  );
}
