'use client';

import { TrophyI<PERSON>, StarIcon, FlameIcon, CrownIcon } from 'lucide-react';

interface Achievement {
  id: string;
  type: string;
  title: string;
  description: string;
  badgeIcon: string;
  badgeColor: string;
  unlockedAt: string;
  value?: number;
}

interface AchievementsSectionProps {
  achievements: {
    recent: Achievement[];
    total: number;
    points: number;
  };
}

export function AchievementsSection({ achievements }: AchievementsSectionProps) {
  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'FIRST_SALE': return StarIcon;
      case 'MONTHLY_MILESTONE': return TrophyIcon;
      case 'STREAK': return FlameIcon;
      case 'CONVERSION_KING': return CrownIcon;
      default: return TrophyIcon;
    }
  };

  const getAchievementColor = (type: string) => {
    switch (type) {
      case 'FIRST_SALE': return 'text-primary-600 bg-primary-100';
      case 'MONTHLY_MILESTONE': return 'text-primary-600 bg-primary-100';
      case 'STREAK': return 'text-gray-600 bg-gray-100';
      case 'CONVERSION_KING': return 'text-primary-600 bg-primary-100';
      default: return 'text-primary-600 bg-primary-100';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900">Achievements & Milestones</h3>
        <p className="text-gray-600">Celebrate your affiliate journey</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <TrophyIcon className="w-12 h-12 text-yellow-600 mx-auto mb-2" />
          <p className="text-3xl font-bold text-gray-900">{achievements.total}</p>
          <p className="text-sm text-gray-600">Total Achievements</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <StarIcon className="w-12 h-12 text-blue-600 mx-auto mb-2" />
          <p className="text-3xl font-bold text-gray-900">{achievements.points}</p>
          <p className="text-sm text-gray-600">Achievement Points</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-6 text-center">
          <FlameIcon className="w-12 h-12 text-red-600 mx-auto mb-2" />
          <p className="text-3xl font-bold text-gray-900">{achievements.recent.length}</p>
          <p className="text-sm text-gray-600">Recent Unlocks</p>
        </div>
      </div>

      {/* Recent Achievements */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-lg font-medium text-gray-900">Recent Achievements</h4>
        </div>
        
        <div className="p-6">
          {achievements.recent.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.recent.map((achievement) => {
                const Icon = getAchievementIcon(achievement.type);
                const colorClass = getAchievementColor(achievement.type);
                
                return (
                  <div key={achievement.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start">
                      <div className={`flex-shrink-0 p-3 rounded-lg ${colorClass}`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      <div className="ml-4 flex-1">
                        <h5 className="font-medium text-gray-900">{achievement.title}</h5>
                        <p className="text-sm text-gray-600 mt-1">{achievement.description}</p>
                        <p className="text-xs text-gray-500 mt-2">
                          {new Date(achievement.unlockedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <TrophyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No achievements unlocked yet</p>
              <p className="text-sm text-gray-400 mt-2">Keep working toward your goals to unlock achievements!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}