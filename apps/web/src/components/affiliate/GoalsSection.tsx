'use client';

import { useState } from 'react';
import { 
  PlusIcon, 
  TargetIcon, 
  TrendingUpIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  EditIcon
} from 'lucide-react';
import { apiPost, apiPut, apiDelete } from '../../lib/api';
import EmptyState from '../ui/EmptyState';
import Button from '../ui/Button';

interface Goal {
  id: string;
  goalType: string;
  title: string;
  description?: string;
  targetValue: number;
  currentValue: number;
  targetDate?: string;
  isCompleted: boolean;
  createdAt: string;
}

interface GoalsSectionProps {
  goals: {
    active: Goal[];
    completed: number;
    progress: number;
  };
  userId: string;
  onGoalsUpdate: () => void;
}

export function GoalsSection({ goals, userId, onGoalsUpdate }: GoalsSectionProps) {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);

  const handleCreateGoal = async (goalData: any) => {
    try {
      const response = await apiPost(`api/affiliate-dashboard/user/${userId}/goals`, goalData);
      const data = await response.json();
      
      if (data.success) {
        setShowCreateModal(false);
        onGoalsUpdate();
      } else {
        alert(data.error || 'Failed to create goal');
      }
    } catch (error) {
      console.error('Failed to create goal:', error);
      alert('Failed to create goal');
    }
  };

  const handleUpdateGoal = async (goalId: string, updates: any) => {
    try {
      const response = await apiPut(`api/affiliate-dashboard/user/${userId}/goals/${goalId}`, updates);
      const data = await response.json();
      
      if (data.success) {
        setEditingGoal(null);
        onGoalsUpdate();
      } else {
        alert(data.error || 'Failed to update goal');
      }
    } catch (error) {
      console.error('Failed to update goal:', error);
      alert('Failed to update goal');
    }
  };

  const handleDeleteGoal = async (goalId: string) => {
    if (!confirm('Are you sure you want to delete this goal?')) return;
    
    try {
      const response = await apiDelete(`api/affiliate-dashboard/user/${userId}/goals/${goalId}`);
      const data = await response.json();
      
      if (data.success) {
        onGoalsUpdate();
      } else {
        alert(data.error || 'Failed to delete goal');
      }
    } catch (error) {
      console.error('Failed to delete goal:', error);
      alert('Failed to delete goal');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getGoalTypeLabel = (type: string) => {
    switch (type) {
      case 'MONTHLY_GMV': return 'Monthly GMV';
      case 'MONTHLY_COMMISSIONS': return 'Monthly Commissions';
      case 'CONVERSION_RATE': return 'Conversion Rate';
      case 'PRODUCT_SALES': return 'Product Sales';
      case 'CUSTOM': return 'Custom Goal';
      default: return type;
    }
  };

  const formatGoalValue = (value: number, type: string) => {
    if (type === 'MONTHLY_GMV' || type === 'MONTHLY_COMMISSIONS') {
      return formatCurrency(value);
    }
    if (type === 'CONVERSION_RATE') {
      return `${value.toFixed(2)}%`;
    }
    return value.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-bold text-gray-900">Goals & Milestones</h3>
          <p className="text-gray-600">Track your progress toward affiliate success</p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <PlusIcon className="w-4 h-4 mr-2" />
          New Goal
        </button>
      </div>

      {/* Goals Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <TargetIcon className="w-8 h-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Goals</p>
              <p className="text-3xl font-bold text-gray-900">{goals.active.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <CheckCircleIcon className="w-8 h-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Completed Goals</p>
              <p className="text-3xl font-bold text-gray-900">{goals.completed}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center">
            <TrendingUpIcon className="w-8 h-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Progress</p>
              <p className="text-3xl font-bold text-gray-900">{goals.progress.toFixed(0)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Active Goals */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-lg font-medium text-gray-900">Active Goals</h4>
        </div>
        
        <div className="p-6">
          {goals.active.length > 0 ? (
            <div className="space-y-6">
              {goals.active.map((goal) => {
                const progress = (goal.currentValue / goal.targetValue) * 100;
                const isOverdue = goal.targetDate && new Date(goal.targetDate) < new Date();
                
                return (
                  <div key={goal.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h5 className="text-lg font-medium text-gray-900">{goal.title}</h5>
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            {getGoalTypeLabel(goal.goalType)}
                          </span>
                          {isOverdue && (
                            <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                              Overdue
                            </span>
                          )}
                        </div>
                        
                        {goal.description && (
                          <p className="text-gray-600 mt-1">{goal.description}</p>
                        )}
                        
                        <div className="mt-4">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">
                              {formatGoalValue(goal.currentValue, goal.goalType)} of {formatGoalValue(goal.targetValue, goal.goalType)}
                            </span>
                            <span className="font-medium text-gray-900">
                              {progress.toFixed(1)}%
                            </span>
                          </div>
                          
                          <div className="mt-2 w-full bg-gray-200 rounded-full h-3">
                            <div 
                              className={`h-3 rounded-full transition-all duration-500 ${
                                progress >= 100 ? 'bg-green-600' : 
                                progress >= 75 ? 'bg-blue-600' : 
                                progress >= 50 ? 'bg-yellow-600' : 'bg-red-600'
                              }`}
                              style={{ width: `${Math.min(progress, 100)}%` }}
                            />
                          </div>
                        </div>
                        
                        {goal.targetDate && (
                          <div className="flex items-center mt-3 text-sm text-gray-600">
                            <CalendarIcon className="w-4 h-4 mr-1" />
                            Target: {new Date(goal.targetDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => setEditingGoal(goal)}
                          className="p-2 text-gray-400 hover:text-gray-600"
                        >
                          <EditIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteGoal(goal.id)}
                          className="p-2 text-gray-400 hover:text-red-600"
                        >
                          <XCircleIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <EmptyState
              icon={TargetIcon}
              title="No active goals yet"
              description="Set your first goal to start tracking your progress and achievements"
              action={{
                label: "Create Your First Goal",
                onClick: () => setShowCreateModal(true),
                intent: 'primary'
              }}
            />
          )}
        </div>
      </div>

      {/* Create Goal Modal */}
      {showCreateModal && (
        <CreateGoalModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateGoal}
        />
      )}

      {/* Edit Goal Modal */}
      {editingGoal && (
        <EditGoalModal
          goal={editingGoal}
          onClose={() => setEditingGoal(null)}
          onSubmit={(updates) => handleUpdateGoal(editingGoal.id, updates)}
        />
      )}
    </div>
  );
}

function CreateGoalModal({ onClose, onSubmit }: { onClose: () => void; onSubmit: (data: any) => void }) {
  const [formData, setFormData] = useState({
    goalType: 'MONTHLY_COMMISSIONS',
    title: '',
    description: '',
    targetValue: '',
    targetDate: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      targetValue: parseFloat(formData.targetValue),
      targetDate: formData.targetDate || undefined,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Create New Goal</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Goal Type</label>
            <select
              value={formData.goalType}
              onChange={(e) => setFormData({ ...formData, goalType: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="MONTHLY_COMMISSIONS">Monthly Commissions</option>
              <option value="MONTHLY_GMV">Monthly GMV</option>
              <option value="CONVERSION_RATE">Conversion Rate</option>
              <option value="PRODUCT_SALES">Product Sales</option>
              <option value="CUSTOM">Custom Goal</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Description (Optional)</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Target Value</label>
            <input
              type="number"
              value={formData.targetValue}
              onChange={(e) => setFormData({ ...formData, targetValue: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
              min="0"
              step="0.01"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Target Date (Optional)</label>
            <input
              type="date"
              value={formData.targetDate}
              onChange={(e) => setFormData({ ...formData, targetDate: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Create Goal
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

function EditGoalModal({ goal, onClose, onSubmit }: { goal: Goal; onClose: () => void; onSubmit: (data: any) => void }) {
  const [formData, setFormData] = useState({
    title: goal.title,
    description: goal.description || '',
    targetValue: goal.targetValue.toString(),
    currentValue: goal.currentValue.toString(),
    targetDate: goal.targetDate ? goal.targetDate.split('T')[0] : '',
    isCompleted: goal.isCompleted,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      targetValue: parseFloat(formData.targetValue),
      currentValue: parseFloat(formData.currentValue),
      targetDate: formData.targetDate || undefined,
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Goal</h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Current Value</label>
              <input
                type="number"
                value={formData.currentValue}
                onChange={(e) => setFormData({ ...formData, currentValue: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
                min="0"
                step="0.01"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Target Value</label>
              <input
                type="number"
                value={formData.targetValue}
                onChange={(e) => setFormData({ ...formData, targetValue: e.target.value })}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
                min="0"
                step="0.01"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Target Date</label>
            <input
              type="date"
              value={formData.targetDate}
              onChange={(e) => setFormData({ ...formData, targetDate: e.target.value })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              checked={formData.isCompleted}
              onChange={(e) => setFormData({ ...formData, isCompleted: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <label className="ml-2 text-sm text-gray-700">Mark as completed</label>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Update Goal
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}