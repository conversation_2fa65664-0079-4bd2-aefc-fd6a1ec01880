'use client'

import { signIn, useSession } from 'next-auth/react'
import { useState } from 'react'

interface WhopLoginButtonProps {
  className?: string
  children?: React.ReactNode
}

export function WhopLoginButton({ className = '', children }: WhopLoginButtonProps) {
  const { data: session, status } = useSession()
  const [loading, setLoading] = useState(false)

  const handleSignIn = async () => {
    setLoading(true)
    try {
      await signIn('whop', { callbackUrl: '/dashboard' })
    } catch (error) {
      console.error('Sign in error:', error)
      setLoading(false)
    }
  }

  // Don't show login button if user is already signed in
  if (status === 'authenticated') {
    return null
  }

  return (
    <button
      onClick={handleSignIn}
      disabled={loading || status === 'loading'}
      className={`bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center ${className}`}
      data-rounded="default"
      style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
    >
      {loading || status === 'loading' ? (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-2"></div>
          Signing in...
        </div>
      ) : (
        <>
          <img
            src="/09png.png"
            alt="Xact Data"
            className="w-4 h-4 mr-2 object-contain"
          />
          {children || 'Continue with Xact Data'}
        </>
      )}
    </button>
  )
}