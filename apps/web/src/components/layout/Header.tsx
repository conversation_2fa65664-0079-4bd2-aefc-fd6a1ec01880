'use client'

import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { WhopLoginButton } from '@/components/auth/WhopLoginButton'

export function Header() {
  const { data: session, status } = useSession()

  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">Xact Data</h1>
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                Creator OS
              </span>
            </Link>
          </div>

          <nav className="hidden md:flex space-x-8">
            <Link href="/products" className="text-gray-500 hover:text-gray-900">
              Products
            </Link>
            <Link href="/alerts" className="text-gray-500 hover:text-gray-900">
              Alerts
            </Link>
            <Link href="/dashboard" className="text-gray-500 hover:text-gray-900">
              Affiliate Dashboard
            </Link>
            <Link href="/competitors" className="text-gray-500 hover:text-gray-900">
              Competitors
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            {status === 'authenticated' && session?.user ? (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  {session.user.image && (
                    <img
                      src={session.user.image}
                      alt={session.user.name || 'User'}
                      className="w-8 h-8 rounded-full"
                    />
                  )}
                  <span className="text-sm font-medium text-gray-700">
                    {session.user.name || session.user.email}
                  </span>
                </div>
                <button
                  onClick={() => signOut({ callbackUrl: '/' })}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Sign out
                </button>
              </div>
            ) : (
              <WhopLoginButton />
            )}
          </div>
        </div>
      </div>
    </header>
  )
}