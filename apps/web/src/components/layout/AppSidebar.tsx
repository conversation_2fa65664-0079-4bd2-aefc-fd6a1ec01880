'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useCallback, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import {
  Home01 as HomeIcon,
  ShoppingBag01 as ShoppingBagIcon,
  Bell01 as BellIcon,
  BarChart01 as BarChart3Icon,
  Users01 as UsersIcon,
  Settings01 as SettingsIcon,
  MagicWand01 as SparklesIcon,
  LayoutLeft as LayoutLeftIcon
} from '@untitled-ui/icons-react';

import DropdownMenu from '../ui/DropdownMenu';
import UserAccountDropdown from '../ui/UserAccountDropdown';
import { cn } from '../../lib/utils';

const navigation = [
  { name: 'Products', href: '/products', icon: ShoppingBagIcon },
  { name: 'Alerts', href: '/alerts', icon: BellIcon },
  { name: 'Creators', href: '/competitors', icon: UsersIcon },
  { name: 'Settings', href: '/settings', icon: SettingsIcon },
];



const dashboardItems = [
  { name: 'Overview', href: '/dashboard', icon: BarChart3Icon },
  { name: 'Insights', href: '/insights', icon: SparklesIcon },
];



export function AppSidebar() {
  const pathname = usePathname();
  const [isMinimized, setIsMinimized] = useState(false);

  // Persist minimized state in localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-minimized');
    if (savedState !== null) {
      setIsMinimized(JSON.parse(savedState));
    }
  }, []);

  // Stable callback functions
  const handleMinimize = useCallback(() => {
    setIsMinimized(true);
    localStorage.setItem('sidebar-minimized', 'true');
  }, []);

  const handleExpand = useCallback(() => {
    setIsMinimized(false);
    localStorage.setItem('sidebar-minimized', 'false');
  }, []);

  // Get user data from session or use mock for development
  const { data: session } = useSession();
  const isDevelopment = process.env.NODE_ENV === 'development';
  const mockUser = {
    name: 'Demo User',
    email: '<EMAIL>',
    image: null
  };

  const currentUser = isDevelopment ? mockUser : (session?.user || null);

  return (
    <div className={`flex h-screen ${isMinimized ? 'w-16' : 'w-56'} flex-col bg-white dark:bg-black relative overflow-visible`} style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
      {/* Subtle glow effects - only when not minimized */}
      {!isMinimized && (
        <>
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400/5 dark:bg-gray-600/10 rounded-full blur-3xl"></div>
          <div className="absolute top-4 left-4 w-20 h-20 bg-gray-400/3 dark:bg-gray-700/5 rounded-full blur-2xl"></div>
        </>
      )}

      {/* Logo Section */}
      <div className={`flex items-center ${isMinimized ? 'justify-center' : 'justify-between'} px-4 py-4 relative z-10 ${!isMinimized ? 'border-b border-gray-200 dark:border-gray-800' : ''}`}>
        <div className="flex items-center">
          <img
            src="/09png.png"
            alt="Xact Data Logo"
            className="object-contain"
            style={{ width: '28px', height: 'auto', maxHeight: '28px' }}
          />
        </div>
        {!isMinimized && (
          <button
            onClick={handleMinimize}
            className="p-1.5 text-gray-900 dark:text-gray-100 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          >
            <LayoutLeftIcon className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className={`flex-1 ${isMinimized ? 'px-2 py-2' : 'px-4 py-2 space-y-1'} overflow-visible`}>
        {/* Dashboard Section */}
        {!isMinimized ? (
          <DropdownMenu
            label="Dashboard"
            icon={HomeIcon}
            items={dashboardItems}
          />
        ) : (
          <div className="relative group">
            <Link
              href="/dashboard"
              className={cn(
                'flex items-center justify-center rounded-lg py-3 px-2 text-sm font-medium mx-2',
                pathname === '/dashboard'
                  ? 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white'
                  : 'text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900 hover:text-gray-900 dark:hover:text-white'
              )}
            >
              <HomeIcon className="h-6 w-6 text-gray-900 dark:text-gray-300 flex-shrink-0" />
            </Link>
            {/* Tooltip */}
            <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 px-3 py-2 bg-white dark:bg-gray-900 text-gray-900 dark:text-white text-xs rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999] invisible group-hover:visible">
              Dashboard
            </div>
          </div>
        )}

        {/* Navigation Items */}
        {navigation.map((item) => {
          const isActive = pathname === item.href;
          const isInsights = item.href === '/insights';

          return (
            <div key={item.name} className={isMinimized ? "relative group" : ""}>
              <Link
                href={item.href}
                className={cn(
                  isMinimized
                    ? 'flex items-center justify-center rounded-lg py-3 px-2 text-sm font-medium mx-2'
                    : 'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors',
                  isActive
                    ? 'bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white'
                    : isMinimized
                      ? 'text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900 hover:text-gray-900 dark:hover:text-white'
                      : 'text-gray-900 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-900 hover:text-gray-900 dark:hover:text-white'
                )}
                data-rounded="default"
              >
                <item.icon className={cn(
                  isMinimized ? "h-6 w-6" : "h-5 w-5",
                  isActive
                    ? "text-gray-900 dark:text-gray-100"
                    : "text-gray-900 dark:text-gray-100",
                  "flex-shrink-0"
                )} />
                {!isMinimized && (
                  <span className="flex items-center gap-2">
                    {item.name}
                    {isInsights && (
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-500 text-white">
                        New
                      </span>
                    )}
                  </span>
                )}
              </Link>
              {/* Tooltip for minimized state */}
              {isMinimized && (
                <div className="absolute left-full ml-2 top-1/2 -translate-y-1/2 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 text-xs rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-[9999] invisible group-hover:visible">
                  {item.name}
                </div>
              )}
            </div>
          );
        })}
      </nav>

      {/* Bottom Section */}
      <div className="mt-auto">
        {/* Minimizer Button (when minimized, show above user profile) */}
        {isMinimized && (
          <div className="px-2 pb-2">
            <button
              onClick={handleExpand}
              className="w-full flex items-center justify-center p-2 rounded-lg transition-colors text-gray-900 hover:bg-gray-100 hover:text-gray-700"
              title="Expand sidebar"
            >
              <LayoutLeftIcon className="h-6 w-6 rotate-180" />
            </button>
          </div>
        )}

        {/* User Profile Section */}
        <div className={`px-4 py-4 ${!isMinimized ? 'border-t border-gray-200 dark:border-gray-700' : ''}`}>
          {!isMinimized ? (
            <UserAccountDropdown currentUser={currentUser || { name: null, email: null, image: null }} isDevelopment={isDevelopment} />
          ) : (
            <div className="flex justify-center">
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <UsersIcon className="h-4 w-4 text-gray-900 dark:text-gray-100" />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}