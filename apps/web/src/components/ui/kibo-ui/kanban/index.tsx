"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent, closestCorners } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { cn } from '@/lib/utils';

// Types
export interface KanbanColumn {
  id: string;
  name: string;
  color: string;
}

export interface KanbanItem {
  id: string;
  column: string;
  [key: string]: any;
}

interface KanbanContextType {
  columns: KanbanColumn[];
  data: KanbanItem[];
  onDataChange: (data: KanbanItem[]) => void;
  activeId: string | null;
  setActiveId: (id: string | null) => void;
}

// Context
const KanbanContext = createContext<KanbanContextType | null>(null);

export const useKanban = () => {
  const context = useContext(KanbanContext);
  if (!context) {
    throw new Error('useKanban must be used within a KanbanProvider');
  }
  return context;
};

// Provider Component
interface KanbanProviderProps {
  columns: KanbanColumn[];
  data: KanbanItem[];
  onDataChange: (data: KanbanItem[]) => void;
  children: (column: KanbanColumn) => ReactNode;
}

export const KanbanProvider: React.FC<KanbanProviderProps> = ({
  columns,
  data,
  onDataChange,
  children,
}) => {
  const [activeId, setActiveId] = useState<string | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over) {
      setActiveId(null);
      return;
    }

    const activeId = active.id as string;
    const overId = over.id as string;

    // Find the active item
    const activeItem = data.find(item => item.id === activeId);
    if (!activeItem) {
      setActiveId(null);
      return;
    }

    // Determine the target column
    let targetColumn = overId;
    
    // If dropped on another item, get that item's column
    const overItem = data.find(item => item.id === overId);
    if (overItem) {
      targetColumn = overItem.column;
    }

    // If dropped on a column header, use that column
    const overColumn = columns.find(col => col.id === overId);
    if (overColumn) {
      targetColumn = overColumn.id;
    }

    // Update the item's column
    if (activeItem.column !== targetColumn) {
      const updatedData = data.map(item =>
        item.id === activeId
          ? { ...item, column: targetColumn }
          : item
      );
      onDataChange(updatedData);
    }

    setActiveId(null);
  };

  return (
    <KanbanContext.Provider value={{ columns, data, onDataChange, activeId, setActiveId }}>
      <DndContext
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-6 h-full overflow-x-auto pb-6">
          {columns.map(column => (
            <div key={column.id} className="flex-shrink-0 w-80">
              {children(column)}
            </div>
          ))}
        </div>
        <DragOverlay>
          {activeId ? (
            <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200 opacity-90">
              <div className="text-sm font-medium text-gray-900">
                {data.find(item => item.id === activeId)?.name || 'Dragging...'}
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </KanbanContext.Provider>
  );
};

// Board Component
interface KanbanBoardProps {
  id: string;
  children: ReactNode;
  className?: string;
}

export const KanbanBoard: React.FC<KanbanBoardProps> = ({ id, children, className }) => {
  const { data } = useKanban();
  const columnItems = data.filter(item => item.column === id);

  return (
    <SortableContext items={columnItems.map(item => item.id)} strategy={verticalListSortingStrategy}>
      <div 
        className={cn(
          "bg-gray-50/50 rounded-xl p-4 h-full flex flex-col",
          "border border-gray-200/50 backdrop-blur-sm",
          className
        )}
        style={{ minHeight: '600px' }}
      >
        {children}
      </div>
    </SortableContext>
  );
};

// Header Component
interface KanbanHeaderProps {
  children: ReactNode;
  className?: string;
}

export const KanbanHeader: React.FC<KanbanHeaderProps> = ({ children, className }) => {
  return (
    <div className={cn(
      "flex items-center justify-between mb-4 pb-3 border-b border-gray-200/50",
      className
    )}>
      <div className="flex items-center gap-2 font-medium text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
        {children}
      </div>
    </div>
  );
};

// Cards Container Component
interface KanbanCardsProps {
  id: string;
  children: (item: KanbanItem) => ReactNode;
  className?: string;
}

export const KanbanCards: React.FC<KanbanCardsProps> = ({ id, children, className }) => {
  const { data } = useKanban();
  const columnItems = data.filter(item => item.column === id);

  return (
    <div className={cn("flex-1 space-y-3 overflow-y-auto", className)}>
      {columnItems.map(item => children(item))}
    </div>
  );
};

// Card Component
interface KanbanCardProps {
  id: string;
  column: string;
  name: string;
  children: ReactNode;
  className?: string;
}

export const KanbanCard: React.FC<KanbanCardProps> = ({ 
  id, 
  children, 
  className 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={cn(
        "bg-white rounded-lg p-4 shadow-sm border border-gray-300",
        "hover:shadow-md transition-all duration-200 cursor-grab",
        "hover:border-blue-300 hover:bg-blue-50/20",
        isDragging && "opacity-50 shadow-lg scale-105",
        className
      )}
    >
      {children}
    </div>
  );
};
