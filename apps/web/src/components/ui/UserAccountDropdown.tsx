'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import {
  ChevronDown,
  User01 as UserIcon,
  Settings01 as SettingsIcon,
  CreditCard01 as CreditCardIcon,
  HelpCircle as HelpIcon,
  LogOut01 as LogOutIcon,
  Moon01 as MoonIcon,
  Sun01 as SunIcon,
} from '@untitled-ui/icons-react';
import { cn } from '../../lib/utils';
import { useTheme } from '../../contexts/ThemeContext';

interface UserAccountDropdownProps {
  currentUser: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
  isDevelopment?: boolean;
}

export function UserAccountDropdown({ currentUser, isDevelopment }: UserAccountDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { data: session } = useSession();
  const { theme, toggleTheme } = useTheme();

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = () => {
    if (isDevelopment && !session) {
      window.location.reload();
    } else {
      signOut();
    }
    setIsOpen(false);
  };

  const menuItems = [
    {
      icon: UserIcon,
      label: 'Account',
      onClick: () => {
        console.log('Account clicked');
        setIsOpen(false);
      }
    },
    {
      icon: CreditCardIcon,
      label: 'Billing',
      onClick: () => {
        console.log('Billing clicked');
        setIsOpen(false);
      }
    },
    {
      icon: HelpIcon,
      label: 'Help & Support',
      onClick: () => {
        console.log('Help clicked');
        setIsOpen(false);
      }
    }
  ];

  return (
    <div className="relative" ref={dropdownRef}>
      {/* User Profile Trigger */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'w-full flex items-center gap-2 p-2 rounded-lg transition-all duration-200',
          'hover:bg-gray-100 dark:hover:bg-gray-900',
          isOpen ? 'bg-gray-100 dark:bg-gray-900' : ''
        )}
        data-rounded="default"
      >
        <div className="flex items-center gap-2 flex-1">
          {currentUser.image ? (
            <img
              src={currentUser.image}
              alt={currentUser.name || 'User'}
              className="h-7 w-7 rounded-lg"
            />
          ) : (
            <div className="flex h-7 w-7 items-center justify-center rounded-lg bg-primary-100 dark:bg-primary-800">
              <span className="text-xs font-medium text-primary-700 dark:text-primary-300">
                {currentUser.name?.charAt(0) || currentUser.email?.charAt(0)}
              </span>
            </div>
          )}
          <div className="flex flex-col text-left">
            <span className="text-xs font-medium text-gray-900 dark:text-gray-100">
              {currentUser.name || currentUser.email}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">Creator</span>
          </div>
        </div>
        <ChevronDown
          className={cn(
            'h-4 w-4 text-gray-500 dark:text-gray-400 transition-transform duration-200',
            isOpen ? 'rotate-180' : ''
          )}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
          <div className="py-2">
            {menuItems.map((item, index) => (
              <button
                key={index}
                onClick={item.onClick}
                className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <item.icon className="h-4 w-4" />
                {item.label}
              </button>
            ))}

            {/* Dark Mode Toggle */}
            <button
              onClick={() => {
                toggleTheme();
                setIsOpen(false);
              }}
              className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              {theme === 'dark' ? (
                <SunIcon className="h-4 w-4" />
              ) : (
                <MoonIcon className="h-4 w-4" />
              )}
              {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
            </button>

            {/* Separator */}
            <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>
            
            {/* Sign Out */}
            <button
              onClick={handleSignOut}
              className="w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
            >
              <LogOutIcon className="h-4 w-4" />
              Sign Out
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default UserAccountDropdown;
