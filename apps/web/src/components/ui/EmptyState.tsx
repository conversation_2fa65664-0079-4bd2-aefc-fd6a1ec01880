'use client';

import { LucideIcon } from 'lucide-react';
import Button from './Button';
import Card from './Card';

interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
    intent?: 'primary' | 'secondary' | 'gray';
  };
  className?: string;
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  action,
  className = ''
}: EmptyStateProps) {
  return (
    <Card className={`p-12 text-center ${className}`}>
      <div className="mx-auto max-w-md">
        {Icon && (
          <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-xl bg-gray-100 mb-6">
            <Icon className="h-8 w-8 text-gray-400" />
          </div>
        )}
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-6">{description}</p>
        
        {action && (
          <Button
            onClick={action.onClick}
            intent={action.intent || 'primary'}
            data-rounded="large"
          >
            {action.label}
          </Button>
        )}
      </div>
    </Card>
  );
}

export default EmptyState;
