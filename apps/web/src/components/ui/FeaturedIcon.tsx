import React from 'react';
import { cn } from '../../lib/utils';

interface FeaturedIconProps {
  icon: React.ComponentType<any>;
  color?: 'gray' | 'blue' | 'green' | 'purple' | 'red' | 'yellow' | 'orange' | 'brand';
  theme?: 'modern-neue' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function FeaturedIcon({
  icon: Icon,
  color = 'gray',
  theme = 'modern-neue',
  size = 'lg',
  className
}: FeaturedIconProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };

  // Theme configurations
  const getThemeStyles = () => {
    if (theme === 'outline') {
      // Outline theme - clean border with no fill
      const outlineColors = {
        gray: { container: 'bg-white border-2 border-gray-300', icon: 'text-gray-600', shadow: '' },
        blue: { container: 'bg-white border-2 border-blue-300', icon: 'text-blue-600', shadow: '' },
        green: { container: 'bg-white border-2 border-green-300', icon: 'text-green-600', shadow: '' },
        purple: { container: 'bg-white border-2 border-purple-300', icon: 'text-purple-600', shadow: '' },
        red: { container: 'bg-white border-2 border-red-300', icon: 'text-red-600', shadow: '' },
        yellow: { container: 'bg-white border-2 border-yellow-300', icon: 'text-yellow-600', shadow: '' },
        orange: { container: 'bg-white border-2 border-orange-300', icon: 'text-orange-600', shadow: '' },
        brand: { container: 'bg-white border-2 border-blue-500', icon: 'text-blue-600', shadow: '' }
      };
      return outlineColors[color] || outlineColors.gray;
    }

    // Modern-neue theme with depth and subtle shadows
    const modernColors = {
      gray: {
        container: 'bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200/60 shadow-sm',
        icon: 'text-gray-600',
        shadow: 'shadow-gray-500/10'
      },
      blue: {
        container: 'bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200/60 shadow-sm',
        icon: 'text-blue-600',
        shadow: 'shadow-blue-500/10'
      },
      green: {
        container: 'bg-gradient-to-br from-green-50 to-green-100 border border-green-200/60 shadow-sm',
        icon: 'text-green-600',
        shadow: 'shadow-green-500/10'
      },
      purple: {
        container: 'bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200/60 shadow-sm',
        icon: 'text-purple-600',
        shadow: 'shadow-purple-500/10'
      },
      red: {
        container: 'bg-gradient-to-br from-red-50 to-red-100 border border-red-200/60 shadow-sm',
        icon: 'text-red-600',
        shadow: 'shadow-red-500/10'
      },
      yellow: {
        container: 'bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200/60 shadow-sm',
        icon: 'text-yellow-600',
        shadow: 'shadow-yellow-500/10'
      },
      orange: {
        container: 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200/60 shadow-sm',
        icon: 'text-orange-600',
        shadow: 'shadow-orange-500/10'
      },
      brand: {
        container: 'bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200/60 shadow-sm',
        icon: 'text-blue-600',
        shadow: 'shadow-blue-500/10'
      }
    };
    return modernColors[color] || modernColors.gray;
  };

  const themeStyles = getThemeStyles();

  return (
    <div
      className={cn(
        'rounded-xl flex items-center justify-center',
        sizeClasses[size],
        themeStyles.container,
        themeStyles.shadow,
        className
      )}
      data-rounded="default"
    >
      <Icon className={cn(iconSizes[size], themeStyles.icon)} />
    </div>
  );
}

export default FeaturedIcon;
