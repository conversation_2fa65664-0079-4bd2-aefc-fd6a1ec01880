'use client';

import React, { useState } from 'react';
import { Card } from './Card';
import Button from './Button';
import { FeaturedIcon } from './FeaturedIcon';
import { cn } from '../../lib/utils';
import {
  Plus as PlusIcon,
  X as XIcon
} from '@untitled-ui/icons-react';

interface InputField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number' | 'date';
  placeholder?: string;
  options?: { value: string; label: string; }[];
  required?: boolean;
  value?: string | number;
}

interface UniversalInputCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  iconColor?: 'gray' | 'blue' | 'green' | 'purple' | 'red' | 'yellow' | 'orange' | 'brand';
  fields: InputField[];
  onSubmit: (data: Record<string, any>) => void;
  onCancel?: () => void;
  submitLabel?: string;
  isOpen?: boolean;
  className?: string;
}

export function UniversalInputCard({
  title,
  description,
  icon: Icon,
  iconColor = 'brand',
  fields,
  onSubmit,
  onCancel,
  submitLabel = 'Create',
  isOpen = false,
  className
}: UniversalInputCardProps) {
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (fieldId: string, value: any) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }));
    // Clear error when user starts typing
    if (errors[fieldId]) {
      setErrors(prev => ({ ...prev, [fieldId]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    fields.forEach(field => {
      if (field.required && (!formData[field.id] || formData[field.id].toString().trim() === '')) {
        newErrors[field.id] = `${field.label} is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
      setFormData({});
      setIsExpanded(false);
    }
  };

  const handleCancel = () => {
    setFormData({});
    setErrors({});
    setIsExpanded(false);
    onCancel?.();
  };

  const renderField = (field: InputField) => {
    const hasError = !!errors[field.id];
    const baseInputClasses = cn(
      'w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:outline-none focus:ring-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white',
      hasError
        ? 'border-red-300 dark:border-red-600 focus:border-red-500 focus:ring-red-200 dark:focus:ring-red-800'
        : 'border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-blue-200 dark:focus:ring-blue-800'
    );

    switch (field.type) {
      case 'textarea':
        return (
          <textarea
            id={field.id}
            placeholder={field.placeholder}
            value={formData[field.id] || ''}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={cn(baseInputClasses, 'min-h-[80px] resize-none')}
            rows={3}
          />
        );
      
      case 'select':
        return (
          <select
            id={field.id}
            value={formData[field.id] || ''}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={baseInputClasses}
          >
            <option value="">{field.placeholder || `Select ${field.label}`}</option>
            {field.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      
      case 'number':
        return (
          <input
            type="number"
            id={field.id}
            placeholder={field.placeholder}
            value={formData[field.id] || ''}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={baseInputClasses}
          />
        );
      
      case 'date':
        return (
          <input
            type="date"
            id={field.id}
            value={formData[field.id] || ''}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={baseInputClasses}
          />
        );
      
      default:
        return (
          <input
            type="text"
            id={field.id}
            placeholder={field.placeholder}
            value={formData[field.id] || ''}
            onChange={(e) => handleInputChange(field.id, e.target.value)}
            className={baseInputClasses}
          />
        );
    }
  };

  if (!isExpanded) {
    return (
      <Card 
        className={cn(
          'p-6 cursor-pointer transition-all duration-200 border border-gray-200 hover:border-blue-400/60',
          className
        )} 
        data-rounded="default"
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex items-center gap-4">
          <FeaturedIcon
            icon={Icon}
            color={iconColor}
            theme="outline"
            size="lg"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-1">
              {title}
            </h3>
            <p className="text-sm text-gray-600 font-sans">
              {description}
            </p>
          </div>
          <PlusIcon className="w-5 h-5 text-gray-400" />
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn('p-6 border border-blue-200', className)} data-rounded="default">
      <form onSubmit={handleSubmit}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <FeaturedIcon
              icon={Icon}
              color={iconColor}
              theme="outline"
              size="lg"
            />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 font-sans">
                {title}
              </h3>
              <p className="text-sm text-gray-600 font-sans">
                {description}
              </p>
            </div>
          </div>
          <Button
            type="button"
            intent="gray"
            variant="ghost"
            size="sm"
            onClick={handleCancel}
          >
            <XIcon className="w-4 h-4" />
          </Button>
        </div>

        <div className="space-y-4">
          {fields.map(field => (
            <div key={field.id}>
              <label 
                htmlFor={field.id}
                className="block text-sm font-medium text-gray-700 mb-2 font-sans"
              >
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>
              {renderField(field)}
              {errors[field.id] && (
                <p className="text-sm text-red-600 mt-1 font-sans">
                  {errors[field.id]}
                </p>
              )}
            </div>
          ))}
        </div>

        <div className="flex items-center gap-3 mt-6 pt-4 border-t border-gray-200">
          <Button
            type="submit"
            intent="primary"
            variant="filled"
            className="flex-1"
          >
            {submitLabel}
          </Button>
          <Button
            type="button"
            intent="gray"
            variant="outlined"
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Card>
  );
}

export default UniversalInputCard;
