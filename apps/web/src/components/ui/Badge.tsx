import React from "react";
import { badge } from "@tailus/themer";
import { cn } from "../../lib/utils";

export interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: "solid" | "soft" | "outlined";
  intent?: "primary" | "secondary" | "success" | "warning" | "danger" | "gray";
  size?: "xs" | "sm" | "md" | "lg";
}

export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = "soft", intent = "gray", size = "md", children, ...props }, ref) => {
    return (
      <span
        ref={ref}
        className={cn(badge[variant]({ intent, size }), className)}
        data-rounded="default"
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";

export default Badge;