import React from "react";
import { cn } from "../../lib/utils";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  intent?: "primary" | "secondary" | "success" | "warning" | "danger" | "gray";
  variant?: "filled" | "outlined" | "ghost" | "analytics";
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  href?: string;
}

// Custom button styling with consistent radius and two main variants
const customButtonStyles = {
  base: "inline-flex items-center justify-center font-medium transition-all duration-200 ease-in-out focus:outline-none focus:ring-1 focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg active:scale-[0.98]",

  // Size variants with comfortable padding
  size: {
    xs: "px-3 py-1.5 text-xs min-h-[28px]",
    sm: "px-4 py-2 text-sm min-h-[36px]",
    md: "px-6 py-2.5 text-sm min-h-[40px]",
    lg: "px-8 py-3 text-base min-h-[44px]",
    xl: "px-10 py-3.5 text-lg min-h-[48px]"
  },

  // Intent and variant combinations - simplified to main variants
  variants: {
    primary: {
      filled: "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 focus:ring-blue-500 shadow-lg shadow-blue-500/25 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800",
      outlined: "border border-blue-500 text-blue-600 hover:bg-blue-50 focus:ring-blue-500 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-950/20",
      ghost: "text-blue-600 hover:bg-blue-50 focus:ring-blue-500 dark:text-blue-400 dark:hover:bg-blue-950/20",
      analytics: "bg-white border-0 text-gray-700 hover:bg-gray-50 focus:ring-gray-300 shadow-sm dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
    },
    secondary: {
      filled: "bg-gradient-to-r from-gray-600 to-gray-700 text-white hover:from-gray-700 hover:to-gray-800 focus:ring-gray-500 shadow-lg shadow-gray-500/25 dark:from-gray-700 dark:to-gray-800 dark:hover:from-gray-800 dark:hover:to-gray-900",
      outlined: "border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",
      ghost: "text-gray-600 hover:bg-gray-50 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-800",
      analytics: "bg-white border-0 text-gray-700 hover:bg-gray-50 focus:ring-gray-300 shadow-sm dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
    },
    success: {
      filled: "bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 focus:ring-green-500 shadow-lg shadow-green-500/25",
      outlined: "border border-green-500 text-green-600 hover:bg-green-50 focus:ring-green-500",
      ghost: "text-green-600 hover:bg-green-50 focus:ring-green-500",
      analytics: "bg-white border-0 text-gray-700 hover:bg-gray-50 focus:ring-gray-300 shadow-sm"
    },
    warning: {
      filled: "bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 focus:ring-yellow-500 shadow-lg shadow-yellow-500/25",
      outlined: "border border-yellow-500 text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500",
      ghost: "text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500",
      analytics: "bg-white border-0 text-gray-700 hover:bg-gray-50 focus:ring-gray-300 shadow-sm"
    },
    danger: {
      filled: "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500 shadow-lg shadow-red-500/25",
      outlined: "border border-red-500 text-red-600 hover:bg-red-50 focus:ring-red-500",
      ghost: "text-red-600 hover:bg-red-50 focus:ring-red-500",
      analytics: "bg-white border-0 text-gray-700 hover:bg-gray-50 focus:ring-gray-300 shadow-sm"
    },
    gray: {
      filled: "bg-gradient-to-r from-gray-500 to-gray-600 text-white hover:from-gray-600 hover:to-gray-700 focus:ring-gray-500 shadow-lg shadow-gray-500/25 dark:from-gray-600 dark:to-gray-700 dark:hover:from-gray-700 dark:hover:to-gray-800",
      outlined: "border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",
      ghost: "text-gray-600 hover:bg-gray-50 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-800",
      analytics: "bg-white border-0 text-gray-700 hover:bg-gray-50 focus:ring-gray-300 shadow-sm dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
    }
  }
};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, intent = "primary", variant = "filled", size = "md", href, children, ...props }, ref) => {
    // Get custom styling
    const baseStyles = customButtonStyles.base;
    const sizeStyles = customButtonStyles.size[size];
    const variantStyles = customButtonStyles.variants[intent][variant];

    const buttonClasses = cn(
      baseStyles,
      sizeStyles,
      variantStyles,
      className
    );

    if (href) {
      return (
        <a
          className={buttonClasses}
          href={href}
          data-rounded="default"
        >
          {children}
        </a>
      );
    }

    return (
      <button
        className={buttonClasses}
        ref={ref}
        {...props}
        data-rounded="default"
      >
        {children}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;