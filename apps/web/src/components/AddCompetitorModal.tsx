'use client';

import { useState } from 'react';
import { Card } from './ui/Card';
import Button from './ui/Button';
import {
  XClose as XIcon,
  Users01 as UserIcon,
  Loading01 as LoaderIcon
} from '@untitled-ui/icons-react';

interface AddCompetitorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (username: string, nickname?: string) => Promise<void>;
}

export function AddCompetitorModal({ isOpen, onClose, onAdd }: AddCompetitorModalProps) {
  const [username, setUsername] = useState('');
  const [nickname, setNickname] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim()) {
      setError('Username is required');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await onAdd(username.trim(), nickname.trim() || undefined);
      setUsername('');
      setNickname('');
    } catch (error) {
      setError('Failed to add competitor. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setUsername('');
      setNickname('');
      setError('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
      <Card className="relative w-full max-w-md shadow-2xl border border-gray-200" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
        <div className="bg-white rounded-lg">
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 font-sans">Add Competitor</h3>
          <Button
            intent="gray"
            variant="ghost"
            size="sm"
            onClick={handleClose}
            disabled={loading}
            className="h-8 w-8 p-0"
          >
            <XIcon className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-sans">
              TikTok Username *
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 text-sm font-sans">@</span>
              </div>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="taylorswift"
                disabled={loading}
                className="block w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 dark:disabled:bg-gray-700 disabled:opacity-50 font-sans bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                data-rounded="default"
              />
            </div>
            <p className="mt-1 text-xs text-gray-500 font-sans">
              Enter the TikTok username without the @ symbol
            </p>
          </div>

          <div>
            <label htmlFor="nickname" className="block text-sm font-medium text-gray-700 mb-2 font-sans">
              Nickname (Optional)
            </label>
            <input
              type="text"
              id="nickname"
              value={nickname}
              onChange={(e) => setNickname(e.target.value)}
              placeholder="Main competitor"
              disabled={loading}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:opacity-50 font-sans"
              data-rounded="default"
            />
            <p className="mt-1 text-xs text-gray-500 font-sans">
              A custom name to help you identify this competitor
            </p>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3" data-rounded="default">
              <p className="text-sm text-red-600 font-sans">{error}</p>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-md p-3" data-rounded="default">
            <div className="flex items-start gap-2">
              <UserIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-800 font-sans">What happens next?</h4>
                <p className="text-sm text-blue-700 mt-1 font-sans">
                  We'll fetch the latest data from TikTok and add this creator to your tracking list.
                  You'll be able to analyze their performance, get insights, and receive alerts about their activities.
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button
              intent="gray"
              variant="outlined"
              size="sm"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              intent="primary"
              variant="filled"
              size="sm"
              type="submit"
              disabled={loading || !username.trim()}
              className="flex items-center gap-2"
            >
              {loading ? (
                <>
                  <LoaderIcon className="animate-spin h-4 w-4" />
                  Adding...
                </>
              ) : (
                <>
                  <UserIcon className="h-4 w-4" />
                  Add Competitor
                </>
              )}
            </Button>
          </div>
        </form>
        </div>
      </Card>
    </div>
  );
}