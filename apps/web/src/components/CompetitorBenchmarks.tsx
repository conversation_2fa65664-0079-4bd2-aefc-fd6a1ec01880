'use client';

import { Card } from './ui/Card';
import Button from './ui/Button';
import { Badge } from './ui/Badge';
import { FeaturedIcon } from './ui/FeaturedIcon';
import { Competitor } from '@xact-data/shared';
import {
  BarChart01 as BarChartIcon,
  Target04 as TargetIcon,
  TrendUp01 as TrendUpIcon,
  TrendDown01 as TrendDownIcon,
  ArrowLeft as ArrowLeftIcon
} from '@untitled-ui/icons-react';

interface CompetitorBenchmarksProps {
  competitor: Competitor;
  onBack: () => void;
}

export function CompetitorBenchmarks({ competitor, onBack }: CompetitorBenchmarksProps) {
  // Mock benchmark data
  const performanceMetrics = [
    { metric: 'Retention Rate', you: '68%', competitor: '72%', industry: '65%', trend: 'up' },
    { metric: 'Watch Time', you: '45s', competitor: '52s', industry: '38s', trend: 'down' },
    { metric: 'Conversion Rate', you: '3.8%', competitor: '4.2%', industry: '3.1%', trend: 'up' },
    { metric: 'Sales/Hour', you: '$127', competitor: '$156', industry: '$98', trend: 'up' },
    { metric: 'Engagement Rate', you: '8.5%', competitor: '9.2%', industry: '7.1%', trend: 'up' },
    { metric: 'Click-through Rate', you: '2.1%', competitor: '2.8%', industry: '1.9%', trend: 'down' }
  ];

  const competitiveGaps = [
    { gap: 'Content Frequency', impact: 'High', description: 'Post 2x more than top performers', priority: 'urgent' },
    { gap: 'Hook Effectiveness', impact: 'Medium', description: 'Improve first 3-second retention', priority: 'high' },
    { gap: 'CTA Optimization', impact: 'Medium', description: 'Stronger call-to-action placement', priority: 'medium' },
    { gap: 'Social Proof', impact: 'Low', description: 'Add more customer testimonials', priority: 'low' },
    { gap: 'Video Quality', impact: 'Medium', description: 'Enhance production value', priority: 'medium' },
    { gap: 'Posting Schedule', impact: 'High', description: 'Optimize timing for peak engagement', priority: 'high' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={onBack}
            intent="gray"
            variant="outlined"
            size="sm"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Profile
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 font-sans">
              Benchmarks vs {competitor.username}
            </h2>
            <p className="text-gray-600 font-sans">
              Performance comparison and competitive gap analysis
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Metrics */}
        <Card className="p-6" data-rounded="default">
          <div className="flex items-center gap-3 mb-6">
            <FeaturedIcon
              icon={BarChartIcon}
              color="blue"
              theme="outline"
              size="lg"
            />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 font-sans">Performance vs {competitor.username}</h3>
              <p className="text-sm text-gray-600 font-sans">Key metrics comparison</p>
            </div>
          </div>

          <div className="space-y-4">
            {performanceMetrics.map((item, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-900 font-sans">{item.metric}</span>
                  {item.trend === 'up' ? (
                    <TrendUpIcon className="w-4 h-4 text-green-600" />
                  ) : (
                    <TrendDownIcon className="w-4 h-4 text-red-600" />
                  )}
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600 font-sans">You</p>
                    <p className="font-bold text-gray-900 font-sans">{item.you}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 font-sans">{competitor.username}</p>
                    <p className="font-bold text-red-600 font-sans">{item.competitor}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 font-sans">Industry Avg</p>
                    <p className="font-bold text-blue-600 font-sans">{item.industry}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Competitive Gaps */}
        <Card className="p-6" data-rounded="default">
          <div className="flex items-center gap-3 mb-6">
            <FeaturedIcon
              icon={TargetIcon}
              color="orange"
              theme="outline"
              size="lg"
            />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 font-sans">Competitive Gaps</h3>
              <p className="text-sm text-gray-600 font-sans">Areas for improvement vs {competitor.username}</p>
            </div>
          </div>

          <div className="space-y-4">
            {competitiveGaps.map((gap, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg border-l-4 border-orange-400">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-gray-900 font-sans">{gap.gap}</h4>
                      <Badge 
                        intent={gap.priority === 'urgent' ? 'danger' : gap.priority === 'high' ? 'warning' : gap.priority === 'medium' ? 'primary' : 'secondary'}
                        size="sm"
                      >
                        {gap.impact} Impact
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 font-sans">{gap.description}</p>
                  </div>
                  <Button intent="gray" variant="outlined" size="xs">
                    Fix Gap
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Summary Card */}
      <Card className="p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700" data-rounded="default">
        <div className="flex items-start gap-4">
          <FeaturedIcon
            icon={TargetIcon}
            color="blue"
            theme="outline"
            size="lg"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans mb-2">
              Key Insights vs {competitor.username}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-white p-3 rounded-lg">
                <p className="text-blue-600 font-medium font-sans">Areas Behind</p>
                <p className="text-xl font-bold text-blue-900 font-sans">4/6</p>
                <p className="text-blue-700 font-sans">metrics</p>
              </div>
              <div className="bg-white p-3 rounded-lg">
                <p className="text-blue-600 font-medium font-sans">Priority Gaps</p>
                <p className="text-xl font-bold text-blue-900 font-sans">3</p>
                <p className="text-blue-700 font-sans">high impact</p>
              </div>
              <div className="bg-white p-3 rounded-lg">
                <p className="text-blue-600 font-medium font-sans">Est. Improvement</p>
                <p className="text-xl font-bold text-blue-900 font-sans">+35%</p>
                <p className="text-blue-700 font-sans">performance</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
