'use client';

import {
  MagicWand01 as MagicWandIcon,
  TrendUp01 as TrendingUpIcon,
  Alert<PERSON>riangle as AlertTriangleIcon,
  CheckCircle as CheckCircleIcon
} from '@untitled-ui/icons-react';
import { Card } from '../ui/Card';

interface CoachCommentaryProps {
  type?: 'default' | 'success' | 'warning' | 'info';
  title?: string;
  message: string;
  actionable?: boolean;
  className?: string;
}

export function CoachCommentary({ 
  type = 'default', 
  title = 'Coach Commentary',
  message, 
  actionable = false,
  className = ''
}: CoachCommentaryProps) {
  const getThemeClasses = () => {
    switch (type) {
      case 'success':
        return 'bg-white dark:bg-gray-900 border-green-200 dark:border-green-800';
      case 'warning':
        return 'bg-white dark:bg-gray-900 border-yellow-200 dark:border-yellow-800';
      case 'info':
        return 'bg-white dark:bg-gray-900 border-purple-200 dark:border-purple-800';
      default:
        return 'bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="w-6 h-6 text-green-600" />;
      case 'warning':
        return <AlertTriangleIcon className="w-6 h-6 text-yellow-600" />;
      case 'info':
        return <TrendingUpIcon className="w-6 h-6 text-purple-600" />;
      default:
        return <MagicWandIcon className="w-6 h-6 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getIconBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-100 dark:bg-green-900/20';
      case 'warning':
        return 'bg-yellow-100 dark:bg-yellow-900/20';
      case 'info':
        return 'bg-purple-100 dark:bg-purple-900/20';
      default:
        return 'bg-gray-100 dark:bg-gray-800';
    }
  };

  return (
    <Card className={`p-6 ${getThemeClasses()} ${className}`} data-rounded="default">
      <div className="flex items-start gap-4">
        <div className={`w-12 h-12 ${getIconBgColor()} rounded-full flex items-center justify-center`}>
          {getIcon()}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            {title}
          </h3>
          <p className="text-gray-700 dark:text-gray-300" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            {message}
          </p>
          {actionable && (
            <div className="mt-3 flex items-center gap-2 text-sm text-blue-600 font-medium">
              <CheckCircleIcon className="w-4 h-4" />
              <span style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                Click the cards below to take action
              </span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
