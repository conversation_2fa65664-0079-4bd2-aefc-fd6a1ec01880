'use client';

import {
  MagicWand01 as MagicWandIcon,
  TrendUp01 as TrendingUpIcon,
  Alert<PERSON>riangle as <PERSON>ertTriangleIcon,
  CheckCircle as CheckCircleIcon
} from '@untitled-ui/icons-react';
import { Card } from '../ui/Card';

interface CoachCommentaryProps {
  type?: 'default' | 'success' | 'warning' | 'info';
  title?: string;
  message: string;
  actionable?: boolean;
  className?: string;
}

export function CoachCommentary({ 
  type = 'default', 
  title = 'Coach Commentary',
  message, 
  actionable = false,
  className = ''
}: CoachCommentaryProps) {
  const getThemeClasses = () => {
    switch (type) {
      case 'success':
        return 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200';
      case 'warning':
        return 'bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200';
      case 'info':
        return 'bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200';
      default:
        return 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="w-6 h-6 text-green-600" />;
      case 'warning':
        return <AlertTriangleIcon className="w-6 h-6 text-yellow-600" />;
      case 'info':
        return <TrendingUpIcon className="w-6 h-6 text-purple-600" />;
      default:
        return <MagicWandIcon className="w-6 h-6 text-blue-600" />;
    }
  };

  const getIconBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-100';
      case 'warning':
        return 'bg-yellow-100';
      case 'info':
        return 'bg-purple-100';
      default:
        return 'bg-blue-100';
    }
  };

  return (
    <Card className={`p-6 ${getThemeClasses()} ${className}`} data-rounded="default">
      <div className="flex items-start gap-4">
        <div className={`w-12 h-12 ${getIconBgColor()} rounded-full flex items-center justify-center`}>
          {getIcon()}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            {title}
          </h3>
          <p className="text-gray-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            {message}
          </p>
          {actionable && (
            <div className="mt-3 flex items-center gap-2 text-sm text-blue-600 font-medium">
              <CheckCircleIcon className="w-4 h-4" />
              <span style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                Click the cards below to take action
              </span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
