'use client';

import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface DataPoint {
  name: string;
  value: number;
  secondaryValue?: number;
}

interface AreaChartProps {
  title: string;
  description?: string;
  data: DataPoint[];
  primaryColor?: string;
  secondaryColor?: string;
  showSecondary?: boolean;
  height?: number;
  formatValue?: (value: number) => string;
  className?: string;
}

const defaultFormatValue = (value: number) => value.toLocaleString();

export function CustomAreaChart({
  title,
  description,
  data,
  primaryColor = '#007FFF',
  secondaryColor = '#80c1ff',
  showSecondary = false,
  height = 300,
  formatValue = defaultFormatValue,
  className = ''
}: AreaChartProps) {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-xl shadow-lg">
          <p className="text-sm font-medium text-gray-900">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatValue(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className={className}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>

      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="primaryGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={primaryColor} stopOpacity={0.3}/>
                <stop offset="95%" stopColor={primaryColor} stopOpacity={0.05}/>
              </linearGradient>
              {showSecondary && (
                <linearGradient id="secondaryGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={secondaryColor} stopOpacity={0.3}/>
                  <stop offset="95%" stopColor={secondaryColor} stopOpacity={0.05}/>
                </linearGradient>
              )}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              tickFormatter={formatValue}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="value"
              stroke={primaryColor}
              strokeWidth={2}
              fillOpacity={1}
              fill="url(#primaryGradient)"
              name="Primary"
            />
            {showSecondary && (
              <Area
                type="monotone"
                dataKey="secondaryValue"
                stroke={secondaryColor}
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#secondaryGradient)"
                name="Secondary"
              />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

export default CustomAreaChart;
