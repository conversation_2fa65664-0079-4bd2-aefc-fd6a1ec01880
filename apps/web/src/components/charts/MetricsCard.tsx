'use client';

import { TrendingUpIcon, TrendingDownIcon } from 'lucide-react';
import Badge from '../ui/Badge';

interface MetricsCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  description?: string;
  badge?: {
    text: string;
    intent?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'gray';
  };
  className?: string;
}

export function MetricsCard({
  title,
  value,
  change,
  changeLabel,
  description,
  badge,
  className = ''
}: MetricsCardProps) {
  const formatChange = (change: number) => {
    const isPositive = change >= 0;
    const TrendIcon = isPositive ? TrendingUpIcon : TrendingDownIcon;
    const colorClass = isPositive ? 'text-primary-600' : 'text-gray-500';
    
    return (
      <div className={`flex items-center ${colorClass} px-2 py-1 rounded-md bg-blue-100/60 dark:bg-gray-700/60`}>
        <TrendIcon className="w-3 h-3 mr-1" />
        <span className="text-xs font-normal">
          {isPositive ? '+' : ''}{change.toFixed(1)}%
        </span>
      </div>
    );
  };

  return (
    <div className={`p-6 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[120px] flex flex-col justify-between ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-300">{title}</p>
            {badge && (
              <Badge variant="soft" intent={badge.intent || 'gray'} size="sm">
                {badge.text}
              </Badge>
            )}
          </div>

          <div className="flex items-baseline space-x-2">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </p>
            {change !== undefined && formatChange(change)}
          </div>

          {(description || changeLabel) && (
            <div className="mt-2 space-y-1">
              {description && (
                <p className="text-xs text-gray-500 dark:text-gray-400">{description}</p>
              )}
              {changeLabel && (
                <p className="text-xs text-gray-500 dark:text-gray-400">{changeLabel}</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default MetricsCard;
