'use client';

import { useState } from 'react';
import { Product } from '@xact-data/shared';
import {
  ExternalLinkIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  CheckIcon
} from 'lucide-react';
import { Bell01 as BellIcon } from '@untitled-ui/icons-react';
import Button from './ui/Button';
import { CreateAlertButton } from './alerts/CreateAlertButton';

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

type SortableFields = 'trendScore' | 'soldIn24h' | 'estimatedGMV' | 'commissionRate';

interface WinningProductsTableProps {
  products: Product[];
  pagination: Pagination;
  onPageChange: (page: number) => void;
  onSort: (sortBy: SortableFields, sortOrder: 'asc' | 'desc') => void;
  currentSort: { sortBy: SortableFields; sortOrder: 'asc' | 'desc' };
}

interface TrendScoreChartProps {
  score: number;
}

function TrendScoreChart({ score }: TrendScoreChartProps) {
  const getColor = (score: number) => {
    if (score >= 80) return '#22c55e'; // green
    if (score >= 60) return '#eab308'; // yellow
    if (score >= 40) return '#f97316'; // orange
    return '#ef4444'; // red
  };

  const getTextColor = (score: number) => {
    if (score >= 80) return 'text-green-700';
    if (score >= 60) return 'text-yellow-700';
    if (score >= 40) return 'text-orange-700';
    return 'text-red-700';
  };

  // Generate simple chart data points
  const chartData = Array.from({ length: 8 }, (_, i) => ({
    x: i,
    y: Math.max(0, score + (Math.random() - 0.5) * 20)
  }));

  const maxY = Math.max(...chartData.map(d => d.y), 100);
  const color = getColor(score);

  return (
    <div className="flex items-center space-x-3">
      <div className="w-16 h-8 relative">
        <svg width="64" height="32" className="overflow-visible">
          <defs>
            <linearGradient id={`gradient-${score}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={color} stopOpacity={0.3} />
              <stop offset="100%" stopColor={color} stopOpacity={0.05} />
            </linearGradient>
          </defs>

          {/* Area fill */}
          <path
            d={`M 0 32 ${chartData.map((d, i) =>
              `L ${(i / (chartData.length - 1)) * 64} ${32 - (d.y / maxY) * 28}`
            ).join(' ')} L 64 32 Z`}
            fill={`url(#gradient-${score})`}
          />

          {/* Line */}
          <path
            d={`M ${chartData.map((d, i) =>
              `${(i / (chartData.length - 1)) * 64} ${32 - (d.y / maxY) * 28}`
            ).join(' L ')}`}
            stroke={color}
            strokeWidth="1.5"
            fill="none"
          />
        </svg>
      </div>
      <span className={`text-sm font-medium ${getTextColor(score)} font-sans`}>
        {score.toFixed(1)}
      </span>
    </div>
  );
}

interface GetLinkButtonProps {
  affiliateLink?: string;
  productId: string;
}

function GetLinkButton({ affiliateLink, productId }: GetLinkButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyLink = async () => {
    const link = affiliateLink || `https://example.com/product/${productId}`;
    try {
      await navigator.clipboard.writeText(link);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  return (
    <button
      onClick={handleCopyLink}
      className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
    >
      {copied ? (
        <>
          <CheckIcon className="w-4 h-4 mr-1" />
          Copied!
        </>
      ) : (
        <>
          <CopyIcon className="w-4 h-4 mr-1" />
          Get Link
        </>
      )}
    </button>
  );
}

export function WinningProductsTable({ 
  products, 
  pagination, 
  onPageChange, 
  onSort, 
  currentSort 
}: WinningProductsTableProps) {
  const handleSort = (column: SortableFields) => {
    const newOrder = currentSort.sortBy === column && currentSort.sortOrder === 'desc' ? 'asc' : 'desc';
    onSort(column, newOrder);
  };

  const SortIcon = ({ column }: { column: SortableFields }) => {
    if (currentSort.sortBy !== column) {
      return <ChevronUpIcon className="w-4 h-4 text-gray-400" />;
    }
    return currentSort.sortOrder === 'desc' ? 
      <ChevronDownIcon className="w-4 h-4 text-blue-600" /> : 
      <ChevronUpIcon className="w-4 h-4 text-blue-600" />;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Product
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('commissionRate')}
              >
                <div className="flex items-center space-x-1">
                  <span>Commission %</span>
                  <SortIcon column="commissionRate" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('soldIn24h')}
              >
                <div className="flex items-center space-x-1">
                  <span>24h Sold</span>
                  <SortIcon column="soldIn24h" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Creators Carrying
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('estimatedGMV')}
              >
                <div className="flex items-center space-x-1">
                  <span>Estimated GMV</span>
                  <SortIcon column="estimatedGMV" />
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('trendScore')}
              >
                <div className="flex items-center space-x-1">
                  <span>Trend Score</span>
                  <SortIcon column="trendScore" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {products.map((product) => (
              <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {product.imageUrl ? (
                      <img 
                        className="h-12 w-12 rounded-lg object-cover mr-4" 
                        src={product.imageUrl} 
                        alt={product.title}
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center mr-4">
                        <span className="text-gray-400 text-xs">No Image</span>
                      </div>
                    )}
                    <div>
                      <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                        {product.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatCurrency(product.price || 0)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {product.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {(product.commissionRate || 0).toFixed(1)}%
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {formatNumber(product.soldIn24h || 0)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {formatNumber(product.creatorsCarrying || 0)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(product.estimatedGMV || 0)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <TrendScoreChart score={product.trendScore || 0} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center gap-2">
                    <GetLinkButton
                      affiliateLink={product.affiliateLink}
                      productId={product.id || ''}
                    />
                    <CreateAlertButton
                      type="product"
                      itemName={product.title}
                      currentTrendScore={product.trendScore}
                      size="sm"
                      variant="outlined"
                    />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div className="flex-1 flex justify-between sm:hidden">
          <button
            onClick={() => pagination.hasPrev && onPageChange(pagination.page - 1)}
            disabled={!pagination.hasPrev}
            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => pagination.hasNext && onPageChange(pagination.page + 1)}
            disabled={!pagination.hasNext}
            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing{' '}
              <span className="font-medium">
                {(pagination.page - 1) * pagination.limit + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(pagination.page * pagination.limit, pagination.total)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{pagination.total}</span> results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => pagination.hasPrev && onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrev}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="h-5 w-5" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                const pageNum = Math.max(1, pagination.page - 2) + i;
                if (pageNum > pagination.totalPages) return null;
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => onPageChange(pageNum)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      pageNum === pagination.page
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button
                onClick={() => pagination.hasNext && onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="h-5 w-5" />
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}