'use client';

import { useState } from 'react';
import { Card } from './ui/Card';
import Button from './ui/Button';
import { Badge } from './ui/Badge';
import { FeaturedIcon } from './ui/FeaturedIcon';
import { Competitor } from '@xact-data/shared';
import {
  MagicWand01 as SparklesIcon,
  Target04 as TargetIcon,
  Clock as ClockIcon,
  ArrowRight as ArrowRightIcon,
  ArrowLeft as ArrowLeftIcon
} from '@untitled-ui/icons-react';

interface CompetitorPlaybooksProps {
  competitor: Competitor;
  onBack: () => void;
}

export function CompetitorPlaybooks({ competitor, onBack }: CompetitorPlaybooksProps) {
  // Mock playbooks data
  const playbooks = [
    {
      id: '1',
      title: `Outperform ${competitor.username} on Wireless Earbuds`,
      difficulty: 'Medium',
      estimatedImpact: '+25% conversion rate',
      estimatedTime: '2-3 weeks',
      steps: [
        'Create stronger opening hook (0-3 seconds)',
        'Add customer testimonials for social proof',
        'Improve pacing with quick cuts',
        'Add urgency with limited-time offer',
        'Optimize CTA placement and timing'
      ]
    },
    {
      id: '2',
      title: `Beat ${competitor.username} in Beauty Category`,
      difficulty: 'Hard',
      estimatedImpact: '+40% engagement',
      estimatedTime: '3-4 weeks',
      steps: [
        'Analyze viral content patterns',
        'Replicate successful hook formulas',
        'Improve visual storytelling',
        'Add trending audio elements',
        'Optimize posting timing'
      ]
    },
    {
      id: '3',
      title: `Improve Content Quality vs ${competitor.username}`,
      difficulty: 'Easy',
      estimatedImpact: '+15% watch time',
      estimatedTime: '1-2 weeks',
      steps: [
        'Enhance video quality and lighting',
        'Improve audio clarity',
        'Add captions and text overlays',
        'Use better background music',
        'Optimize video length'
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={onBack}
            intent="gray"
            variant="outlined"
            size="sm"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Profile
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900 font-sans">
              AI Playbooks for {competitor.username}
            </h2>
            <p className="text-gray-600 font-sans">
              Step-by-step strategies to outperform this competitor
            </p>
          </div>
        </div>
      </div>

      {/* Playbooks Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {playbooks.map((playbook) => (
          <Card key={playbook.id} className="p-6" data-rounded="default">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <FeaturedIcon
                  icon={SparklesIcon}
                  color="purple"
                  theme="outline"
                  size="lg"
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 font-sans">{playbook.title}</h3>
                  <p className="text-sm text-gray-600 font-sans">vs {competitor.username}</p>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Badge 
                  intent={playbook.difficulty === 'Easy' ? 'success' : playbook.difficulty === 'Medium' ? 'warning' : 'danger'}
                  size="sm"
                >
                  {playbook.difficulty}
                </Badge>
              </div>
            </div>

            <div className="mb-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-center gap-2 mb-2">
                <TargetIcon className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-medium text-purple-700 font-sans">Estimated Impact</span>
              </div>
              <p className="text-lg font-bold text-purple-800 font-sans">{playbook.estimatedImpact}</p>
            </div>

            <div className="space-y-3 mb-6">
              <h4 className="font-semibold text-gray-900 font-sans">Step-by-Step Action Plan:</h4>
              {playbook.steps.map((step, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-bold text-blue-700">{index + 1}</span>
                  </div>
                  <p className="text-sm text-gray-800 font-sans">{step}</p>
                </div>
              ))}
            </div>

            <div className="border-t border-dashed border-gray-300 pt-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ClockIcon className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600 font-sans">{playbook.estimatedTime}</span>
              </div>
              <div className="flex items-center gap-2">
                <Button intent="gray" variant="outlined" size="sm">
                  Save Playbook
                </Button>
                <Button intent="primary" size="sm">
                  Start Execution
                  <ArrowRightIcon className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Generate New Playbook */}
      <Card className="p-6 border-2 border-dashed border-gray-300" data-rounded="default">
        <div className="text-center">
          <FeaturedIcon
            icon={SparklesIcon}
            color="gray"
            theme="outline"
            size="xl"
            className="mx-auto mb-4"
          />
          <h3 className="text-lg font-semibold text-gray-900 font-sans mb-2">Generate Custom Playbook</h3>
          <p className="text-gray-600 font-sans mb-4">
            AI will analyze {competitor.username} and create a personalized strategy to outperform them
          </p>
          <Button intent="primary" size="lg">
            <SparklesIcon className="w-5 h-5 mr-2" />
            Generate New Playbook for {competitor.username}
          </Button>
        </div>
      </Card>
    </div>
  );
}
