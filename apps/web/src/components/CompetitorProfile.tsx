'use client';

import { useState } from 'react';
import { Card } from './ui/Card';
import Button from './ui/Button';
import { Badge } from './ui/Badge';
import { ResponsiveContainer, AreaChart, Area, Tooltip, YAxis, XAxis, CartesianGrid } from 'recharts';

import { Competitor } from '@xact-data/shared';
import {
  Users01 as UserIcon,
  TrendUp01 as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  ArrowUpRight as ExternalLinkIcon,
  BarChart01 as BarChartIcon,
  MagicWand01 as SparklesIcon,
  Target04 as TargetIcon,
  Bell01 as BellIcon,
  Plus as PlusIcon,
  Download01 as DownloadIcon
} from '@untitled-ui/icons-react';

interface CompetitorProfileProps {
  competitor: Competitor;
}

export function CompetitorProfile({ competitor }: CompetitorProfileProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [showAlertModal, setShowAlertModal] = useState(false);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const benchmarkData = [
    { metric: 'Retention Rate', you: '68%', competitor: '72%', industry: '65%', trend: 'up' },
    { metric: 'Watch Time', you: '45s', competitor: '52s', industry: '38s', trend: 'down' },
    { metric: 'Conversion Rate', you: '3.8%', competitor: '4.2%', industry: '3.1%', trend: 'up' },
    { metric: 'Sales/Hour', you: '$127', competitor: '$156', industry: '$98', trend: 'up' }
  ];

  const chartData = [
    { name: 'Jan', engagement: 6.2, views: 1.8, posts: 12 },
    { name: 'Feb', engagement: 7.1, views: 2.1, posts: 14 },
    { name: 'Mar', engagement: 6.8, views: 2.0, posts: 13 },
    { name: 'Apr', engagement: 8.4, views: 2.3, posts: 15 },
    { name: 'May', engagement: 7.9, views: 2.2, posts: 14 },
    { name: 'Jun', engagement: 8.1, views: 2.4, posts: 16 }
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 font-sans">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm text-gray-600 font-sans">
              <span style={{ color: entry.color }}>●</span> {entry.name}: {entry.value}
              {entry.dataKey === 'engagement' ? '%' : entry.dataKey === 'views' ? 'M' : ''}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card className="p-6 border border-gray-200" data-rounded="default">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            {competitor.creator.profileImageUrl ? (
              <img
                src={competitor.creator.profileImageUrl}
                alt={competitor.creator.username}
                className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                <UserIcon className="w-8 h-8 text-gray-400" />
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold text-gray-900 font-sans">
                {competitor.creator.displayName || competitor.creator.username}
              </h1>
              <p className="text-gray-600 font-sans">@{competitor.creator.username}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              intent="gray"
              variant="outlined"
              size="sm"
              onClick={() => setShowAlertModal(true)}
              className="flex items-center gap-2 border-gray-300 text-gray-600 hover:text-gray-900 hover:border-gray-400"
            >
              <BellIcon className="w-4 h-4" />
              Create Alert
            </Button>
            <Button
              intent="gray"
              variant="outlined"
              size="sm"
              className="flex items-center gap-2 border-gray-300 text-gray-600 hover:text-gray-900 hover:border-gray-400"
              onClick={() => window.open(`https://tiktok.com/@${competitor.creator.username}`, '_blank')}
            >
              <ExternalLinkIcon className="w-4 h-4" />
              View on TikTok
            </Button>
          </div>
        </div>
      </Card>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 border border-gray-200" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <UserIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">Followers</p>
              <p className="text-xl font-bold text-gray-900 font-sans">
                {formatNumber(competitor.creator.followerCount || 0)}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4 border border-gray-200" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUpIcon className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">Engagement</p>
              <p className="text-xl font-bold text-gray-900 font-sans">
                {(competitor.creator.engagementRate || 0).toFixed(1)}%
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4 border border-gray-200" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <TargetIcon className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">Total GMV</p>
              <p className="text-xl font-bold text-gray-900 font-sans">
                ${formatNumber(competitor.creator.totalGMV || 0)}
              </p>
            </div>
          </div>
        </Card>
        <Card className="p-4 border border-gray-200" data-rounded="default">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <SparklesIcon className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600 font-sans">AI Score</p>
              <p className="text-xl font-bold text-gray-900 font-sans">85/100</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-50 p-1 rounded-lg border border-gray-200">
        <button
          onClick={() => setActiveTab('overview')}
          className={`
            flex-1 px-4 py-3 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'overview'
              ? 'bg-white text-gray-900 shadow-sm border border-gray-200'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <UserIcon className="w-4 h-4" />
          Profile
        </button>
        <button
          onClick={() => setActiveTab('analysis')}
          className={`
            flex-1 px-4 py-3 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'analysis'
              ? 'bg-white text-gray-900 shadow-sm border border-gray-200'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <BarChartIcon className="w-4 h-4" />
          AI Analysis
        </button>
        <button
          onClick={() => setActiveTab('playbooks')}
          className={`
            flex-1 px-4 py-3 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'playbooks'
              ? 'bg-white text-gray-900 shadow-sm border border-gray-200'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <SparklesIcon className="w-4 h-4" />
          Playbooks
        </button>
        <button
          onClick={() => setActiveTab('benchmarks')}
          className={`
            flex-1 px-4 py-3 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
            ${activeTab === 'benchmarks'
              ? 'bg-white text-gray-900 shadow-sm border border-gray-200'
              : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
            }
          `}
          style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
        >
          <TargetIcon className="w-4 h-4" />
          Benchmarks
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          <Card className="p-6 border border-gray-200" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">Profile Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500 font-sans">Username</label>
                  <p className="text-base text-gray-900 font-sans">@{competitor.creator.username}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 font-sans">Display Name</label>
                  <p className="text-base text-gray-900 font-sans">{competitor.creator.displayName || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 font-sans">Verification Status</label>
                  <p className="text-base text-gray-900 font-sans flex items-center gap-2">
                    {competitor.creator.isVerified ? (
                      <>
                        <CheckCircleIcon className="w-4 h-4 text-blue-600" />
                        Verified
                      </>
                    ) : (
                      'Not Verified'
                    )}
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500 font-sans">Account Status</label>
                  <p className="text-base text-gray-900 font-sans">{competitor.isActive ? 'Active' : 'Inactive'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 font-sans">Added to Tracking</label>
                  <p className="text-base text-gray-900 font-sans">{new Date(competitor.createdAt).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-6 border border-gray-200" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">Recent Activity</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900 font-sans">Latest Content Posted</p>
                  <p className="text-xs text-gray-500 font-sans">2 hours ago</p>
                </div>
                <Button intent="gray" variant="outlined" size="sm">
                  <ExternalLinkIcon className="w-4 h-4 mr-2" />
                  View
                </Button>
              </div>
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-900 font-sans">Follower Milestone Reached</p>
                  <p className="text-xs text-gray-500 font-sans">1 day ago</p>
                </div>
                <Badge variant="soft" intent="success" size="sm">+10K</Badge>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* AI Analysis Tab */}
      {activeTab === 'analysis' && (
        <div className="space-y-6">
          <Card className="p-6 border border-gray-200" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">Content Performance</h3>

            {/* Metrics Grid */}
            <div className="my-6 grid max-w-2xl grid-cols-3 gap-6 divide-y border-y py-6 sm:divide-x sm:divide-y-0">
              <div>
                <span className="text-sm text-gray-500 font-sans">Engagement Rate</span>
                <div className="mt-2 flex items-center gap-3">
                  <span className="text-lg font-medium text-gray-900 font-sans">8.4%</span>
                  <Badge variant="soft" intent="success" size="sm" className="flex h-fit items-center gap-1.5">
                    <TrendingUpIcon className="w-3.5 h-3.5" />
                    12%
                  </Badge>
                </div>
              </div>

              <div className="px-6">
                <span className="text-sm text-gray-500 font-sans">Avg Views</span>
                <div className="mt-2 flex items-center gap-3">
                  <span className="text-lg font-medium text-gray-900 font-sans">2.3M</span>
                  <Badge variant="soft" intent="success" size="sm" className="flex h-fit items-center gap-1.5">
                    <TrendingUpIcon className="w-3.5 h-3.5" />
                    8%
                  </Badge>
                </div>
              </div>

              <div className="pl-6">
                <span className="text-sm text-gray-500 font-sans">Posts/Week</span>
                <div className="mt-2 flex items-center gap-3">
                  <span className="text-lg font-medium text-gray-900 font-sans">15</span>
                  <Badge variant="soft" intent="primary" size="sm" className="flex h-fit items-center gap-1.5">
                    <TrendingUpIcon className="w-3.5 h-3.5" />
                    3%
                  </Badge>
                </div>
              </div>
            </div>

            {/* Performance Chart */}
            <div className="mt-6 h-56 w-full sm:h-72">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={chartData}>
                  <YAxis
                    className="text-xs text-gray-500"
                    width={40}
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <XAxis
                    className="text-xs text-gray-500"
                    dataKey="name"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <Tooltip
                    cursor={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                    content={<CustomTooltip />}
                  />
                  <CartesianGrid
                    horizontal={false}
                    stroke="#e5e7eb"
                    strokeDasharray="3 3"
                  />

                  <Area
                    fill="#6b7280"
                    stroke="#6b7280"
                    fillOpacity={0.1}
                    dataKey="posts"
                    activeDot={{
                      fill: '#6b7280',
                      r: 3,
                      stroke: 'white'
                    }}
                    stackId="1"
                  />
                  <Area
                    fill="#3b82f6"
                    stroke="#3b82f6"
                    fillOpacity={0.1}
                    dataKey="views"
                    activeDot={{
                      fill: '#3b82f6',
                      r: 3,
                      stroke: 'white'
                    }}
                    stackId="1"
                  />
                  <Area
                    fill="#06b6d4"
                    stroke="#06b6d4"
                    fillOpacity={0.1}
                    dataKey="engagement"
                    activeDot={{
                      fill: '#06b6d4',
                      r: 3,
                      stroke: 'white'
                    }}
                    stackId="1"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </Card>

          <Card className="p-6 border border-gray-200" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">AI Insights</h3>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <SparklesIcon className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900 font-sans">Content Strategy</h4>
                    <p className="text-sm text-blue-700 font-sans mt-1">
                      This creator excels at product demonstrations with high conversion rates.
                      Their content typically features lifestyle integration and authentic testimonials.
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <TrendingUpIcon className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-green-900 font-sans">Growth Pattern</h4>
                    <p className="text-sm text-green-700 font-sans mt-1">
                      Consistent growth with viral spikes every 2-3 weeks.
                      Best performing content is posted between 6-8 PM EST.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Playbooks Tab */}
      {activeTab === 'playbooks' && (
        <div className="space-y-6">
          <Card className="p-6 border border-gray-200" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">Content Playbooks</h3>
            <div className="space-y-6">

              {/* Product Demo Playbook */}
              <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg border border-green-200">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 font-sans mb-2">Product Demo Format</h4>
                    <Badge variant="soft" intent="success" size="sm">High Converting • 87% Success Rate</Badge>
                  </div>
                  <Button intent="gray" variant="outlined" size="sm" className="border-green-300 text-green-700 hover:bg-green-100">
                    <DownloadIcon className="w-4 h-4 mr-2" />
                    Use Template
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
                    <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-800 font-sans">1. Hook</div>
                      <div className="text-xs text-green-600 font-sans mt-1">Grab attention</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-800 font-sans">2. Problem</div>
                      <div className="text-xs text-green-600 font-sans mt-1">Identify pain point</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-800 font-sans">3. Solution</div>
                      <div className="text-xs text-green-600 font-sans mt-1">Present product</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-800 font-sans">4. Demo</div>
                      <div className="text-xs text-green-600 font-sans mt-1">Show in action</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-800 font-sans">5. Results</div>
                      <div className="text-xs text-green-600 font-sans mt-1">Prove value</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg border border-green-200">
                      <div className="text-sm font-medium text-green-800 font-sans">6. CTA</div>
                      <div className="text-xs text-green-600 font-sans mt-1">Drive action</div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-green-200">
                    <h5 className="font-medium text-gray-900 font-sans mb-2">Key Success Factors:</h5>
                    <ul className="text-sm text-gray-700 font-sans space-y-1">
                      <li>• Keep hook under 3 seconds</li>
                      <li>• Show real transformation/before-after</li>
                      <li>• Include authentic user testimonials</li>
                      <li>• Clear, compelling call-to-action</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Lifestyle Integration Playbook */}
              <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 font-sans mb-2">Lifestyle Integration</h4>
                    <Badge variant="soft" intent="primary" size="sm">Trending • 73% Success Rate</Badge>
                  </div>
                  <Button intent="gray" variant="outlined" size="sm" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                    <DownloadIcon className="w-4 h-4 mr-2" />
                    Use Template
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                      <div className="text-sm font-medium text-blue-800 font-sans">Daily Routine</div>
                      <div className="text-xs text-blue-600 font-sans mt-1">Show natural context</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                      <div className="text-sm font-medium text-blue-800 font-sans">Product Use</div>
                      <div className="text-xs text-blue-600 font-sans mt-1">Seamless integration</div>
                    </div>
                    <div className="text-center p-4 bg-white rounded-lg border border-blue-200">
                      <div className="text-sm font-medium text-blue-800 font-sans">Benefits</div>
                      <div className="text-xs text-blue-600 font-sans mt-1">Highlight improvements</div>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-blue-200">
                    <h5 className="font-medium text-gray-900 font-sans mb-2">Best Practices:</h5>
                    <ul className="text-sm text-gray-700 font-sans space-y-1">
                      <li>• Film in natural lighting and settings</li>
                      <li>• Show genuine daily moments</li>
                      <li>• Focus on lifestyle enhancement</li>
                      <li>• Maintain authentic, relatable tone</li>
                    </ul>
                  </div>
                </div>
              </div>

            </div>
          </Card>
        </div>
      )}

      {/* Benchmarks Tab */}
      {activeTab === 'benchmarks' && (
        <div className="space-y-6">
          <Card className="p-6 border border-gray-200" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">Performance Benchmarks</h3>

            {/* Metrics Grid - Same style as affiliate dashboard */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {benchmarkData.map((item, index) => (
                <div key={index} className="p-4 bg-white border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-600 font-sans">{item.metric}</h4>
                    <div className="flex items-center">
                      {item.trend === 'up' ? (
                        <TrendingUpIcon className="w-4 h-4 text-green-500" />
                      ) : (
                        <TrendingUpIcon className="w-4 h-4 text-red-500 rotate-180" />
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-xs text-gray-500 font-sans mb-1">You</div>
                      <div className="text-lg font-bold text-gray-900 font-sans">{item.you}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 font-sans mb-1">Competitor</div>
                      <div className="text-lg font-bold text-blue-600 font-sans">{item.competitor}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 font-sans mb-1">Industry</div>
                      <div className="text-lg font-bold text-gray-900 font-sans">{item.industry}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {/* Simple Alert Modal */}
      {showAlertModal && (
        <div className="fixed inset-0 bg-black/50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <Card className="relative w-full max-w-md bg-white shadow-2xl border border-gray-200">
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 font-sans">Create Alert</h3>
              <Button
                intent="gray"
                variant="ghost"
                size="sm"
                onClick={() => setShowAlertModal(false)}
                className="h-8 w-8 p-0"
              >
                <PlusIcon className="h-4 w-4 rotate-45" />
              </Button>
            </div>

            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-sans">
                  Alert Type
                </label>
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  <button className="flex-1 px-3 py-2 rounded-md text-sm font-medium bg-white text-gray-900 shadow-sm font-sans">
                    Follower Growth
                  </button>
                  <button className="flex-1 px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-white/50 font-sans">
                    New Content
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-sans">
                  Threshold
                </label>
                <input
                  type="number"
                  placeholder="e.g., 1000"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 font-sans"
                />
                <p className="mt-1 text-xs text-gray-500 font-sans">
                  Get notified when follower count increases by this amount
                </p>
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <Button
                  intent="gray"
                  variant="outlined"
                  size="sm"
                  onClick={() => setShowAlertModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  intent="primary"
                  variant="filled"
                  size="sm"
                  onClick={() => {
                    setShowAlertModal(false);
                    // Add alert creation logic here
                  }}
                  className="flex items-center gap-2"
                >
                  <BellIcon className="h-4 w-4" />
                  Create Alert
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}