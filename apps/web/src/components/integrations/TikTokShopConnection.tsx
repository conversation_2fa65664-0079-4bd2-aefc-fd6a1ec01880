'use client';

import { useState, useEffect } from 'react';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import { CheckCircleIcon, XCircleIcon, RefreshCwIcon, ExternalLinkIcon } from 'lucide-react';

interface TikTokShopConnectionProps {
  userId: string;
}

interface ConnectionStatus {
  isConnected: boolean;
  isTokenExpired: boolean;
  shopName?: string;
  connectedAt?: string;
  needsReconnection: boolean;
}

export function TikTokShopConnection({ userId }: TikTokShopConnectionProps) {
  const [status, setStatus] = useState<ConnectionStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [connecting, setConnecting] = useState(false);

  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api-production-7bd1.up.railway.app';

  useEffect(() => {
    checkConnectionStatus();
    
    // Check if we need to refresh due to redirect from callback
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('refresh') === 'true') {
      console.log('Refresh requested from callback, checking status...');
      
      // Remove the refresh parameter from URL
      const newUrl = window.location.pathname + '?tab=integrations';
      window.history.replaceState({}, '', newUrl);
      
      // Refresh status multiple times to ensure we catch the update
      const refreshAttempts = [500, 1500, 3000, 5000];
      refreshAttempts.forEach((delay, index) => {
        setTimeout(() => {
          console.log(`Status check attempt ${index + 1}`);
          checkConnectionStatus();
        }, delay);
      });
    }
  }, [userId]);

  const checkConnectionStatus = async () => {
    try {
      setLoading(true);
      console.log('Checking TikTok Shop connection status for user:', userId);
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/status/${userId}?t=${Date.now()}`);
      const data = await response.json();
      
      console.log('Status check response:', data);
      
      if (data.success) {
        // Map backend response to frontend interface
        const mappedStatus: ConnectionStatus = {
          isConnected: data.data.isConnected,
          isTokenExpired: data.data.tokenExpired,
          shopName: data.data.shopInfo?.shopName,
          connectedAt: data.data.shopInfo?.connectedAt,
          needsReconnection: data.data.tokenExpired || false,
        };
        console.log('Mapped status:', mappedStatus);
        setStatus(mappedStatus);
      } else {
        console.error('Status check failed:', data.message);
      }
    } catch (error) {
      console.error('Failed to check TikTok Shop connection status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = async () => {
    setConnecting(true);
    
    try {
      const redirectUri = `${window.location.origin}/auth/tiktok-shop/callback`;
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/auth-url?userId=${userId}&redirectUri=${encodeURIComponent(redirectUri)}`);

      const data = await response.json();
      
      if (data.success) {
        // Redirect to TikTok Shop authorization
        window.location.href = data.data.authUrl;
      } else {
        alert(`Failed to generate authorization URL: ${data.message}`);
      }
    } catch (error) {
      console.error('Failed to initiate TikTok Shop connection:', error);
      alert('Failed to connect to TikTok Shop. Please try again.');
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnect = async () => {
    if (!confirm('Are you sure you want to disconnect your TikTok Shop account? This will stop syncing your performance data.')) {
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/disconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setStatus({
          isConnected: false,
          isTokenExpired: false,
          needsReconnection: false,
        });
        alert('TikTok Shop account disconnected successfully.');
      } else {
        alert(`Failed to disconnect: ${data.message}`);
      }
    } catch (error) {
      console.error('Failed to disconnect TikTok Shop:', error);
      alert('Failed to disconnect TikTok Shop. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshToken = async () => {
    setLoading(true);
    
    try {
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        await checkConnectionStatus();
        alert('Access token refreshed successfully.');
      } else {
        alert(`Failed to refresh token: ${data.message}`);
      }
    } catch (error) {
      console.error('Failed to refresh TikTok Shop token:', error);
      alert('Failed to refresh token. Please reconnect your account.');
    } finally {
      setLoading(false);
    }
  };

  const handleManageAuthorization = async () => {
    setConnecting(true);
    
    try {
      // Get the authorization URL for managing permissions
      const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/auth-url?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(window.location.origin + '/auth/tiktok-shop/callback')}`, {
        method: 'GET',
      });

      const data = await response.json();
      
      if (data.success && data.data.authUrl) {
        // Open the authorization URL in a new window for managing permissions
        const authWindow = window.open(data.data.authUrl, 'tiktok-auth', 'width=500,height=600,scrollbars=yes,resizable=yes');
        
        // Poll for window closure
        const checkClosed = setInterval(() => {
          if (authWindow?.closed) {
            clearInterval(checkClosed);
            setConnecting(false);
            // Refresh status after potential changes
            setTimeout(() => checkConnectionStatus(), 1000);
          }
        }, 1000);
      } else {
        alert(`Failed to get authorization URL: ${data.message}`);
        setConnecting(false);
      }
    } catch (error) {
      console.error('Failed to get authorization URL:', error);
      alert('Failed to open authorization page. Please try again.');
      setConnecting(false);
    }
  };

  if (!status) {
    return (
      <div className="p-4 border border-gray-200 rounded-lg animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
            <div>
              <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-48"></div>
            </div>
          </div>
          <div className="h-8 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12.53.02C13.84 0 15.14.01 16.44 0c.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
            </svg>
          </div>
          <div>
            <p className="font-medium text-gray-900">TikTok Shop Creator API</p>
            <p className="text-sm text-gray-500">
              {status.isConnected 
                ? `Connected as ${status.shopName || 'TikTok Shop creator'}`
                : 'Connect your TikTok Shop creator account to sync affiliate performance data'
              }
            </p>
            {status.isConnected && status.connectedAt && (
              <p className="text-xs text-gray-400 mt-1">
                Connected on {new Date(status.connectedAt).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          {status.isConnected ? (
            <>
              {status.needsReconnection ? (
                <Badge variant="soft" intent="warning" className="flex items-center gap-1">
                  <XCircleIcon className="w-3 h-3" />
                  Token Expired
                </Badge>
              ) : (
                <Badge variant="soft" intent="success" className="flex items-center gap-1">
                  <CheckCircleIcon className="w-3 h-3" />
                  Connected
                </Badge>
              )}
              
              <div className="flex gap-2">
                <Button 
                  intent="primary" 
                  variant="outlined"
                  size="sm" 
                  onClick={checkConnectionStatus}
                  disabled={loading}
                  data-rounded="default"
                  title="Refresh connection status"
                >
                  <RefreshCwIcon className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
                
                {status.needsReconnection ? (
                  <Button 
                    intent="primary" 
                    size="sm" 
                    onClick={handleRefreshToken}
                    disabled={loading}
                    data-rounded="default"
                  >
                    {loading ? (
                      <RefreshCwIcon className="w-4 h-4 animate-spin" />
                    ) : (
                      'Refresh Token'
                    )}
                  </Button>
                ) : (
                  <Button 
                    intent="primary" 
                    variant="outlined"
                    size="sm" 
                    onClick={handleRefreshToken}
                    disabled={loading}
                    data-rounded="default"
                  >
                    {loading ? (
                      <RefreshCwIcon className="w-4 h-4 animate-spin" />
                    ) : (
                      'Refresh Token'
                    )}
                  </Button>
                )}
                
                <Button 
                  intent="primary" 
                  variant="outlined"
                  size="sm" 
                  onClick={handleManageAuthorization}
                  disabled={loading}
                  data-rounded="default"
                >
                  <ExternalLinkIcon className="w-4 h-4 mr-2" />
                  Manage Authorization
                </Button>
                
                <Button 
                  intent="gray" 
                  variant="outlined"
                  size="sm" 
                  onClick={handleDisconnect}
                  disabled={loading}
                  data-rounded="default"
                >
                  Disconnect
                </Button>
              </div>
            </>
          ) : (
            <>
              <Badge variant="soft" intent="gray">Not Connected</Badge>
              <Button 
                intent="primary" 
                size="sm" 
                onClick={handleConnect}
                disabled={connecting}
                data-rounded="default"
              >
                {connecting ? (
                  <>
                    <RefreshCwIcon className="w-4 h-4 animate-spin mr-2" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLinkIcon className="w-4 h-4 mr-2" />
                    Connect Account
                  </>
                )}
              </Button>
            </>
          )}
        </div>
      </div>

      {status.isConnected && !status.needsReconnection && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-start gap-2">
            <CheckCircleIcon className="w-5 h-5 text-green-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-green-900">
                Successfully Connected!
              </p>
              <p className="text-sm text-green-800">
                Your TikTok Shop creator performance data will now be automatically synced to your affiliate dashboard.
              </p>
              <p className="text-xs text-green-700 mt-2">
                Use "Manage Authorization" to change permissions or remove access to specific scopes.
              </p>
            </div>
          </div>
        </div>
      )}

      {status.needsReconnection && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start gap-2">
            <XCircleIcon className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-900">
                Connection Needs Attention
              </p>
              <p className="text-sm text-yellow-800">
                Your access token has expired. Please refresh your token or reconnect your account to continue syncing data.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}