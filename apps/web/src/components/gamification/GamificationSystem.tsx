'use client';

import { useState, useEffect } from 'react';
import { useToastHelpers } from '../ui/Toast';
import { Card } from '../ui/Card';
import Badge from '../ui/Badge';
import Button from '../ui/Button';
import {
  Trophy01 as TrophyIcon,
  Star01 as StarIcon,
  TrendUp01 as TrendingUpIcon,
  Target04 as TargetIcon,
  Zap as ZapIcon,
  Award01 as CrownIcon,
  Award01 as AwardIcon,
  CheckCircle as CheckCircleIcon,
  CurrencyDollar as DollarSignIcon,
  Gift01 as GiftIcon,
  Target01 as Target01Icon
} from '@untitled-ui/icons-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'revenue' | 'growth' | 'engagement' | 'milestone';
  threshold: number;
  currentValue: number;
  unit: string;
  unlocked: boolean;
  unlockedAt?: Date;
  reward?: string;
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  progress: number;
  category: 'monthly_gmv' | 'followers' | 'conversion';
  reward: string;
  nextMilestone?: string;
}

export function GamificationSystem() {
  const toast = useToastHelpers();
  const [achievements, setAchievements] = useState<Achievement[]>([
    {
      id: 'first_sale',
      title: 'First Sale',
      description: 'Made your first affiliate sale',
      icon: <DollarSignIcon className="w-6 h-6" />,
      category: 'revenue',
      threshold: 1,
      currentValue: 340,
      unit: 'sales',
      unlocked: true,
      unlockedAt: new Date('2024-01-15'),
      reward: 'Unlock advanced analytics'
    },
    {
      id: 'revenue_milestone_1k',
      title: 'Revenue Rookie',
      description: 'Reached $1,000 in monthly GMV',
      icon: <TrendingUpIcon className="w-6 h-6" />,
      category: 'revenue',
      threshold: 1000,
      currentValue: 85000,
      unit: 'GMV',
      unlocked: true,
      unlockedAt: new Date('2024-02-01'),
      reward: 'Coach insights unlocked'
    },
    {
      id: 'revenue_milestone_10k',
      title: 'Revenue Rising Star',
      description: 'Reached $10,000 in monthly GMV',
      icon: <StarIcon className="w-6 h-6" />,
      category: 'revenue',
      threshold: 10000,
      currentValue: 85000,
      unit: 'GMV',
      unlocked: true,
      unlockedAt: new Date('2024-03-15'),
      reward: 'Priority support access'
    },
    {
      id: 'revenue_milestone_50k',
      title: 'Revenue Rockstar',
      description: 'Reached $50,000 in monthly GMV',
      icon: <CrownIcon className="w-6 h-6" />,
      category: 'revenue',
      threshold: 50000,
      currentValue: 85000,
      unit: 'GMV',
      unlocked: true,
      unlockedAt: new Date('2024-08-20'),
      reward: 'Exclusive mastermind access'
    },
    {
      id: 'revenue_milestone_100k',
      title: 'Revenue Royalty',
      description: 'Reach $100,000 in monthly GMV',
      icon: <TrophyIcon className="w-6 h-6" />,
      category: 'revenue',
      threshold: 100000,
      currentValue: 85000,
      unit: 'GMV',
      unlocked: false,
      reward: 'Personal success manager'
    },
    {
      id: 'engagement_master',
      title: 'Engagement Master',
      description: 'Achieve 5%+ conversion rate',
      icon: <TargetIcon className="w-6 h-6" />,
      category: 'engagement',
      threshold: 5,
      currentValue: 3.8,
      unit: '%',
      unlocked: false,
      reward: 'Advanced targeting tools'
    },
    {
      id: 'growth_accelerator',
      title: 'Growth Accelerator',
      description: 'Achieve 5%+ monthly follower growth',
      icon: <ZapIcon className="w-6 h-6" />,
      category: 'growth',
      threshold: 5,
      currentValue: 2.3,
      unit: '%',
      unlocked: false,
      reward: 'Growth strategy consultation'
    }
  ]);

  const [milestones, setMilestones] = useState<Milestone[]>([
    {
      id: 'gmv_100k',
      title: 'Six-Figure Creator',
      description: 'Reach $100,000 monthly GMV',
      targetValue: 100000,
      currentValue: 85000,
      unit: 'GMV',
      progress: 85,
      category: 'monthly_gmv',
      reward: 'Personal success manager + $500 bonus',
      nextMilestone: 'Seven-Figure Creator ($1M GMV)'
    },
    {
      id: 'conversion_5',
      title: 'Conversion Champion',
      description: 'Achieve 5% conversion rate',
      targetValue: 5,
      currentValue: 3.8,
      unit: '%',
      progress: 76,
      category: 'conversion',
      reward: 'Advanced targeting tools',
      nextMilestone: 'Conversion Master (7% rate)'
    },
    {
      id: 'followers_100k',
      title: 'Influence Icon',
      description: 'Reach 100,000 followers',
      targetValue: 100000,
      currentValue: 67500,
      unit: 'followers',
      progress: 67.5,
      category: 'followers',
      reward: 'Verified creator badge + brand partnerships',
      nextMilestone: 'Mega Influencer (500K followers)'
    }
  ]);

  // Check for new achievements
  useEffect(() => {
    const checkAchievements = () => {
      achievements.forEach(achievement => {
        if (!achievement.unlocked && achievement.currentValue >= achievement.threshold) {
          // Unlock achievement
          setAchievements(prev => 
            prev.map(a => 
              a.id === achievement.id 
                ? { ...a, unlocked: true, unlockedAt: new Date() }
                : a
            )
          );
          
          // Show celebration toast
          toast.success(
            `Achievement Unlocked: ${achievement.title}!`,
            achievement.reward ? `Reward: ${achievement.reward}` : undefined
          );
        }
      });
    };

    checkAchievements();
  }, [achievements, toast]);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'revenue': return 'bg-green-100 text-green-700 border-green-200';
      case 'growth': return 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700';
      case 'engagement': return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'milestone': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const unlockedAchievements = achievements.filter(a => a.unlocked);
  const lockedAchievements = achievements.filter(a => !a.unlocked);

  return (
    <div className="space-y-6">
      {/* Current Milestones */}
      <Card className="p-6" data-rounded="default">
        <div className="flex items-center gap-2 mb-4">
          <Target01Icon className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Active Milestones
          </h3>
        </div>
        <div className="space-y-4">
          {milestones.map(milestone => (
            <div key={milestone.id} className="border rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {milestone.title}
                  </h4>
                  <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {milestone.description}
                  </p>
                </div>
                <Badge variant="soft" className="bg-blue-100 text-blue-700">
                  {milestone.progress.toFixed(1)}%
                </Badge>
              </div>
              
              <div className="mb-3">
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {milestone.currentValue.toLocaleString()} / {milestone.targetValue.toLocaleString()} {milestone.unit}
                  </span>
                  <span className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {(milestone.targetValue - milestone.currentValue).toLocaleString()} to go
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(milestone.progress, 100)}%` }}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  <GiftIcon className="w-4 h-4 text-blue-600" />
                  Reward: {milestone.reward}
                </div>
                {milestone.nextMilestone && (
                  <div className="text-xs text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    Next: {milestone.nextMilestone}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Unlocked Achievements */}
      <Card className="p-6" data-rounded="default">
        <div className="flex items-center gap-2 mb-4">
          <TrophyIcon className="w-5 h-5 text-yellow-600" />
          <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Unlocked Achievements ({unlockedAchievements.length})
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {unlockedAchievements.map(achievement => (
            <div key={achievement.id} className="border rounded-lg p-4 bg-gradient-to-br from-yellow-50 to-orange-50">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600">
                  {achievement.icon}
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {achievement.title}
                  </h4>
                  <Badge variant="soft" className={`text-xs ${getCategoryColor(achievement.category)}`}>
                    {achievement.category}
                  </Badge>
                </div>
              </div>
              <p className="text-xs text-gray-600 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                {achievement.description}
              </p>
              {achievement.reward && (
                <div className="flex items-center gap-1 text-xs text-green-600 font-medium" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  <GiftIcon className="w-3 h-3" />
                  {achievement.reward}
                </div>
              )}
              {achievement.unlockedAt && (
                <p className="text-xs text-gray-500 mt-1" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Unlocked {achievement.unlockedAt.toLocaleDateString()}
                </p>
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Locked Achievements */}
      {lockedAchievements.length > 0 && (
        <Card className="p-6" data-rounded="default">
          <div className="flex items-center gap-2 mb-4">
            <StarIcon className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Upcoming Achievements
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {lockedAchievements.map(achievement => (
              <div key={achievement.id} className="border rounded-lg p-4 opacity-75">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-400">
                    {achievement.icon}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 text-sm" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {achievement.title}
                    </h4>
                    <Badge variant="soft" className={`text-xs ${getCategoryColor(achievement.category)}`}>
                      {achievement.category}
                    </Badge>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {achievement.description}
                </p>
                <div className="text-xs text-gray-500 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Progress: {achievement.currentValue.toLocaleString()} / {achievement.threshold.toLocaleString()} {achievement.unit}
                </div>
                {achievement.reward && (
                  <div className="flex items-center gap-1 text-xs text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    <GiftIcon className="w-3 h-3" />
                    {achievement.reward}
                  </div>
                )}
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}
