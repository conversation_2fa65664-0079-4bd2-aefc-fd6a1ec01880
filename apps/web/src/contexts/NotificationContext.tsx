'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';

export interface Notification {
  id: string;
  type: 'product_alert' | 'competitor_alert' | 'system' | 'success' | 'warning';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  metadata?: {
    productName?: string;
    competitorName?: string;
    trendScore?: number;
    threshold?: number;
    changePercent?: number;
    avatar?: string;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isOpen: boolean;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  togglePanel: () => void;
  closePanel: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'product_alert',
      title: 'Trend Score Hit Target',
      message: 'Wireless Earbuds Pro hit 89% trend score (target: 85%)',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRead: false,
      metadata: {
        productName: 'Wireless Earbuds Pro',
        trendScore: 89,
        threshold: 85
      }
    },
    {
      id: '2',
      type: 'competitor_alert',
      title: 'Competitor Went Live',
      message: '@techreviewer started livestream with 2.3K viewers',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      isRead: false,
      metadata: {
        competitorName: '@techreviewer',
        avatar: '/api/placeholder/40/40'
      }
    },
    {
      id: '3',
      type: 'product_alert',
      title: 'GMV Growth Alert',
      message: 'Smart Watch Series 8 GMV grew 25% in 24h ($12.4K → $15.5K)',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      isRead: true,
      metadata: {
        productName: 'Smart Watch Series 8',
        changePercent: 25
      }
    },
    {
      id: '4',
      type: 'product_alert',
      title: 'Score Above Threshold',
      message: 'Gaming Headset reached 92% trend score (target: 80%)',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
      isRead: false,
      metadata: {
        productName: 'Gaming Headset RGB',
        trendScore: 92,
        threshold: 80
      }
    },
    {
      id: '5',
      type: 'competitor_alert',
      title: 'New Product Launch',
      message: '@gadgetguru launched "Ultimate Phone Case" with 500+ orders',
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      isRead: true,
      metadata: {
        competitorName: '@gadgetguru',
        avatar: '/api/placeholder/40/40'
      }
    },
    {
      id: '6',
      type: 'product_alert',
      title: 'Viral Content Alert',
      message: 'Fitness Tracker featured in viral TikTok (2.1M views)',
      timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000), // 18 hours ago
      isRead: true,
      metadata: {
        productName: 'Fitness Tracker Pro',
        trendScore: 87,
        threshold: 75
      }
    }
  ]);
  
  const [isOpen, setIsOpen] = useState(false);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      isRead: false
    };
    
    setNotifications(prev => [newNotification, ...prev]);
  }, []);

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const togglePanel = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const closePanel = useCallback(() => {
    setIsOpen(false);
  }, []);

  return (
    <NotificationContext.Provider value={{
      notifications,
      unreadCount,
      isOpen,
      addNotification,
      markAsRead,
      markAllAsRead,
      removeNotification,
      togglePanel,
      closePanel
    }}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}
