/**
 * API utility functions for making requests to the backend API
 */

// Get the API base URL from environment variables or default to production
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api-production-7bd1.up.railway.app';

/**
 * Create a full API URL from a relative path
 */
export function createApiUrl(path: string): string {
  // Remove leading slash if present to avoid double slashes
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${API_BASE_URL}/${cleanPath}`;
}

/**
 * Make a GET request to the API
 */
export async function apiGet(path: string): Promise<Response> {
  return fetch(createApiUrl(path));
}

/**
 * Make a POST request to the API
 */
export async function apiPost(path: string, data?: any): Promise<Response> {
  return fetch(createApiUrl(path), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Make a PUT request to the API
 */
export async function apiPut(path: string, data?: any): Promise<Response> {
  return fetch(createApiUrl(path), {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: data ? JSON.stringify(data) : undefined,
  });
}

/**
 * Make a DELETE request to the API
 */
export async function apiDelete(path: string): Promise<Response> {
  return fetch(createApiUrl(path), {
    method: 'DELETE',
  });
}