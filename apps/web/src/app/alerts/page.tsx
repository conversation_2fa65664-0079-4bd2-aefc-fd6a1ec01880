'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import {
  Bell01 as BellIcon,
  Plus as PlusIcon,
  SearchSm as SearchIcon,
  FilterLines as FilterIcon,
  TrendUp01 as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  XClose as XCircleIcon,
  Target04 as TargetIcon,
  Users01 as UsersIcon,
  Edit03 as EditIcon,
  Package as PackageIcon,
  Calendar as CalendarIcon
} from '@untitled-ui/icons-react';

export default function AlertsPage() {
  const [activeView, setActiveView] = useState('feed'); // 'feed' or 'history'

  // Active Alert Rules
  const activeAlerts = [
    {
      id: 1,
      type: 'product',
      target: 'Wireless Earbuds Pro',
      condition: 'Trend Score > 80',
      isActive: true
    },
    {
      id: 2,
      type: 'competitor',
      target: '@CreatorX',
      condition: 'New Product Launch',
      isActive: true
    },
    {
      id: 3,
      type: 'product',
      target: 'Smart Fitness Watch',
      condition: 'GMV Growth > 20%',
      isActive: false
    },
    {
      id: 4,
      type: 'competitor',
      target: '@TechReviewer',
      condition: 'Viral Post > 1M views',
      isActive: true
    }
  ];

  // Feed Notifications
  const feedNotifications = [
    {
      id: 1,
      type: 'product',
      title: 'Wireless Earbuds Pro spiked to 87 Trend Score',
      subtitle: '+24% GMV in last 24h',
      timestamp: '12m ago',
      timeGroup: 'Today',
      actionText: 'View Product'
    },
    {
      id: 2,
      type: 'competitor',
      title: '@CreatorX launched a new product: "Glow Serum"',
      subtitle: 'Estimated GMV: $12,300 in 6h',
      timestamp: '45m ago',
      timeGroup: 'Today',
      actionText: 'View Profile'
    },
    {
      id: 3,
      type: 'competitor',
      title: '@CreatorY viral video hit 2.4M views in 6h',
      subtitle: 'Weak CTA flagged in AI Insights',
      timestamp: '1d ago',
      timeGroup: 'Yesterday',
      actionText: 'View Profile'
    },
    {
      id: 4,
      type: 'product',
      title: 'Smart Fitness Watch crossed 500 units sold in 24h',
      subtitle: 'Trend Score: 92',
      timestamp: '1d ago',
      timeGroup: 'Yesterday',
      actionText: 'View Product'
    }
  ];

  // History Data
  const historyData = [
    {
      id: 1,
      datetime: '2025-09-25 14:20',
      type: 'Product',
      target: 'Wireless Earbuds Pro',
      trigger: 'Trend Score > 80',
      action: 'Viewed'
    },
    {
      id: 2,
      datetime: '2025-09-25 12:40',
      type: 'Competitor',
      target: '@CreatorX',
      trigger: 'New Product Launch',
      action: '-'
    },
    {
      id: 3,
      datetime: '2025-09-24 16:10',
      type: 'Competitor',
      target: '@CreatorY',
      trigger: 'Viral Post (2.4M)',
      action: 'Viewed'
    }
  ];

  const toggleAlert = (id: number) => {
    setActiveAlerts(prev => prev.map(alert =>
      alert.id === id ? { ...alert, isActive: !alert.isActive } : alert
    ));
  };

  const editAlert = (id: number) => {
    // Edit alert settings
    console.log('Edit alert:', id);
  };

  const groupedNotifications = feedNotifications.reduce((groups, notification) => {
    const group = notification.timeGroup;
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(notification);
    return groups;
  }, {} as Record<string, typeof feedNotifications>);

  const getNotificationIcon = (type: string) => {
    return type === 'product' ? (
      <PackageIcon className="w-4 h-4 text-blue-600" />
    ) : (
      <UsersIcon className="w-4 h-4 text-purple-600" />
    );
  };

  return (
    <AppLayout>
      <div className="space-y-4">
        {/* Page Header */}
        <div className="flex items-center justify-between border-b border-gray-200 pb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 font-sans">Live Alerts</h1>
            <p className="text-sm text-gray-600 font-sans">
              Monitor and manage all your product and competitor alerts
            </p>
          </div>
        </div>

        {/* Active Alerts Section */}
        <Card className="border border-gray-200">
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <h2 className="text-sm font-semibold text-gray-900 font-sans">Active Alerts</h2>
          </div>
          <div className="divide-y divide-gray-100">
            {activeAlerts.map((alert) => (
              <div key={alert.id} className="p-4 flex items-center justify-between hover:bg-gray-50">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900 font-sans">
                      {alert.target}
                    </span>
                    <span className="text-sm text-gray-500 font-sans">→</span>
                    <span className="text-sm text-gray-600 font-sans">
                      {alert.condition}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={alert.isActive}
                      onChange={() => toggleAlert(alert.id)}
                      className="sr-only peer"
                    />
                    <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                  <Button
                    intent="gray"
                    variant="ghost"
                    size="sm"
                    onClick={() => editAlert(alert.id)}
                    className="h-6 w-6 p-0 bg-gray-50/50 hover:bg-gray-100/50"
                  >
                    <EditIcon className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
          <button
            onClick={() => setActiveView('feed')}
            className={`
              flex-1 px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
              ${activeView === 'feed'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
              }
            `}
            style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
          >
            Feed
          </button>
          <button
            onClick={() => setActiveView('history')}
            className={`
              flex-1 px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
              ${activeView === 'history'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
              }
            `}
            style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
          >
            History
          </button>
        </div>

        {/* Feed View */}
        {activeView === 'feed' && (
          <div className="space-y-4">
            {Object.entries(groupedNotifications).map(([timeGroup, notifications]) => (
              <div key={timeGroup}>
                {/* Time Group Header */}
                <div className="flex items-center gap-3 mb-3">
                  <h3 className="text-xs font-semibold text-gray-700 font-sans uppercase tracking-wide">{timeGroup}</h3>
                  <div className="flex-1 border-t border-dashed border-gray-300"></div>
                </div>

                {/* Notifications */}
                <div className="space-y-3">
                  {notifications.map((notification) => (
                    <Card key={notification.id} className="p-3 border border-gray-200 hover:border-gray-300 transition-colors bg-white">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          <div className="p-1.5 bg-gray-50 rounded-md border border-gray-200">
                            {getNotificationIcon(notification.type)}
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge
                                  variant="soft"
                                  intent={notification.type === 'product' ? 'primary' : 'secondary'}
                                  size="xs"
                                  className="text-xs px-1.5 py-0"
                                >
                                  {notification.type === 'product' ? 'Product Alert' : 'Competitor Alert'}
                                </Badge>
                              </div>
                              <h4 className="text-sm font-medium text-gray-900 font-sans leading-tight mb-1">
                                {notification.title}
                              </h4>
                              <p className="text-xs text-gray-600 font-sans">
                                {notification.subtitle}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 flex-shrink-0">
                              <Button
                                intent="gray"
                                variant="outlined"
                                size="sm"
                                className="h-6 px-2 text-xs bg-gray-50/50 hover:bg-gray-100/50"
                              >
                                {notification.actionText}
                              </Button>
                              <span className="text-xs text-gray-500 font-sans whitespace-nowrap">
                                {notification.timestamp}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* History View */}
        {activeView === 'history' && (
          <Card className="border border-gray-200">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-sans">
                      Date/Time
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-sans">
                      Type
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-sans">
                      Target
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-sans">
                      Trigger
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-sans">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-100">
                  {historyData.map((item, index) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3 text-xs text-gray-900 font-sans">
                        {item.datetime}
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="soft"
                          intent={item.type === 'Product' ? 'primary' : 'secondary'}
                          size="xs"
                          className="text-xs px-1.5 py-0"
                        >
                          {item.type}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-xs font-medium text-gray-900 font-sans">
                        {item.target}
                      </td>
                      <td className="px-4 py-3 text-xs text-gray-600 font-sans">
                        {item.trigger}
                      </td>
                      <td className="px-4 py-3">
                        {item.action === 'Viewed' ? (
                          <Badge variant="soft" intent="success" size="xs" className="text-xs px-1.5 py-0">
                            Viewed
                          </Badge>
                        ) : (
                          <span className="text-xs text-gray-400 font-sans">-</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        )}

      </div>
    </AppLayout>
  );
}