'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { FeaturedIcon } from '../../components/ui/FeaturedIcon';
import {
  TrendUp01 as TrendUpIcon,
  TrendDown01 as TrendDownIcon,
  Users01 as UsersIcon,
  CurrencyDollar as DollarSignIcon,
  Target04 as TargetIcon,
  ArrowRight as ArrowRightIcon,
  PlayCircle as PlayIcon,
  AlertTriangle as AlertTriangleIcon,
  CheckCircle as CheckCircleIcon,
  MagicWand01 as SparklesIcon
} from '@untitled-ui/icons-react';

export default function BenchmarksPage() {
  const [activeCategory, setActiveCategory] = useState('all');

  const benchmarks = [
    {
      id: 1,
      category: 'revenue',
      metric: 'Monthly GMV',
      yourValue: '$85,000',
      nicheAverage: '$62,000',
      topPerformer: '$145,000',
      status: 'above',
      gap: '+37%',
      recommendation: 'You\'re outperforming the niche average. Focus on scaling to reach top performer level.'
    },
    {
      id: 2,
      category: 'engagement',
      metric: 'Conversion Rate',
      yourValue: '3.8%',
      nicheAverage: '4.2%',
      topPerformer: '6.1%',
      status: 'below',
      gap: '-9.5%',
      recommendation: 'Optimize your CTAs and product presentation to close the conversion gap.'
    },
    {
      id: 3,
      category: 'content',
      metric: 'Posting Frequency',
      yourValue: '12/week',
      nicheAverage: '15/week',
      topPerformer: '22/week',
      status: 'below',
      gap: '-20%',
      recommendation: 'Increase content output to match niche standards and improve visibility.'
    },
    {
      id: 4,
      category: 'audience',
      metric: 'Follower Growth',
      yourValue: '+2.3%',
      nicheAverage: '+1.8%',
      topPerformer: '+4.1%',
      status: 'above',
      gap: '+28%',
      recommendation: 'Strong growth rate. Maintain current strategy while testing new content formats.'
    }
  ];

  const competitorPlaybooks = [
    {
      id: 1,
      competitor: '@TopCreator123',
      advantage: 'Hook Strategy',
      impact: '$2.1K monthly gap',
      description: 'Uses emotional triggers in first 3 seconds, 40% higher engagement',
      actions: ['Analyze their top 10 hooks', 'Test emotional opening lines', 'A/B test hook formats']
    },
    {
      id: 2,
      competitor: '@BeautyInfluencer',
      advantage: 'Product Mix',
      impact: '$1.8K monthly gap',
      description: 'Diversified across 3 categories, higher average order value',
      actions: ['Research their product selection', 'Test beauty category', 'Bundle complementary products']
    }
  ];

  const getStatusColor = (status: string) => {
    return status === 'above' ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (status: string) => {
    return status === 'above' ? 
      <TrendUpIcon className="w-4 h-4 text-green-600" /> : 
      <TrendDownIcon className="w-4 h-4 text-red-600" />;
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'revenue': return <FeaturedIcon icon={DollarSignIcon} color="green" theme="modern-neue" size="md" />;
      case 'engagement': return <FeaturedIcon icon={TargetIcon} color="blue" theme="modern-neue" size="md" />;
      case 'content': return <FeaturedIcon icon={PlayIcon} color="purple" theme="modern-neue" size="md" />;
      case 'audience': return <FeaturedIcon icon={UsersIcon} color="orange" theme="modern-neue" size="md" />;
      default: return <FeaturedIcon icon={TargetIcon} color="gray" theme="modern-neue" size="md" />;
    }
  };

  const filteredBenchmarks = activeCategory === 'all' 
    ? benchmarks 
    : benchmarks.filter(b => b.category === activeCategory);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Competitive Benchmarks
            </h1>
            <p className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              See how you stack up against your niche and top performers
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" data-rounded="default">
              <SparklesIcon className="w-4 h-4 mr-2" />
              Generate New Benchmarks
            </Button>
          </div>
        </div>

        {/* Coach Commentary */}
        <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200" data-rounded="default">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <TrendUpIcon className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                Performance Overview
              </h3>
              <p className="text-gray-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                You're performing above average in 2 out of 4 key metrics. Focus on conversion rate and content frequency to close the gaps.
              </p>
            </div>
          </div>
        </Card>

        {/* Category Filters */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'all', label: 'All Metrics', count: benchmarks.length },
            { id: 'revenue', label: 'Revenue', count: 1 },
            { id: 'engagement', label: 'Engagement', count: 1 },
            { id: 'content', label: 'Content', count: 1 },
            { id: 'audience', label: 'Audience', count: 1 }
          ].map(category => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`
                flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                ${activeCategory === category.id 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              {category.label}
              <span className="ml-2 px-2 py-0.5 bg-gray-200 text-gray-600 rounded-full text-xs">
                {category.count}
              </span>
            </button>
          ))}
        </div>

        {/* Benchmarks Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredBenchmarks.map(benchmark => (
            <Card key={benchmark.id} className="p-6 transition-all duration-200 border border-gray-200 hover:border-blue-400/60" data-rounded="default">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getCategoryIcon(benchmark.category)}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {benchmark.metric}
                    </h3>
                    <Badge variant="soft" className="text-xs bg-gray-100 text-gray-700">
                      {benchmark.category}
                    </Badge>
                  </div>
                </div>
                <div className="flex items-center gap-2 bg-gray-50/50 px-3 py-2 rounded-lg">
                  {getStatusIcon(benchmark.status)}
                  <span className={`text-sm font-medium ${getStatusColor(benchmark.status)}`}>
                    {benchmark.gap}
                  </span>
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex justify-between items-center p-3 bg-blue-50/30 rounded-lg">
                  <span className="text-sm font-medium text-gray-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Your Performance</span>
                  <span className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {benchmark.yourValue}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50/50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Niche Average</span>
                  <span className="text-sm font-medium text-gray-800" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {benchmark.nicheAverage}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-green-50/30 rounded-lg">
                  <span className="text-sm font-medium text-gray-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Top Performer</span>
                  <span className="text-sm font-semibold text-green-700" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {benchmark.topPerformer}
                  </span>
                </div>
              </div>

              <div className="border-t pt-4">
                <p className="text-sm text-gray-600 mb-3" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {benchmark.recommendation}
                </p>
                <Button intent="gray" variant="outlined" size="sm" className="w-full justify-center">
                  View Action Plan
                  <ArrowRightIcon className="w-4 h-4 ml-1" />
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Competitor Playbooks */}
        <Card className="p-6" data-rounded="default">
          <h3 className="text-lg font-semibold text-gray-900 mb-4" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Beat the Competition Playbooks
          </h3>
          <div className="space-y-4">
            {competitorPlaybooks.map(playbook => (
              <div key={playbook.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {playbook.competitor} - {playbook.advantage}
                    </h4>
                    <p className="text-sm text-red-600 font-medium">{playbook.impact}</p>
                  </div>
                  <Button intent="primary" variant="outlined" size="sm">
                    <PlayIcon className="w-4 h-4 mr-1" />
                    Execute
                  </Button>
                </div>
                <p className="text-sm text-gray-600 mb-3" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {playbook.description}
                </p>
                <div className="space-y-2">
                  {playbook.actions.map((action, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                      <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>{action}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </AppLayout>
  );
}
