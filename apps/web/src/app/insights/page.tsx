'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';

import {
  MagicWand01 as SparklesIcon,
  Calendar as CalendarIcon,
  LayoutGrid01 as GridIcon,
  BarChart03 as BarChartIcon,
  Target04 as TargetIcon,
  CheckCircle as CheckIcon,
  Clock as ClockIcon,
  TrendUp01 as TrendUpIcon
} from '@untitled-ui/icons-react';

export default function InsightsPage() {
  const [activeView, setActiveView] = useState('cards');

  // Action items data
  const actionItems = [
    {
      id: '1',
      title: 'Upload 2 TikToks for Product X',
      description: 'Competitors avg. 5 videos/week, you\'re at 3.',
      priority: 'High Revenue Impact',
      priorityColor: 'bg-red-50 text-red-600 border-red-200',
      status: 'todo',
      dueDate: 'Today',
      actions: ['Schedule Post', 'Mark Done']
    },
    {
      id: '2',
      title: 'Update product description for +5% CTR',
      description: 'Weak CTA compared to competitor benchmarks.',
      priority: 'Optimization',
      priorityColor: 'bg-orange-50 text-orange-600 border-orange-200',
      status: 'todo',
      dueDate: 'This Week',
      actions: ['Edit Now', 'Mark Done']
    },
    {
      id: '3',
      title: 'Run livestream for Product X',
      description: 'Peak engagement window: 2-4 PM EST.',
      priority: 'High Impact',
      priorityColor: 'bg-green-50 text-green-600 border-green-200',
      status: 'in-progress',
      dueDate: 'Thursday',
      actions: ['Go Live', 'Mark Done']
    },
    {
      id: '4',
      title: 'Test new CTA variations',
      description: 'A/B test different call-to-action styles.',
      priority: 'Optimization',
      priorityColor: 'bg-blue-50 text-blue-600 border-blue-200',
      status: 'backlog',
      dueDate: 'Next Week',
      actions: ['Start Test', 'Mark Done']
    }
  ];

  // AI Insights data
  const aiInsights = [
    "Your posting frequency is 20% below top competitors.",
    "AOV is trending +8% — double down on Product X.",
    "Retention is weak: competitors avg. 35s watch time.",
    "Peak conversion hours: 2-4 PM EST (schedule more content).",
    "Beauty category shows 40% higher engagement rates."
  ];

  // Progress calculation
  const currentGMV = 3200;
  const goalGMV = 5000;
  const progressPercentage = Math.round((currentGMV / goalGMV) * 100);

  const markAsDone = (id: string) => {
    // Update the action item status to done
    const updatedItems = actionItems.map(item =>
      item.id === id ? { ...item, status: 'done' } : item
    );
    // In a real app, you'd update state here
    console.log('Marking task as done:', id);
  };

  return (
    <AppLayout>
      <div className="space-y-6" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 font-sans">
              AI Growth Coach
            </h1>
            <p className="text-gray-600 dark:text-gray-400 font-sans">
              Your personalized daily/weekly action plan
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <Card className="p-6" data-rounded="default">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 font-sans">Progress to Goal</h3>
              <p className="text-sm text-gray-600 font-sans">${currentGMV.toLocaleString()} → ${goalGMV.toLocaleString()} GMV</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600 font-sans">{progressPercentage}%</div>
              <div className="text-sm text-gray-500 font-sans">${(goalGMV - currentGMV).toLocaleString()} to go</div>
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </Card>

        {/* View Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'cards', label: 'Cards', icon: GridIcon },
            { id: 'kanban', label: 'Kanban', icon: BarChartIcon },
            { id: 'calendar', label: 'Calendar', icon: CalendarIcon },
            { id: 'insights', label: 'Insights', icon: SparklesIcon }
          ].map(view => (
            <button
              key={view.id}
              onClick={() => setActiveView(view.id)}
              className={`
                flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center gap-2
                ${activeView === view.id
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
            >
              <view.icon className="w-4 h-4" />
              {view.label}
            </button>
          ))}
        </div>

        {/* Content Views */}
        {activeView === 'cards' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {actionItems.filter(item => item.status !== 'done').map(item => (
              <Card key={item.id} className="p-6 border border-gray-200 hover:border-blue-200 transition-colors" data-rounded="default">
                <div className="flex items-start justify-between mb-4">
                  <Badge variant="soft" className={`text-xs font-medium ${item.priorityColor}`}>
                    {item.priority}
                  </Badge>
                  <span className="text-xs text-gray-500 font-sans">{item.dueDate}</span>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 font-sans mb-2">
                  {item.title}
                </h3>

                <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-4">
                  <p className="text-sm text-blue-800 font-sans">
                    <span className="font-medium">Why:</span> {item.description}
                  </p>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    intent="gray"
                    variant="outlined"
                    size="sm"
                    onClick={() => markAsDone(item.id)}
                    className="border-gray-300 text-gray-600 hover:text-gray-900 hover:border-gray-400"
                  >
                    Mark Done
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeView === 'kanban' && (
          <div className="grid grid-cols-4 gap-6">
              {/* Today Column */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">Today</h3>
                <div className="space-y-3">
                  {actionItems.filter(item => item.dueDate === 'Today').map(item => (
                    <Card key={item.id} className="p-4 border border-gray-200" data-rounded="default">
                      <h4 className="text-sm font-medium text-gray-900 font-sans mb-2">{item.title}</h4>
                      <Badge variant="soft" className={`text-xs ${item.priorityColor}`}>
                        {item.priority}
                      </Badge>
                    </Card>
                  ))}
                </div>
              </div>

              {/* This Week Column */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">This Week</h3>
                <div className="space-y-3">
                  {actionItems.filter(item => item.dueDate === 'This Week' || item.dueDate === 'Thursday').map(item => (
                    <Card key={item.id} className="p-4 border border-gray-200" data-rounded="default">
                      <h4 className="text-sm font-medium text-gray-900 font-sans mb-2">{item.title}</h4>
                      <Badge variant="soft" className={`text-xs ${item.priorityColor}`}>
                        {item.priority}
                      </Badge>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Backlog Column */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">Backlog</h3>
                <div className="space-y-3">
                  {actionItems.filter(item => item.dueDate === 'Next Week').map(item => (
                    <Card key={item.id} className="p-4 border border-gray-200" data-rounded="default">
                      <h4 className="text-sm font-medium text-gray-900 font-sans mb-2">{item.title}</h4>
                      <Badge variant="soft" className={`text-xs ${item.priorityColor}`}>
                        {item.priority}
                      </Badge>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Done Column */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 font-sans mb-4">Done</h3>
                <div className="space-y-3">
                  <Card className="p-4 border border-gray-200 opacity-60" data-rounded="default">
                    <h4 className="text-sm font-medium text-gray-500 font-sans mb-2 line-through">Review competitor analysis</h4>
                    <Badge variant="soft" className="text-xs bg-gray-50 text-gray-500">
                      Completed
                    </Badge>
                  </Card>
                </div>
              </div>
          </div>
        )}

        {activeView === 'calendar' && (
          <Card className="p-6" data-rounded="default">
            <h3 className="text-lg font-semibold text-gray-900 font-sans mb-6">Weekly Schedule</h3>
            <div className="grid grid-cols-7 gap-4">
              {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                <div key={day} className="text-center">
                  <h4 className="text-sm font-medium text-gray-900 font-sans mb-3">{day}</h4>
                  <div className="space-y-2">
                    {index === 1 && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
                        <p className="text-xs text-blue-800 font-sans">Upload video</p>
                      </div>
                    )}
                    {index === 3 && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-2">
                        <p className="text-xs text-green-800 font-sans">Run stream</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {activeView === 'insights' && (
          <Card className="p-6" data-rounded="default">
            <div className="flex items-center gap-3 mb-6">
              <SparklesIcon className="w-6 h-6 text-gray-600 dark:text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans">Insights from AI Coach</h3>
            </div>
            <div className="space-y-4">
              {aiInsights.map((insight, index) => (
                <div key={index} className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700 dark:text-gray-300 font-sans">{insight}</p>
                </div>
              ))}
            </div>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}