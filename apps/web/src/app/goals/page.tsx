'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import Calendar from '../../components/ui/Calendar';
import { GamificationSystem } from '../../components/gamification/GamificationSystem';
import { CoachCommentary } from '../../components/coach/CoachCommentary';
import { FeaturedIcon } from '../../components/ui/FeaturedIcon';
import { Tooltip } from '../../components/ui/Tooltip';
import {
  KanbanProvider,
  KanbanBoard,
  KanbanHeader,
  KanbanCards,
  KanbanCard,
} from '../../components/ui/kibo-ui/kanban';
import {
  Target04 as TargetIcon,
  TrendUp01 as TrendUpIcon,
  Calendar as CalendarIcon,
  CheckCircle as CheckCircleIcon,
  Plus as PlusIcon,
  Edit01 as EditIcon,
  Clock as ClockIcon,
  CurrencyDollar as DollarSignIcon,
  MagicWand01 as SparklesIcon,
  Grid01 as KanbanIcon,
  BarChart03 as AnalyticsIcon,
  FilterLines as FilterIcon
} from '@untitled-ui/icons-react';

interface Goal {
  id: string;
  title: string;
  description: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  category: 'revenue' | 'growth' | 'engagement' | 'content';
  deadline: string;
  progress: number;
  status: 'active' | 'completed' | 'overdue';
  priority: 'high' | 'medium' | 'low';
}

interface Task {
  id: string;
  name: string;
  description?: string;
  goalId?: string;
  column: string;
  priority: 'high' | 'medium' | 'low';
  dueDate?: Date;
  assignee?: {
    id: string;
    name: string;
    avatar?: string;
  };
  tags?: string[];
  type: 'goal' | 'task' | 'milestone';
}

interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  type: 'goal' | 'task' | 'deadline';
  goalId?: string;
  taskId?: string;
}

type ViewMode = 'kanban' | 'calendar' | 'analytics';

export default function GoalsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('kanban');
  const [activeTab, setActiveTab] = useState('active');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);

  // Kanban columns
  const kanbanColumns = [
    { id: 'planned', name: 'Planned', color: '#6B7280' },
    { id: 'in_progress', name: 'In Progress', color: '#F59E0B' },
    { id: 'review', name: 'Review', color: '#8B5CF6' },
    { id: 'completed', name: 'Completed', color: '#10B981' },
  ];

  // Sample tasks data
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      name: 'Launch Q4 Product Campaign',
      description: 'Create and execute comprehensive marketing campaign for new product line',
      goalId: '1',
      column: 'in_progress',
      priority: 'high',
      dueDate: new Date(2024, 11, 15),
      assignee: { id: '1', name: 'Sarah Chen', avatar: '/avatars/sarah.jpg' },
      tags: ['marketing', 'product'],
      type: 'task'
    },
    {
      id: '2',
      name: 'Optimize Conversion Funnel',
      description: 'Analyze and improve checkout process to increase conversion rates',
      goalId: '2',
      column: 'planned',
      priority: 'high',
      dueDate: new Date(2024, 10, 30),
      assignee: { id: '2', name: 'Mike Johnson', avatar: '/avatars/mike.jpg' },
      tags: ['analytics', 'optimization'],
      type: 'task'
    },
    {
      id: '3',
      name: 'Content Strategy Review',
      description: 'Quarterly review of content performance and strategy adjustments',
      goalId: '3',
      column: 'review',
      priority: 'medium',
      dueDate: new Date(2024, 10, 25),
      assignee: { id: '3', name: 'Emma Davis', avatar: '/avatars/emma.jpg' },
      tags: ['content', 'strategy'],
      type: 'milestone'
    },
    {
      id: '4',
      name: 'Revenue Target Achievement',
      description: 'Reach $100K monthly GMV milestone',
      goalId: '1',
      column: 'completed',
      priority: 'high',
      dueDate: new Date(2024, 9, 31),
      assignee: { id: '1', name: 'Sarah Chen', avatar: '/avatars/sarah.jpg' },
      tags: ['revenue', 'milestone'],
      type: 'goal'
    }
  ]);

  // Calendar events
  const calendarEvents: CalendarEvent[] = [
    {
      id: '1',
      title: 'Q4 Campaign Launch',
      date: new Date(2024, 11, 15),
      type: 'deadline',
      taskId: '1'
    },
    {
      id: '2',
      title: 'Conversion Review',
      date: new Date(2024, 10, 30),
      type: 'task',
      taskId: '2'
    },
    {
      id: '3',
      title: 'Content Strategy Meeting',
      date: new Date(2024, 10, 25),
      type: 'goal',
      taskId: '3'
    }
  ];

  const goals: Goal[] = [
    {
      id: '1',
      title: 'Reach $100K Monthly GMV',
      description: 'Scale revenue to six-figure monthly gross merchandise value',
      targetValue: 100000,
      currentValue: 85000,
      unit: 'GMV',
      category: 'revenue',
      deadline: '2024-12-31',
      progress: 85,
      status: 'active',
      priority: 'high'
    },
    {
      id: '2',
      title: 'Achieve 5% Conversion Rate',
      description: 'Optimize funnel to reach 5% click-to-purchase conversion',
      targetValue: 5,
      currentValue: 3.8,
      unit: '%',
      category: 'engagement',
      deadline: '2024-11-30',
      progress: 76,
      status: 'active',
      priority: 'high'
    },
    {
      id: '3',
      title: 'Grow to 100K Followers',
      description: 'Build audience to 100,000 engaged followers',
      targetValue: 100000,
      currentValue: 67500,
      unit: 'followers',
      category: 'growth',
      deadline: '2025-03-31',
      progress: 67.5,
      status: 'active',
      priority: 'medium'
    },
    {
      id: '4',
      title: 'Post 20 Videos Weekly',
      description: 'Increase content output to 20 videos per week',
      targetValue: 20,
      currentValue: 12,
      unit: 'videos/week',
      category: 'content',
      deadline: '2024-10-31',
      progress: 60,
      status: 'active',
      priority: 'medium'
    }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'revenue': return <FeaturedIcon icon={DollarSignIcon} color="green" theme="modern-neue" size="md" />;
      case 'growth': return <FeaturedIcon icon={TrendUpIcon} color="blue" theme="modern-neue" size="md" />;
      case 'engagement': return <FeaturedIcon icon={TargetIcon} color="purple" theme="modern-neue" size="md" />;
      case 'content': return <FeaturedIcon icon={EditIcon} color="orange" theme="modern-neue" size="md" />;
      default: return <FeaturedIcon icon={TargetIcon} color="gray" theme="modern-neue" size="md" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'medium': return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'low': return 'bg-gray-100 text-gray-700 border-gray-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-700';
      case 'overdue': return 'bg-red-100 text-red-700';
      default: return 'bg-blue-100 text-blue-700';
    }
  };

  const activeGoals = goals.filter(g => g.status === 'active');
  const completedGoals = goals.filter(g => g.status === 'completed');

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Goals & Task Management
            </h1>
            <p className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Your central hub for goals, tasks, and progress tracking
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" data-rounded="default" onClick={() => setShowCreateModal(true)}>
              <PlusIcon className="w-4 h-4 mr-2" />
              Create Goal
            </Button>
          </div>
        </div>

        {/* Coach Commentary */}
        <CoachCommentary
          type="info"
          title="Goal Progress Update"
          message="You're making excellent progress! You're 85% toward your revenue goal and 76% toward your conversion target. Focus on the high-priority goals to maximize impact this quarter."
          actionable={true}
        />

        {/* Goal Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <FeaturedIcon
                icon={TargetIcon}
                color="blue"
                size="md"
              />
              <div>
                <p className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {activeGoals.length}
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Active Goals
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <FeaturedIcon
                icon={CheckCircleIcon}
                color="green"
                size="md"
              />
              <div>
                <p className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {completedGoals.length}
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Completed
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <FeaturedIcon
                icon={TrendUpIcon}
                color="purple"
                size="md"
              />
              <div>
                <p className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {Math.round(activeGoals.reduce((acc, goal) => acc + goal.progress, 0) / activeGoals.length)}%
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Avg Progress
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <FeaturedIcon
                icon={ClockIcon}
                color="orange"
                size="md"
              />
              <div>
                <p className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {activeGoals.filter(g => g.priority === 'high').length}
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  High Priority
                </p>
              </div>
            </div>
          </Card>
        </div>





        {/* View Mode Toggle */}
        <div className="flex items-center justify-start mb-4">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setViewMode('kanban')}
              className={`
                px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                ${viewMode === 'kanban'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              <KanbanIcon className="w-4 h-4 mr-2 inline" />
              Kanban
            </button>
            <button
              onClick={() => setViewMode('calendar')}
              className={`
                px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                ${viewMode === 'calendar'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              <CalendarIcon className="w-4 h-4 mr-2 inline" />
              Calendar
            </button>
          </div>
        </div>

        {/* Main Content - View Mode Switching */}
        {viewMode === 'kanban' && (
          <div className="h-[600px]">
            <KanbanProvider
              columns={kanbanColumns}
              data={tasks as any}
              onDataChange={(data) => setTasks(data as Task[])}
            >
              {(column) => (
                <KanbanBoard id={column.id} key={column.id}>
                  <KanbanHeader>
                    <div className="flex items-center gap-2">
                      <div
                        className="h-2 w-2 rounded-full"
                        style={{ backgroundColor: column.color }}
                      />
                      <span>{column.name}</span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                        {tasks.filter(task => task.column === column.id).length}
                      </span>
                    </div>
                  </KanbanHeader>
                  <KanbanCards id={column.id}>
                    {(task: any) => (
                      <KanbanCard
                        column={column.id}
                        id={task.id}
                        key={task.id}
                        name={task.name}
                      >
                        <div className="space-y-3">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm text-gray-900 mb-1" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                                {task.name}
                              </h4>
                              {task.description && (
                                <p className="text-xs text-gray-600 line-clamp-2">
                                  {task.description}
                                </p>
                              )}
                            </div>
                            {task.assignee && (
                              <div className="flex-shrink-0">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                  <span className="text-xs font-medium text-blue-600">
                                    {task.assignee.name.split(' ').map((n: string) => n[0]).join('')}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge
                                variant="soft"
                                className={`text-xs ${
                                  task.priority === 'high' ? 'bg-red-50 text-red-600 border-red-100' :
                                  task.priority === 'medium' ? 'bg-yellow-50 text-yellow-600 border-yellow-100' :
                                  'bg-gray-50 text-gray-600 border-gray-100'
                                }`}
                              >
                                {task.priority}
                              </Badge>
                              <Badge
                                variant="soft"
                                className={`text-xs ${
                                  task.type === 'goal' ? 'bg-blue-50 text-blue-600 border-blue-100' :
                                  task.type === 'milestone' ? 'bg-purple-50 text-purple-600 border-purple-100' :
                                  'bg-green-50 text-green-600 border-green-100'
                                }`}
                              >
                                {task.type}
                              </Badge>
                            </div>
                            {task.dueDate && (
                              <span className="text-xs text-gray-500">
                                {task.dueDate.toLocaleDateString()}
                              </span>
                            )}
                          </div>

                          {task.tags && task.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {task.tags.slice(0, 2).map(tag => (
                                <span key={tag} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                  {tag}
                                </span>
                              ))}
                              {task.tags.length > 2 && (
                                <span className="text-xs text-gray-500">+{task.tags.length - 2}</span>
                              )}
                            </div>
                          )}
                        </div>
                      </KanbanCard>
                    )}
                  </KanbanCards>
                </KanbanBoard>
              )}
            </KanbanProvider>
          </div>
        )}

        {viewMode === 'calendar' && (
          <Calendar
            events={calendarEvents}
            onDateSelect={(date) => console.log('Date selected:', date)}
            onEventClick={(event) => console.log('Event clicked:', event)}
            className="max-w-4xl mx-auto"
          />
        )}

        {viewMode === 'analytics' && activeTab === 'active' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {activeGoals.map(goal => (
              <Card key={goal.id} className="p-6 transition-all duration-200 border border-gray-200 hover:border-gray-300 bg-gradient-to-br from-white to-gray-50/50" data-rounded="default">
                {/* Header Section */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start gap-4">
                    {getCategoryIcon(goal.category)}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                        {goal.title}
                      </h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="soft" className={`text-xs font-medium ${getPriorityColor(goal.priority)}`}>
                          {goal.priority}
                        </Badge>
                        <Badge variant="soft" className="text-xs font-medium bg-gray-50 text-gray-600 border-gray-100">
                          {goal.category}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 mb-1" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {goal.progress}%
                    </div>
                    <div className="text-sm text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {goal.currentValue.toLocaleString()} / {goal.targetValue.toLocaleString()}
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 mb-4" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {goal.description}
                </p>

                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      Progress
                    </span>
                    <span className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      Due: {new Date(goal.deadline).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min(goal.progress, 100)}%` }}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                    {(goal.targetValue - goal.currentValue).toLocaleString()} {goal.unit} to go
                  </div>
                  <div className="flex items-center gap-2">
                    <Button intent="gray" variant="outlined" size="sm">
                      Edit
                    </Button>
                    <Button intent="gray" variant="outlined" size="sm">
                      View Plan
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Achievements Tab */}
        {activeTab === 'achievements' && (
          <GamificationSystem />
        )}

        {/* Create Goal Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{ top: 0, left: 0, right: 0, bottom: 0 }}>
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl" data-rounded="default">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Create New Goal
                </h3>
                <Tooltip content="Close modal">
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="text-gray-400 hover:text-gray-600 text-xl font-medium w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    ×
                  </button>
                </Tooltip>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Goal Title</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow"
                    placeholder="Enter your goal title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                    <option value="revenue">Revenue</option>
                    <option value="growth">Growth</option>
                    <option value="engagement">Engagement</option>
                    <option value="content">Content</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Target Value</label>
                  <input
                    type="number"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow"
                    placeholder="Enter target value"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                  <input
                    type="date"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow"
                  />
                </div>
              </div>

              <div className="flex items-center gap-3 mt-6">
                <Button intent="gray" variant="outlined" onClick={() => setShowCreateModal(false)}>
                  Cancel
                </Button>
                <Button intent="gray" variant="outlined" onClick={() => setShowCreateModal(false)}>
                  Create Goal
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Generate Goals Modal */}
        {showGenerateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{ top: 0, left: 0, right: 0, bottom: 0 }}>
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl" data-rounded="default">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Generate AI Goals
                </h3>
                <Tooltip content="Close modal">
                  <button
                    onClick={() => setShowGenerateModal(false)}
                    className="text-gray-400 hover:text-gray-600 text-xl font-medium w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    ×
                  </button>
                </Tooltip>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-3 mb-2">
                    <SparklesIcon className="w-5 h-5 text-blue-600" />
                    <span className="font-medium text-blue-900">AI Goal Generation</span>
                  </div>
                  <p className="text-sm text-blue-700">
                    Our AI will analyze your current performance and suggest personalized goals to accelerate your growth.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Focus Area</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                    <option value="all">All Areas</option>
                    <option value="revenue">Revenue Growth</option>
                    <option value="engagement">Engagement</option>
                    <option value="content">Content Strategy</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Time Horizon</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                    <option value="30">30 Days</option>
                    <option value="60">60 Days</option>
                    <option value="90">90 Days</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center gap-3 mt-6">
                <Button intent="gray" variant="outlined" onClick={() => setShowGenerateModal(false)}>
                  Cancel
                </Button>
                <Button intent="gray" variant="outlined" onClick={() => setShowGenerateModal(false)}>
                  <SparklesIcon className="w-4 h-4 mr-2" />
                  Generate Goals
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Create Task Modal */}
        {showCreateTaskModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{ top: 0, left: 0, right: 0, bottom: 0 }}>
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-xl" data-rounded="default">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Create New Task
                </h3>
                <Tooltip content="Close modal">
                  <button
                    onClick={() => setShowCreateTaskModal(false)}
                    className="text-gray-400 hover:text-gray-600 text-xl font-medium w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    ×
                  </button>
                </Tooltip>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Task Name</label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow"
                    placeholder="Enter task name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow"
                    rows={3}
                    placeholder="Enter task description"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                    <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                      <option value="task">Task</option>
                      <option value="goal">Goal</option>
                      <option value="milestone">Milestone</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                  <input
                    type="date"
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Related Goal</label>
                  <select className="w-full border border-gray-300 rounded-lg px-3 py-2 shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md transition-shadow">
                    <option value="">Select a goal (optional)</option>
                    {goals.map(goal => (
                      <option key={goal.id} value={goal.id}>{goal.title}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex items-center gap-3 mt-6">
                <Button intent="gray" variant="outlined" onClick={() => setShowCreateTaskModal(false)}>
                  Cancel
                </Button>
                <Button intent="primary" variant="filled" onClick={() => setShowCreateTaskModal(false)}>
                  Create Task
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
