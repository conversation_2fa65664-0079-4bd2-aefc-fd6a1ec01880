'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { CoachCommentary } from '../../components/coach/CoachCommentary';
import {
  CheckCircle as CheckCircleIcon,
  Clock as ClockIcon,
  Calendar as CalendarIcon,
  Target04 as TargetIcon,
  TrendUp01 as TrendUpIcon,
  Plus as PlusIcon,
  PlayCircle as PlayIcon,
  ArrowRight as ArrowRightIcon,
  MagicWand01 as SparklesIcon
} from '@untitled-ui/icons-react';

interface ActionItem {
  id: string;
  title: string;
  description: string;
  category: 'content' | 'optimization' | 'analysis' | 'engagement';
  priority: 'high' | 'medium' | 'low';
  estimatedTime: string;
  deadline: string;
  status: 'pending' | 'in_progress' | 'completed';
  impact: string;
  relatedGoal?: string;
}

export default function ActionPlansPage() {
  const [activeFilter, setActiveFilter] = useState('all');

  const actionItems: ActionItem[] = [
    {
      id: '1',
      title: 'Create 3 livestreams this week',
      description: 'Focus on Product X during peak hours (2-4 PM EST) to maximize conversion',
      category: 'content',
      priority: 'high',
      estimatedTime: '6 hours',
      deadline: '2024-09-30',
      status: 'in_progress',
      impact: '+$2,300 GMV',
      relatedGoal: 'Reach $100K Monthly GMV'
    },
    {
      id: '2',
      title: 'A/B test new CTAs',
      description: 'Test 3 variations of call-to-action phrases to improve conversion rate',
      category: 'optimization',
      priority: 'high',
      estimatedTime: '2 hours',
      deadline: '2024-09-28',
      status: 'pending',
      impact: '+0.8% conversion',
      relatedGoal: 'Achieve 5% Conversion Rate'
    },
    {
      id: '3',
      title: 'Analyze competitor hooks',
      description: "Study @TopCreator123's top 5 performing hooks and adapt strategies",
      category: 'analysis',
      priority: 'high',
      estimatedTime: '3 hours',
      deadline: '2024-09-29',
      status: 'pending',
      impact: 'Close $2.1K gap',
      relatedGoal: 'Competitive Advantage'
    },
    {
      id: '4',
      title: 'Post 2 TikToks daily',
      description: 'Increase content frequency by 23% to match niche standards',
      category: 'content',
      priority: 'medium',
      estimatedTime: '4 hours/day',
      deadline: '2024-10-31',
      status: 'in_progress',
      impact: '+18% visibility',
      relatedGoal: 'Post 20 Videos Weekly'
    },
    {
      id: '5',
      title: 'Test beauty product category',
      description: 'Research and test 3 beauty products to diversify revenue streams',
      category: 'optimization',
      priority: 'medium',
      estimatedTime: '8 hours',
      deadline: '2024-10-15',
      status: 'pending',
      impact: '+$3,200 GMV',
      relatedGoal: 'Product Mix Optimization'
    }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'content':
        return <PlayIcon className="w-4 h-4 text-blue-600" />;
      case 'optimization':
        return <TrendUpIcon className="w-4 h-4 text-green-600" />;
      case 'analysis':
        return <TargetIcon className="w-4 h-4 text-purple-600" />;
      case 'engagement':
        return <CheckCircleIcon className="w-4 h-4 text-orange-600" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-50 text-red-600 border-red-100';
      case 'medium':
        return 'bg-orange-50 text-orange-600 border-orange-100';
      case 'low':
        return 'bg-blue-50 text-blue-600 border-blue-100';
      default:
        return 'bg-gray-50 text-gray-600 border-gray-100';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 text-green-600 border-green-100';
      case 'in_progress':
        return 'bg-blue-50 text-blue-600 border-blue-100';
      case 'pending':
        return 'bg-gray-50 text-gray-600 border-gray-100';
      default:
        return 'bg-gray-50 text-gray-600 border-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4 text-green-600" />;
      case 'in_progress':
        return <ClockIcon className="w-4 h-4 text-blue-600" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4 text-gray-600" />;
      default:
        return <ClockIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const filteredItems =
    activeFilter === 'all'
      ? actionItems
      : actionItems.filter((item) => item.status === activeFilter);

  const completedCount = actionItems.filter((item) => item.status === 'completed').length;
  const inProgressCount = actionItems.filter((item) => item.status === 'in_progress').length;
  const pendingCount = actionItems.filter((item) => item.status === 'pending').length;

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1
              className="text-3xl font-bold text-black mb-2"
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              Daily Action Plans
            </h1>
            <p
              className="text-gray-600"
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              Your personalized daily and weekly tasks to accelerate growth
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="outlined" data-rounded="default">
              <SparklesIcon className="w-4 h-4 mr-2" />
              <span>Generate New Plans</span>
            </Button>
          </div>
        </div>

        {/* Coach Commentary */}
        <CoachCommentary
          type="success"
          title="Weekly Progress Update"
          message="Great momentum! You've completed 1 out of 3 high-priority tasks this week. Focus on the A/B testing and competitor analysis to maximize your conversion improvements."
          actionable={true}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p
                  className="text-2xl font-bold text-gray-900"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  {completedCount}
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  Completed
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <ClockIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p
                  className="text-2xl font-bold text-gray-900"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  {inProgressCount}
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  In Progress
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <CalendarIcon className="w-5 h-5 text-gray-600" />
              </div>
              <div>
                <p
                  className="text-2xl font-bold text-gray-900"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  {pendingCount}
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  Pending
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <TargetIcon className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p
                  className="text-2xl font-bold text-gray-900"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  {actionItems.filter((item) => item.priority === 'high').length}
                </p>
                <p
                  className="text-sm text-gray-600"
                  style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                >
                  High Priority
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'all', label: 'All Tasks', count: actionItems.length },
            { id: 'pending', label: 'Pending', count: pendingCount },
            { id: 'in_progress', label: 'In Progress', count: inProgressCount },
            { id: 'completed', label: 'Completed', count: completedCount }
          ].map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`flex-1 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 ${
                activeFilter === filter.id
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
              }`}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              type="button"
            >
              <span>{filter.label}</span>
              <span className="ml-2 px-2 py-0.5 bg-gray-200 text-gray-600 rounded-full text-xs">
                {filter.count}
              </span>
            </button>
          ))}
        </div>

        {/* Action Items */}
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <Card
              key={item.id}
              className="p-6 transition-all duration-200 border border-gray-200 hover:border-gray-300 bg-gradient-to-br from-white to-gray-50/50"
              data-rounded="default"
            >
              {/* Header Section */}
              <div className="flex items-start gap-4 mb-6">
                <div className="flex items-center gap-3">
                  {getStatusIcon(item.status)}
                  {getCategoryIcon(item.category)}
                </div>
                <div className="flex-1">
                  <h3
                    className="text-lg font-semibold text-gray-900 mb-2"
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {item.title}
                  </h3>
                  <p
                    className="text-sm text-gray-600 mb-4"
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {item.description}
                  </p>
                  <div className="flex items-center gap-2 mb-4">
                    <Badge
                      variant="soft"
                      className={`text-xs font-medium ${getPriorityColor(item.priority)}`}
                    >
                      {item.priority}
                    </Badge>
                    <Badge
                      variant="soft"
                      className={`text-xs font-medium ${getStatusColor(item.status)}`}
                    >
                      {item.status.replace('_', ' ')}
                    </Badge>
                    <Badge
                      variant="soft"
                      className="text-xs font-medium bg-gray-50 text-gray-600 border-gray-100"
                    >
                      {item.category}
                    </Badge>
                  </div>
                  {item.relatedGoal ? (
                    <p
                      className="text-xs text-blue-600 font-medium"
                      style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                    >
                      Related to: {item.relatedGoal}
                    </p>
                  ) : null}
                </div>
                <div className="text-right">
                  <div
                    className="text-lg font-semibold text-green-600 mb-1"
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {item.impact}
                  </div>
                  <div
                    className="text-sm text-gray-600 mb-1"
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {item.estimatedTime}
                  </div>
                  <div
                    className="text-xs text-gray-500"
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    Due: {new Date(item.deadline).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex items-center gap-2">
                  <CalendarIcon className="w-4 h-4 text-gray-400" />
                  <span
                    className="text-sm text-gray-600"
                    style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                  >
                    {Math.ceil(
                      (new Date(item.deadline).getTime() - new Date().getTime()) /
                        (1000 * 60 * 60 * 24)
                    )}{' '}
                    days left
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Button intent="gray" variant="outlined" size="sm">
                    <span>Edit</span>
                  </Button>
                  {item.status === 'pending' ? (
                    <Button intent="gray" variant="outlined" size="sm">
                      <span>Start Task</span>
                      <ArrowRightIcon className="w-4 h-4 ml-1" />
                    </Button>
                  ) : null}
                  {item.status === 'in_progress' ? (
                    <Button intent="gray" variant="outlined" size="sm">
                      <span>Mark Complete</span>
                      <CheckCircleIcon className="w-4 h-4 ml-1" />
                    </Button>
                  ) : null}
                  {item.status === 'completed' ? (
                    <Badge variant="soft" className="bg-green-100 text-green-700">
                      Completed
                    </Badge>
                  ) : null}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </AppLayout>
  );
}
