'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { CoachCommentary } from '../../components/coach/CoachCommentary';
import { 
  TrendUp01 as TrendUpIcon,
  TrendDown01 as TrendDownIcon,
  CurrencyDollar as DollarSignIcon,
  Package as PackageIcon,
  Eye as EyeIcon,
  ShoppingCart01 as ShoppingCartIcon,
  Star01 as StarIcon,
  ArrowRight as ArrowRightIcon,
  FilterLines as FilterIcon,
  Download01 as DownloadIcon
} from '@untitled-ui/icons-react';

interface Product {
  id: string;
  name: string;
  category: string;
  gmv: number;
  gmvChange: number;
  conversionRate: number;
  conversionChange: number;
  views: number;
  viewsChange: number;
  rating: number;
  commission: number;
  trend: 'up' | 'down' | 'stable';
  image: string;
}

export default function TopProductsPage() {
  const [timeframe, setTimeframe] = useState('7d');
  const [categoryFilter, setCategoryFilter] = useState('all');

  const products: Product[] = [
    {
      id: '1',
      name: 'Wireless Bluetooth Earbuds Pro',
      category: 'Electronics',
      gmv: 45200,
      gmvChange: 23.5,
      conversionRate: 4.8,
      conversionChange: 0.3,
      views: 125000,
      viewsChange: 15.2,
      rating: 4.7,
      commission: 12.5,
      trend: 'up',
      image: '/api/placeholder/60/60'
    },
    {
      id: '2',
      name: 'Skincare Vitamin C Serum',
      category: 'Beauty',
      gmv: 38900,
      gmvChange: 18.7,
      conversionRate: 5.2,
      conversionChange: 0.8,
      views: 98000,
      viewsChange: 22.1,
      rating: 4.9,
      commission: 15.0,
      trend: 'up',
      image: '/api/placeholder/60/60'
    },
    {
      id: '3',
      name: 'Smart Fitness Tracker',
      category: 'Health',
      gmv: 32100,
      gmvChange: -5.2,
      conversionRate: 3.9,
      conversionChange: -0.2,
      views: 87000,
      viewsChange: -8.5,
      rating: 4.4,
      commission: 10.0,
      trend: 'down',
      image: '/api/placeholder/60/60'
    },
    {
      id: '4',
      name: 'Organic Green Tea Set',
      category: 'Food & Beverage',
      gmv: 28500,
      gmvChange: 12.3,
      conversionRate: 6.1,
      conversionChange: 0.5,
      views: 65000,
      viewsChange: 9.8,
      rating: 4.6,
      commission: 8.5,
      trend: 'up',
      image: '/api/placeholder/60/60'
    },
    {
      id: '5',
      name: 'Minimalist Phone Case',
      category: 'Accessories',
      gmv: 24800,
      gmvChange: 7.1,
      conversionRate: 4.2,
      conversionChange: 0.1,
      views: 78000,
      viewsChange: 5.3,
      rating: 4.3,
      commission: 20.0,
      trend: 'up',
      image: '/api/placeholder/60/60'
    }
  ];

  const categories = ['all', 'Electronics', 'Beauty', 'Health', 'Food & Beverage', 'Accessories'];

  const filteredProducts = categoryFilter === 'all' 
    ? products 
    : products.filter(product => product.category === categoryFilter);

  const totalGMV = products.reduce((sum, product) => sum + product.gmv, 0);
  const avgConversion = products.reduce((sum, product) => sum + product.conversionRate, 0) / products.length;
  const totalViews = products.reduce((sum, product) => sum + product.views, 0);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Top Products
            </h1>
            <p className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Your highest performing products and revenue drivers
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button intent="gray" variant="analytics" data-rounded="default">
              <FilterIcon className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button intent="gray" variant="analytics" data-rounded="default">
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Coach Commentary */}
        <CoachCommentary
          type="default"
          title="Product Performance Insights"
          message="Your Wireless Bluetooth Earbuds are crushing it with 23.5% GMV growth! Consider creating more tech content during peak hours to capitalize on this trend."
          actionable={true}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <DollarSignIcon className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  ${(totalGMV / 1000).toFixed(1)}K
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Total GMV
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <ShoppingCartIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {avgConversion.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Avg Conversion
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <EyeIcon className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {(totalViews / 1000).toFixed(0)}K
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Total Views
                </p>
              </div>
            </div>
          </Card>

          <Card className="p-4" data-rounded="default">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <PackageIcon className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  {products.length}
                </p>
                <p className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                  Active Products
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Timeframe and Category Filters */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
            {['7d', '30d', '90d'].map(period => (
              <button
                key={period}
                onClick={() => setTimeframe(period)}
                className={`
                  px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                  ${timeframe === period
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50'
                  }
                `}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                {period === '7d' ? 'Last 7 days' : period === '30d' ? 'Last 30 days' : 'Last 90 days'}
              </button>
            ))}
          </div>

          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setCategoryFilter(category)}
                className={`
                  px-3 py-2 rounded-md font-medium text-sm transition-all duration-200
                  ${categoryFilter === category 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                  }
                `}
                style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
              >
                {category === 'all' ? 'All Categories' : category}
              </button>
            ))}
          </div>
        </div>

        {/* Products List */}
        <div className="space-y-4">
          {filteredProducts.map((product, index) => (
            <Card key={product.id} className="p-6 transition-all duration-200 border border-gray-200 hover:border-blue-400/60" data-rounded="default">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl font-bold text-gray-400" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      #{index + 1}
                    </span>
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <PackageIcon className="w-6 h-6 text-gray-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-black mb-1" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {product.name}
                    </h3>
                    <div className="flex items-center gap-2">
                      <Badge variant="soft" className="text-xs bg-gray-100 text-gray-700">
                        {product.category}
                      </Badge>
                      <div className="flex items-center gap-1">
                        <StarIcon className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                          {product.rating}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-8">
                  <div className="text-center">
                    <p className="text-lg font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      ${(product.gmv / 1000).toFixed(1)}K
                    </p>
                    <div className="flex items-center gap-1">
                      {product.gmvChange > 0 ? (
                        <TrendUpIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendDownIcon className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm font-medium ${product.gmvChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {product.gmvChange > 0 ? '+' : ''}{product.gmvChange}%
                      </span>
                    </div>
                    <p className="text-xs text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>GMV</p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {product.conversionRate}%
                    </p>
                    <div className="flex items-center gap-1">
                      {product.conversionChange > 0 ? (
                        <TrendUpIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendDownIcon className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm font-medium ${product.conversionChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {product.conversionChange > 0 ? '+' : ''}{product.conversionChange}%
                      </span>
                    </div>
                    <p className="text-xs text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Conversion</p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {(product.views / 1000).toFixed(0)}K
                    </p>
                    <div className="flex items-center gap-1">
                      {product.viewsChange > 0 ? (
                        <TrendUpIcon className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendDownIcon className="w-4 h-4 text-red-600" />
                      )}
                      <span className={`text-sm font-medium ${product.viewsChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {product.viewsChange > 0 ? '+' : ''}{product.viewsChange}%
                      </span>
                    </div>
                    <p className="text-xs text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Views</p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-bold text-black" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                      {product.commission}%
                    </p>
                    <p className="text-xs text-gray-500" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Commission</p>
                  </div>

                  <Button intent="primary" variant="outlined" size="sm">
                    View Details
                    <ArrowRightIcon className="w-4 h-4 ml-1" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </AppLayout>
  );
}
