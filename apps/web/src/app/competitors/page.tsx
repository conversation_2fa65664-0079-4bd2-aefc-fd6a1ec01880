'use client';

import { CompetitorDashboard } from '../../components/CompetitorDashboard';
import { AppLayout } from '../../components/layout/AppLayout';


export default function CompetitorsPage() {
  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            All Creators
          </h1>
          <p className="text-gray-600 dark:text-gray-300" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Track and analyze your competition
          </p>
        </div>

        <CompetitorDashboard />
      </div>
    </AppLayout>
  );
}