'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import CustomAreaChart from '../../components/charts/AreaChart';
import MetricsCard from '../../components/charts/MetricsCard';
import EmptyState from '../../components/ui/EmptyState';
import { 
  BarChart3Icon, 
  TrendingUpIcon, 
  CalendarIcon, 
  DownloadIcon,
  DollarSignIcon,
  ShoppingCartIcon,
  UsersIcon,
  TargetIcon
} from 'lucide-react';

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('7d');

  // Mock analytics data
  const analyticsData = [
    { name: 'Jan', value: 4000 },
    { name: 'Feb', value: 3000 },
    { name: 'Mar', value: 2000 },
    { name: 'Apr', value: 2780 },
    { name: 'May', value: 1890 },
    { name: 'Jun', value: 2390 },
    { name: 'Jul', value: 3490 },
  ];

  const formatCurrency = (value: number) => `$${value.toLocaleString()}`;

  const stats = [
    { label: 'Total Revenue', value: '$12,345', change: '+12.5%', trend: 'up', icon: DollarSignIcon },
    { label: 'Orders', value: '1,234', change: '+8.2%', trend: 'up', icon: ShoppingCartIcon },
    { label: 'Conversion Rate', value: '3.4%', change: '-0.5%', trend: 'down', icon: TargetIcon },
    { label: 'Active Products', value: '89', change: '+5', trend: 'up', icon: UsersIcon },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center mb-2">
              <BarChart3Icon className="w-8 h-8 text-blue-600 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
            </div>
            <p className="text-gray-600">
              Track your performance and gain insights into your creator business
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4 text-gray-500" />
              <select 
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                data-rounded="default"
              >
                <option value="1d">Last 24 hours</option>
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
            </div>
            <button
              className="px-4 py-2 rounded-lg text-gray-700 font-medium transition-all duration-200 disabled:opacity-50 flex items-center bg-white border border-gray-200 hover:border-blue-300 hover:bg-blue-50/30 focus:outline-none focus:ring-1 focus:ring-blue-300"
              style={{
                fontFamily: 'General Sans, system-ui, sans-serif'
              }}
              data-rounded="default"
            >
              <DownloadIcon className="w-4 h-4 mr-2" />
              Export Report
            </button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <Card key={stat.label} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    <Badge 
                      variant="soft" 
                      intent={stat.trend === 'up' ? 'success' : 'danger'}
                      size="sm"
                    >
                      {stat.change}
                    </Badge>
                  </div>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <stat.icon className="w-6 h-6 text-gray-600" />
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Chart */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
              <Button intent="gray" variant="ghost" size="sm" data-rounded="default">
                View Details
              </Button>
            </div>
            <CustomAreaChart
              title=""
              data={analyticsData}
              primaryColor="#007FFF"
              formatValue={formatCurrency}
              height={250}
              className="border-0 shadow-none p-0"
            />
          </Card>

          {/* Top Products */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Top Performing Products</h3>
              <Button intent="gray" variant="ghost" size="sm" data-rounded="default">
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((item) => (
                <div key={item} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                    <div>
                      <p className="font-medium text-gray-900">Product {item}</p>
                      <p className="text-sm text-gray-500">Category</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">${(Math.random() * 1000).toFixed(0)}</p>
                    <p className="text-sm text-gray-500">{Math.floor(Math.random() * 100)} sales</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Performance Metrics */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Breakdown</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                <TrendingUpIcon className="w-8 h-8 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Growth Rate</h4>
              <p className="text-2xl font-bold text-green-600 mt-2">+15.3%</p>
              <p className="text-sm text-gray-500 mt-1">vs last period</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <TargetIcon className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Goal Progress</h4>
              <p className="text-2xl font-bold text-blue-600 mt-2">78%</p>
              <p className="text-sm text-gray-500 mt-1">of monthly target</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <UsersIcon className="w-8 h-8 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Audience Reach</h4>
              <p className="text-2xl font-bold text-purple-600 mt-2">24.5K</p>
              <p className="text-sm text-gray-500 mt-1">total impressions</p>
            </div>
          </div>
        </Card>
      </div>
    </AppLayout>
  );
}