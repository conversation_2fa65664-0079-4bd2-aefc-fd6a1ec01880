'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import { TikTokShopConnection } from '../../components/integrations/TikTokShopConnection';
import {
  Settings01 as SettingsIcon,
  User01 as UserIcon,
  Bell01 as BellIcon,
  Shield01 as ShieldIcon,
  CreditCard01 as CreditCardIcon,
  HelpCircle as HelpCircleIcon,
  Save01 as SaveIcon, 
  Link01 as LinkIcon
} from '@untitled-ui/icons-react';

export default function SettingsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('profile');

  const tabs = [
    { id: 'profile', label: 'Profile', icon: UserIcon },
    { id: 'integrations', label: 'Integrations', icon: LinkIcon },
    { id: 'notifications', label: 'Notifications', icon: BellIcon },
    { id: 'security', label: 'Security', icon: ShieldIcon },
    { id: 'billing', label: 'Billing', icon: CreditCardIcon },
    { id: 'support', label: 'Support', icon: HelpCircleIcon },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold text-black mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Settings
          </h1>
          <p className="text-gray-600" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
            Manage your account settings and preferences
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <Card className="p-4" data-rounded="default">
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-50 text-primary-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    data-rounded="default"
                  >
                    <tab.icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === 'profile' && (
              <Card className="p-6" data-rounded="default">
                <h2 className="text-xl font-semibold text-black mb-6" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>Profile Information</h2>
                <div className="space-y-6">
                  <div className="flex items-center gap-6">
                    <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                      <UserIcon className="w-8 h-8 text-gray-400" />
                    </div>
                    <div>
                      <Button intent="primary" size="sm" data-rounded="default">
                        Change Photo
                      </Button>
                      <p className="text-sm text-gray-500 mt-1">JPG, GIF or PNG. Max size 2MB.</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
                        Full Name
                      </label>
                      <input
                        type="text"
                        defaultValue="John Doe"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
                        data-rounded="default"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        data-rounded="default"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        TikTok Username
                      </label>
                      <input
                        type="text"
                        defaultValue="@johndoe"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        data-rounded="default"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        defaultValue="+****************"
                        className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        data-rounded="default"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Bio
                    </label>
                    <textarea
                      rows={4}
                      defaultValue="TikTok creator focused on tech and lifestyle content..."
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      data-rounded="default"
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button intent="primary" data-rounded="default">
                      <SaveIcon className="w-4 h-4 mr-2" />
                      Save Changes
                    </Button>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'integrations' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Connected Accounts</h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">TikTok Shop</h3>
                    {session?.user?.id ? (
                      <TikTokShopConnection userId={session.user.id} />
                    ) : (
                      <div className="p-4 border border-gray-200 rounded-lg">
                        <p className="text-gray-500">Please sign in to manage your integrations.</p>
                      </div>
                    )}
                  </div>



                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="w-5 h-5 text-blue-600 mt-0.5">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="font-medium text-blue-900">Why connect your accounts?</h4>
                        <p className="text-sm text-blue-800 mt-1">
                          Connecting your seller accounts allows Xact Data to automatically sync your sales data, 
                          track performance metrics, and provide personalized insights to help grow your business.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'notifications' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Notification Preferences</h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>
                    <div className="space-y-4">
                      {[
                        { label: 'Product Alerts', description: 'Get notified when products exceed your thresholds' },
                        { label: 'Competitor Updates', description: 'Updates about your tracked competitors' },
                        { label: 'Weekly Reports', description: 'Weekly performance summaries' },
                        { label: 'Marketing Tips', description: 'Tips and strategies to grow your business' },
                      ].map((item) => (
                        <div key={item.label} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{item.label}</p>
                            <p className="text-sm text-gray-500">{item.description}</p>
                          </div>
                          <input type="checkbox" defaultChecked className="rounded" />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Push Notifications</h3>
                    <div className="space-y-4">
                      {[
                        { label: 'Real-time Alerts', description: 'Instant notifications for trending products' },
                        { label: 'Goal Achievements', description: 'Celebrate when you reach your goals' },
                        { label: 'Daily Summaries', description: 'End-of-day performance updates' },
                      ].map((item) => (
                        <div key={item.label} className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">{item.label}</p>
                            <p className="text-sm text-gray-500">{item.description}</p>
                          </div>
                          <input type="checkbox" className="rounded" />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button intent="primary" data-rounded="default">
                      Save Preferences
                    </Button>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'security' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Security Settings</h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Password</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Current Password
                        </label>
                        <input
                          type="password"
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          data-rounded="default"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          New Password
                        </label>
                        <input
                          type="password"
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          data-rounded="default"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          data-rounded="default"
                        />
                      </div>
                      <Button intent="primary" data-rounded="default">
                        Update Password
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Two-Factor Authentication</h3>
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">Enable 2FA</p>
                        <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
                      </div>
                      <Button intent="primary" variant="outlined" data-rounded="default">
                        Enable
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'billing' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Billing & Subscription</h2>
                <div className="space-y-6">
                  <div className="p-4 bg-primary-50 border border-primary-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold text-primary-900">Pro Plan</h3>
                        <p className="text-sm text-primary-700">$29/month • Billed monthly</p>
                      </div>
                      <Badge variant="soft" intent="primary">Active</Badge>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Method</h3>
                    <div className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gray-200 rounded"></div>
                          <div>
                            <p className="font-medium text-gray-900">•••• •••• •••• 1234</p>
                            <p className="text-sm text-gray-500">Expires 12/25</p>
                          </div>
                        </div>
                        <Button intent="gray" variant="outlined" size="sm" data-rounded="default">
                          Update
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Billing History</h3>
                    <div className="space-y-3">
                      {[
                        { date: 'Dec 1, 2023', amount: '$29.00', status: 'Paid' },
                        { date: 'Nov 1, 2023', amount: '$29.00', status: 'Paid' },
                        { date: 'Oct 1, 2023', amount: '$29.00', status: 'Paid' },
                      ].map((invoice) => (
                        <div key={invoice.date} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900">{invoice.date}</p>
                            <p className="text-sm text-gray-500">Pro Plan</p>
                          </div>
                          <div className="flex items-center gap-3">
                            <span className="font-medium text-gray-900">{invoice.amount}</span>
                            <Badge variant="soft" intent="primary" size="sm">{invoice.status}</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {activeTab === 'support' && (
              <Card className="p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Support & Help</h2>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="p-4 border border-gray-200 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-2">Documentation</h3>
                      <p className="text-sm text-gray-500 mb-4">Learn how to use Xact Data effectively</p>
                      <Button intent="primary" variant="outlined" size="sm" data-rounded="default">
                        View Docs
                      </Button>
                    </div>
                    <div className="p-4 border border-gray-200 rounded-lg">
                      <h3 className="font-semibold text-gray-900 mb-2">Video Tutorials</h3>
                      <p className="text-sm text-gray-500 mb-4">Step-by-step guides and tutorials</p>
                      <Button intent="primary" variant="outlined" size="sm" data-rounded="default">
                        Watch Videos
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Support</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Subject
                        </label>
                        <input
                          type="text"
                          placeholder="How can we help you?"
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          data-rounded="default"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Message
                        </label>
                        <textarea
                          rows={6}
                          placeholder="Describe your issue or question..."
                          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          data-rounded="default"
                        />
                      </div>
                      <Button intent="primary" data-rounded="default">
                        Send Message
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}