@import url('https://api.fontshare.com/v2/css?f[]=general-sans@500&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Clean Dark Mode Variables - Black/White/Gray Only */
@layer base {
  :root {
    /* Light mode - clean whites and grays */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;
    --primary: 221.2 83.2% 53.3%; /* Keep blue for primary actions */
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode - PURE blacks and grays (no blue tints) */
    --background: 0 0% 7%; /* Rich dark gray */
    --foreground: 0 0% 95%; /* Clean white */
    --card: 0 0% 9%; /* Slightly lighter than background for depth */
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 12%; /* Lighter for popover distinction */
    --popover-foreground: 0 0% 95%;
    --primary: 221.2 83.2% 53.3%; /* Keep blue for primary actions only */
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 18%; /* Medium dark gray */
    --secondary-foreground: 0 0% 95%;
    --muted: 0 0% 18%;
    --muted-foreground: 0 0% 70%; /* Better contrast */
    --accent: 0 0% 18%;
    --accent-foreground: 0 0% 95%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%; /* Lighter borders for better definition */
    --input: 0 0% 18%;
    --ring: 0 0% 83%;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

/* Removed automatic dark mode media query to prevent hydration issues */
/* Dark mode will be controlled manually via class-based approach */

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Consistent border radius for all elements with data-rounded attribute */
[data-rounded="default"] {
  border-radius: 0.5rem; /* 8px - consistent radius */
}

/* Ensure all interactive elements have consistent radius */
button, input, select, textarea, .card, .badge {
  border-radius: 0.5rem;
}

/* Override any conflicting radius classes */
.rounded-lg {
  border-radius: 0.5rem !important;
}

.rounded-xl {
  border-radius: 0.75rem !important;
}

.rounded-2xl {
  border-radius: 1rem !important;
}

/* Custom slider styling */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #007FFF;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #007FFF;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-track {
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
}

.slider::-moz-range-track {
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  border: none;
}

/* Dark mode slider styling */
.dark .slider::-webkit-slider-thumb {
  border: 2px solid #374151;
}

.dark .slider::-moz-range-thumb {
  border: 2px solid #374151;
}

.dark .slider::-webkit-slider-track {
  background: #4b5563;
}

.dark .slider::-moz-range-track {
  background: #4b5563;
}







@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}