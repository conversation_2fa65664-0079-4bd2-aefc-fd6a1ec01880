'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout/AppLayout';
import { Card } from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Badge from '../../components/ui/Badge';
import Calendar from '../../components/ui/Calendar';
import {
  CheckCircle as CheckCircleIcon,
  Calendar as CalendarViewIcon,
  LayoutGrid01 as GridIcon,
  List as ListIcon,
  Eye as EyeIcon
} from '@untitled-ui/icons-react';

type ViewType = 'cards' | 'kanban' | 'calendar' | 'insights';

interface ActionItem {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: 'content' | 'optimization' | 'engagement' | 'analytics';
  status: 'todo' | 'in-progress' | 'done';
  dueDate?: Date;
  estimatedTime?: string;
}

const mockActionItems: ActionItem[] = [
  {
    id: '1',
    title: 'Upload 2 TikToks for Product X',
    description: 'Competitors avg. 5 videos/week, you\'re at 3.',
    impact: 'high',
    category: 'content',
    status: 'todo',
    dueDate: new Date(),
    estimatedTime: '2 hours'
  },
  {
    id: '2',
    title: 'Update product description for +5% CTR',
    description: 'Weak CTA compared to competitor benchmarks.',
    impact: 'high',
    category: 'optimization',
    status: 'todo',
    estimatedTime: '30 mins'
  },
  {
    id: '3',
    title: 'Run livestream session',
    description: 'Boost engagement and showcase new products.',
    impact: 'medium',
    category: 'engagement',
    status: 'in-progress',
    estimatedTime: '1 hour'
  },
  {
    id: '4',
    title: 'Analyze competitor hashtag strategy',
    description: 'Research trending hashtags in your niche.',
    impact: 'medium',
    category: 'analytics',
    status: 'done',
    estimatedTime: '45 mins'
  }
];

const insights = [
  "Your posting frequency is 20% below top competitors.",
  "AOV is trending +8% — double down on Product X.",
  "Retention is weak: competitors avg. 35s watch time.",
  "Your engagement rate peaked at 4.2% last Tuesday.",
  "Consider posting between 6-8 PM for better reach."
];

export default function AICoachPage() {
  const [activeView, setActiveView] = useState<ViewType>('cards');
  const [actionItems, setActionItems] = useState<ActionItem[]>(mockActionItems);

  const progress = 32; // 32% progress to $5K goal
  const completedTasks = actionItems.filter(item => item.status === 'done').length;
  const totalTasks = actionItems.length;
  const streak = 3; // 3 days in a row

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'low': return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-400';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high': return CheckCircleIcon;
      case 'medium': return CheckCircleIcon;
      case 'low': return CheckCircleIcon;
      default: return CheckCircleIcon;
    }
  };

  const markAsDone = (id: string) => {
    setActionItems(prev => prev.map(item => 
      item.id === id ? { ...item, status: 'done' } : item
    ));
  };



  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-black dark:text-white mb-2" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              AI Growth Coach
            </h1>
            <p className="text-gray-600 dark:text-gray-400" style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}>
              Your personalized daily/weekly action plan
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 font-sans">Progress to $5K Goal</h3>
            <span className="text-2xl font-bold text-blue-600 dark:text-blue-400 font-sans">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
            <div 
              className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 font-sans">
            <span>Current: $1,600</span>
            <span>Target: $5,000</span>
          </div>
        </Card>

        {/* View Toggle */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-fit">
          {[
            { id: 'cards', label: 'Cards', icon: GridIcon },
            { id: 'kanban', label: 'Kanban', icon: ListIcon },
            { id: 'calendar', label: 'Calendar', icon: CalendarViewIcon },
            { id: 'insights', label: 'Insights', icon: EyeIcon }
          ].map((view) => (
            <button
              key={view.id}
              onClick={() => setActiveView(view.id as ViewType)}
              className={`
                flex items-center gap-2 px-4 py-2 rounded-md font-medium text-sm transition-all duration-200
                ${activeView === view.id
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-white/50 dark:hover:bg-gray-700/50'
                }
              `}
              style={{ fontFamily: 'General Sans, system-ui, sans-serif' }}
            >
              <view.icon className="w-4 h-4" />
              {view.label}
            </button>
          ))}
        </div>

        {/* Content Views */}
        {activeView === 'cards' && (
          <div className="space-y-4">
            {actionItems.filter(item => item.status !== 'done').map((item) => (
              <Card key={item.id} className="p-6 border border-gray-200 dark:border-gray-700">
                <div className="space-y-4">
                  {/* Badge */}
                  <div>
                    <Badge variant="soft" className={getImpactColor(item.impact)}>
                      {item.impact === 'high' ? 'High Revenue Impact' :
                       item.impact === 'medium' ? 'Optimization' : 'Low Priority'}
                    </Badge>
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-semibold text-black dark:text-white font-sans">
                    {item.title}
                  </h3>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-400 font-sans">
                    Why: {item.description}
                  </p>

                  {/* Action Buttons */}
                  <div className="flex gap-3">
                    <Button
                      intent="primary"
                      variant="filled"
                      size="sm"
                      className="font-sans"
                    >
                      {item.category === 'content' ? 'Schedule Post' :
                       item.category === 'optimization' ? 'Edit Now' : 'Start Task'}
                    </Button>
                    <Button
                      intent="gray"
                      variant="outlined"
                      size="sm"
                      onClick={() => markAsDone(item.id)}
                      className="font-sans"
                    >
                      Mark Done
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeView === 'kanban' && (
          <Card className="p-6">
            <div className="grid grid-cols-4 gap-6">
              {/* Today Column */}
              <div className="space-y-3">
                <h3 className="font-semibold text-black dark:text-white font-sans border-b border-gray-200 dark:border-gray-700 pb-2">
                  Today
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Upload 2 videos
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Review script
                  </div>
                </div>
              </div>

              {/* This Week Column */}
              <div className="space-y-3">
                <h3 className="font-semibold text-black dark:text-white font-sans border-b border-gray-200 dark:border-gray-700 pb-2">
                  This Week
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Optimize desc
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Run livestream
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Competitor check
                  </div>
                </div>
              </div>

              {/* Backlog Column */}
              <div className="space-y-3">
                <h3 className="font-semibold text-black dark:text-white font-sans border-b border-gray-200 dark:border-gray-700 pb-2">
                  Backlog
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Test new CTA
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300 font-sans">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    Explore hashtags
                  </div>
                </div>
              </div>

              {/* Done Column */}
              <div className="space-y-3">
                <h3 className="font-semibold text-black dark:text-white font-sans border-b border-gray-200 dark:border-gray-700 pb-2">
                  Done
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-500 font-sans opacity-60">
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                    Analytics review
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}

        {activeView === 'calendar' && (
          <Card className="p-6">
            <div className="space-y-4">
              {/* Calendar Header */}
              <div className="grid grid-cols-7 gap-4 border-b border-gray-200 dark:border-gray-700 pb-3">
                {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
                  <div key={day} className="text-center font-semibold text-black dark:text-white font-sans">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-4 min-h-[120px]">
                {/* Monday - Empty */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                </div>

                {/* Tuesday - Upload video */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                  <div className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-2 py-1 rounded font-sans">
                    Upload video
                  </div>
                </div>

                {/* Wednesday - Empty */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                </div>

                {/* Thursday - Run stream */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                  <div className="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded font-sans">
                    Run stream
                  </div>
                </div>

                {/* Friday - Empty */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                </div>

                {/* Saturday - Empty */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                </div>

                {/* Sunday - Empty */}
                <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[80px]">
                </div>
              </div>
            </div>
          </Card>
        )}

        {activeView === 'insights' && (
          <Card className="p-6">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center gap-3 border-b border-gray-200 dark:border-gray-700 pb-4">
                <span className="text-lg">📊</span>
                <h3 className="text-lg font-semibold text-black dark:text-white font-sans">
                  Insights from AI Coach
                </h3>
              </div>

              {/* Insights List */}
              <div className="space-y-3">
                {insights.map((insight, index) => (
                  <div key={index} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p className="text-gray-800 dark:text-gray-200 font-sans">"{insight}"</p>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}


      </div>
    </AppLayout>
  );
}
