'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card } from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import { CheckCircleIcon, XCircleIcon, RefreshCwIcon } from 'lucide-react';

export default function TikTokShopCallbackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [shopInfo, setShopInfo] = useState<any>(null);

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      if (error) {
        setStatus('error');
        setMessage(`Authorization failed: ${error}`);
        return;
      }

      if (!code || !state) {
        setStatus('error');
        setMessage('Missing authorization code or state parameter');
        return;
      }

      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api-production-7bd1.up.railway.app';
        const redirectUri = `${window.location.origin}/auth/tiktok-shop/callback`;
        
        console.log('Processing TikTok Shop callback:', { code: !!code, state: !!state, redirectUri });
        
        const response = await fetch(`${apiUrl}/api/auth/tiktok-shop/callback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code, state, redirectUri }),
        });

        const data = await response.json();
        console.log('Callback response:', data);

        if (data.success) {
          setStatus('success');
          setMessage('TikTok Shop account connected successfully!');
          setShopInfo(data.data.sellerInfo);
          
          // Add a longer delay to ensure database updates are complete
          setTimeout(() => {
            const redirectUrl = data.data.redirectUrl || '/settings?tab=integrations&refresh=true&timestamp=' + Date.now();
            console.log('Redirecting to:', redirectUrl);
            router.push(redirectUrl);
          }, 4000);
        } else {
          setStatus('error');
          setMessage(data.message || 'Failed to connect TikTok Shop account');
          console.error('Callback failed:', data);
        }
      } catch (error) {
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
        console.error('TikTok Shop callback error:', error);
      }
    };

    handleCallback();
  }, [searchParams, router]);

  const handleRetry = () => {
    router.push('/settings?tab=integrations');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-md w-full p-8">
        <div className="text-center">
          {status === 'loading' && (
            <>
              <RefreshCwIcon className="w-12 h-12 text-primary-600 mx-auto mb-4 animate-spin" />
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                Connecting Your TikTok Shop
              </h1>
              <p className="text-gray-600">
                Please wait while we process your authorization...
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <CheckCircleIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                Connection Successful!
              </h1>
              <p className="text-gray-600 mb-4">{message}</p>
              
              {shopInfo && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg mb-6">
                  <div className="text-left">
                    <h3 className="font-medium text-green-900 mb-2">Connected Shop:</h3>
                    <div className="space-y-1 text-sm text-green-800">
                      <p><strong>Name:</strong> {shopInfo.shop_name}</p>
                      <p><strong>Region:</strong> {shopInfo.shop_region}</p>
                      <p><strong>Shop ID:</strong> {shopInfo.shop_id}</p>
                      <p><strong>Type:</strong> {shopInfo.seller_type}</p>
                    </div>
                  </div>
                </div>
              )}
              
              <p className="text-sm text-gray-500">
                Redirecting you back to settings in a few seconds...
              </p>
            </>
          )}

          {status === 'error' && (
            <>
              <XCircleIcon className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h1 className="text-xl font-semibold text-gray-900 mb-2">
                Connection Failed
              </h1>
              <p className="text-gray-600 mb-6">{message}</p>
              
              <div className="space-y-3">
                <Button 
                  intent="primary" 
                  onClick={handleRetry}
                  data-rounded="default"
                  className="w-full"
                >
                  Try Again
                </Button>
                
                <Button 
                  intent="gray" 
                  variant="outlined"
                  onClick={() => router.push('/settings')}
                  data-rounded="default"
                  className="w-full"
                >
                  Back to Settings
                </Button>
              </div>
            </>
          )}
        </div>
      </Card>
    </div>
  );
}