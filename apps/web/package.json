{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "pnpm --filter @xact-data/database build && pnpm --filter @xact-data/ai-wrapper build && pnpm --filter @xact-data/shared build && next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.0.18", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dropdown-menu": "^2.1.16", "@tailus/themer": "^0.1.8", "@uidotdev/usehooks": "^2.4.1", "@untitled-ui/icons-react": "^0.1.4", "@whop/api": "^0.0.50", "@xact-data/shared": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^4.1.0", "jotai": "^2.14.0", "lodash.throttle": "^4.1.1", "lucide-react": "^0.292.0", "next": "14.0.3", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/lodash.throttle": "^4.1.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.16", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8.4.32", "tailwindcss": "^3.3.0", "typescript": "^5"}}