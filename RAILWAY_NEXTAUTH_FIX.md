# 🔧 Railway NextAuth.js Fix - SPARK-37

## Issue Description
NextAuth.js API routes are returning 500 errors because required environment variables are not properly configured in the Railway deployment.

**Error Messages:**
```
GET https://web-production-611c4.up.railway.app/api/auth/session 500 (Internal Server Error)
POST https://web-production-611c4.up.railway.app/api/auth/_log 500 (Internal Server Error)
```

## Root Cause
The web frontend service in Railway is missing critical environment variables required for NextAuth.js to function properly.

## ✅ Solution

### 1. Railway Environment Variables Configuration

In your Railway dashboard, go to your **Web Frontend service** and add the following environment variables:

#### Required NextAuth.js Variables:
```env
NEXTAUTH_URL=https://web-production-611c4.up.railway.app
NEXTAUTH_SECRET=your-secure-nextauth-secret-key-here
```

#### Required Whop OAuth Variables:
```env
WHOP_CLIENT_ID=app_DA2C9XoK7mRye9
WHOP_CLIENT_SECRET=xWhxsuZSsC4OsI8PBURSJORXMAVd2n3jC_3xbDgZU6E
```

#### Required API Configuration:
```env
NEXT_PUBLIC_API_URL=https://api-production-7bd1.up.railway.app
NODE_ENV=production
```

### 2. Important Notes

#### NEXTAUTH_URL Configuration:
- **Current Value:** `https://web-production-611c4.up.railway.app`
- **This must match exactly where your web frontend is deployed**
- If you want to change the web frontend URL to match the API pattern (`web-production-7bd1.up.railway.app`), you'll need to:
  1. Update the Railway service configuration
  2. Update the `NEXTAUTH_URL` accordingly
  3. Update Whop OAuth redirect URIs

#### NEXTAUTH_SECRET:
- **Generate a secure secret:** Use `openssl rand -base64 32` or similar
- **Keep it secure:** This should be a long, random string
- **Never commit to code:** Only set in Railway dashboard

### 3. Whop OAuth Redirect URI Update

If you change the web frontend URL, update the Whop OAuth app configuration:
- **Current:** `https://web-production-611c4.up.railway.app/api/auth/callback/whop`
- **If changed:** `https://new-web-url.up.railway.app/api/auth/callback/whop`

### 4. Deployment Steps

1. **Set Environment Variables** in Railway dashboard for web service
2. **Redeploy** the web frontend service
3. **Test** the authentication flow
4. **Verify** that `/api/auth/session` returns 200 instead of 500

## 🔍 Verification

After setting the environment variables and redeploying:

1. Visit your web frontend URL
2. Open browser developer tools
3. Check that these requests return 200:
   - `GET /api/auth/session`
   - `GET /api/auth/providers`
4. Try the login flow with Whop OAuth

## 📋 Environment Variables Summary

For quick copy-paste into Railway dashboard:

```env
# NextAuth.js Configuration
NEXTAUTH_URL=https://web-production-611c4.up.railway.app
NEXTAUTH_SECRET=your-secure-nextauth-secret-key-here

# Whop OAuth Configuration  
WHOP_CLIENT_ID=app_DA2C9XoK7mRye9
WHOP_CLIENT_SECRET=xWhxsuZSsC4OsI8PBURSJORXMAVd2n3jC_3xbDgZU6E

# API Configuration
NEXT_PUBLIC_API_URL=https://api-production-7bd1.up.railway.app
NODE_ENV=production
```

## 🚨 Security Note

The Whop client credentials shown above are already in the codebase documentation, but in a production environment, you should:
1. Rotate these credentials if they've been exposed
2. Use Railway's environment variable encryption
3. Never commit secrets to version control

---

**Status:** ✅ Ready to Deploy  
**Next Steps:** Set environment variables in Railway dashboard and redeploy web service