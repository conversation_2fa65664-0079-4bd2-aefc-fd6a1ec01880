[variables]
NIXPACKS_METADATA = "node,pnpm"
NODE_VERSION = "18"

[phases.setup]
nixPkgs = ["nodejs_18", "pnpm"]

[phases.install]
cmds = [
  "pnpm install --frozen-lockfile"
]

[phases.build]
cmds = [
  "pnpm --filter @xact-data/shared build",
  "pnpm --filter @xact-data/database db:generate",
  "pnpm --filter @xact-data/database build", 
  "pnpm --filter @xact-data/ai-wrapper build",
  "pnpm --filter api build"
]

[phases.deploy]
cmds = [
  "echo '🔍 Running Railway database diagnostics...'",
  "chmod +x scripts/railway-debug.sh",
  "./scripts/railway-debug.sh || echo 'Debug script completed with warnings'",
  "pnpm --filter @xact-data/database db:push --accept-data-loss || echo 'Database schema sync failed or already up to date'",
  "node test-db-connection.js || echo 'Database connection test completed'"
]

[start]
cmd = "pnpm --filter api start"