#!/bin/bash

# Xact Data Setup Script
echo "🚀 Setting up Xact Data development environment..."

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Please install pnpm first:"
    echo "npm install -g pnpm"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Build shared packages
echo "🔨 Building shared packages..."
pnpm --filter @xact-data/shared build
pnpm --filter @xact-data/database build
pnpm --filter @xact-data/ai-wrapper build

# Copy environment files
echo "📋 Setting up environment files..."
if [ ! -f apps/api/.env ]; then
    cp apps/api/.env.example apps/api/.env
    echo "✅ Created apps/api/.env from example"
fi

if [ ! -f apps/worker/.env ]; then
    cp apps/worker/.env.example apps/worker/.env
    echo "✅ Created apps/worker/.env from example"
fi

if [ ! -f packages/database/.env ]; then
    cp packages/database/.env.example packages/database/.env
    echo "✅ Created packages/database/.env from example"
fi

echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update the .env files with your actual values:"
echo "   - Supabase connection details (already configured)"
echo "   - AI API key (Gemini)"
echo "   - TikTok Shop credentials"
echo ""
echo "2. Set up your Supabase database:"
echo "   cd packages/database"
echo "   pnpm db:generate  # Generate Prisma client"
echo "   pnpm db:push      # Push schema to Supabase"
echo "   pnpm db:seed      # Seed with initial data"
echo ""
echo "3. Start development:"
echo "   pnpm dev"
echo ""
echo "🔗 Applications will be available at:"
echo "   - Web:    http://localhost:3000"
echo "   - API:    http://localhost:8080"
echo "   - Worker: Background process"