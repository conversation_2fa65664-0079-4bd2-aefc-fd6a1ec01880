#!/bin/bash

# Railway Database Troubleshooting Script
echo "🔧 Railway Database Troubleshooting Tool"
echo "========================================"

# Check environment variables
echo "📋 Environment Variables:"
echo "NODE_ENV: ${NODE_ENV:-not set}"
echo "PORT: ${PORT:-not set}"
echo "RAILWAY_ENVIRONMENT: ${RAILWAY_ENVIRONMENT:-not set}"

if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL: not set"
    echo "💡 Set DATABASE_URL in Railway dashboard to your PostgreSQL service URL"
else
    echo "✅ DATABASE_URL: configured"
    # Show DATABASE_URL format without exposing credentials
    echo "   Format: $(echo $DATABASE_URL | sed -E 's/(postgresql:\/\/)[^:]+:[^@]+@/\1[user]:[password]@/')"
fi

echo ""
echo "🔍 Railway Service Checks:"

# Check if we're in Railway environment
if [ -n "$RAILWAY_ENVIRONMENT" ]; then
    echo "✅ Running in Railway environment: $RAILWAY_ENVIRONMENT"
else
    echo "❌ Not running in Railway environment"
    echo "💡 This script should be run in Railway deployment"
fi

# Check build version
if [ -n "$RAILWAY_BETA_ENABLE_BUILD_V2" ]; then
    echo "✅ Build V2 enabled: $RAILWAY_BETA_ENABLE_BUILD_V2"
else
    echo "⚠️  Build V2 not enabled"
    echo "💡 Set RAILWAY_BETA_ENABLE_BUILD_V2=1 in Railway dashboard"
fi

echo ""
echo "📦 Package Status:"

# Check if Prisma client is generated
if [ -d "node_modules/.prisma" ]; then
    echo "✅ Prisma client generated"
else
    echo "❌ Prisma client not found"
    echo "💡 Run: pnpm --filter @xact-data/database db:generate"
fi

# Check if packages are built
if [ -f "packages/database/dist/index.js" ]; then
    echo "✅ Database package built"
else
    echo "❌ Database package not built"
    echo "💡 Run: pnpm --filter @xact-data/database build"
fi

if [ -f "apps/api/dist/index.js" ]; then
    echo "✅ API package built"
else
    echo "❌ API package not built"
    echo "💡 Run: pnpm --filter api build"
fi

echo ""
echo "🗄️ Database Connection Test:"

if [ -n "$DATABASE_URL" ]; then
    echo "✅ DATABASE_URL is set"
    echo "🔍 Testing Supabase connection..."
    cd packages/database
    pnpm exec prisma db push --accept-data-loss
else
    echo "❌ Cannot test database connection - DATABASE_URL not set"
fi

echo ""
echo "📚 Next Steps:"
echo "1. This application now uses Supabase instead of Railway PostgreSQL"
echo "2. Ensure DATABASE_URL points to your Supabase instance"
echo "3. Verify Supabase project is active and accessible"
echo "4. Check deployment logs for detailed error messages"
echo "5. Try manual database operations:"
echo "   cd packages/database"
echo "   pnpm db:push"
echo "   pnpm db:seed"