#!/bin/bash

# Railway Deployment Script for Xact Data
echo "🚀 Preparing Xact Data for Railway deployment..."

# Check if we're in Railway environment
if [ -z "$RAILWAY_ENVIRONMENT" ]; then
    echo "❌ Not running in Railway environment. This script is for Railway deployment only."
    exit 1
fi

echo "📦 Installing dependencies..."
pnpm install --frozen-lockfile

echo "🔨 Building shared packages..."
pnpm --filter @xact-data/shared build

echo "🗄️ Setting up database..."
# Generate Prisma client
pnpm --filter @xact-data/database db:generate

echo "🔨 Building database package..."
pnpm --filter @xact-data/database build

echo "🤖 Building AI wrapper..."
pnpm --filter @xact-data/ai-wrapper build

echo "🔨 Building API..."
pnpm --filter api build

echo "🗄️ Running database migrations..."
# Push database schema (creates tables if they don't exist)
pnpm --filter @xact-data/database db:push --accept-data-loss

echo "🌱 Seeding database (if needed)..."
# Only seed if database is empty
pnpm --filter @xact-data/database db:seed || echo "Seeding skipped or failed (database may already be populated)"

echo "✅ Railway deployment preparation complete!"
echo "🚀 Starting API server..."