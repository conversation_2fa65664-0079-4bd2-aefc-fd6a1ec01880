#!/bin/bash

echo "🔍 Railway Database Connection Debugger"
echo "========================================"

echo ""
echo "📊 Environment Information:"
echo "NODE_ENV: ${NODE_ENV:-'not set'}"
echo "PORT: ${PORT:-'not set'}"

echo ""
echo "🗄️ Database Configuration:"
if [ -z "$DATABASE_URL" ]; then
    echo "❌ DATABASE_URL: NOT SET"
    echo ""
    echo "🚨 CRITICAL ISSUE DETECTED:"
    echo "The DATABASE_URL environment variable is not set."
    echo ""
    echo "💡 SOLUTION STEPS:"
    echo "1. Go to your Railway project dashboard"
    echo "2. Ensure you have a PostgreSQL service added"
    echo "3. In your app service, go to Variables tab"
    echo "4. Add: DATABASE_URL = \${{Postgres.DATABASE_URL}}"
    echo "   (Replace 'Postgres' with your actual PostgreSQL service name)"
    echo ""
    echo "📚 Service Name Examples:"
    echo "   - If service is named 'database': \${{database.DATABASE_URL}}"
    echo "   - If service is named 'postgres': \${{postgres.DATABASE_URL}}"
    echo "   - If service is named 'Postgres': \${{Postgres.DATABASE_URL}}"
    echo ""
    exit 1
else
    echo "✅ DATABASE_URL: configured"
    # Show DATABASE_URL format without exposing credentials
    echo "   Format: $(echo $DATABASE_URL | sed -E 's/(postgresql:\/\/)[^:]+:[^@]+@/\1[user]:[password]@/')"
fi

echo ""
echo "🔗 Testing Database Connection:"

# Test if we can connect to the database
if command -v node >/dev/null 2>&1; then
    if [ -f "test-db-connection.js" ]; then
        echo "Running connection test..."
        node test-db-connection.js
    else
        echo "⚠️ test-db-connection.js not found, skipping connection test"
    fi
else
    echo "⚠️ Node.js not available, skipping connection test"
fi

echo ""
echo "🏗️ Railway Service Requirements:"
echo "1. PostgreSQL service must be running"
echo "2. App service must reference PostgreSQL service in variables"
echo "3. Both services must be in the same Railway project"

echo ""
echo "🔧 If issues persist:"
echo "1. Check Railway dashboard for service status"
echo "2. Verify PostgreSQL service is running (green status)"
echo "3. Check service logs for additional error details"
echo "4. Ensure services are properly linked via environment variables"